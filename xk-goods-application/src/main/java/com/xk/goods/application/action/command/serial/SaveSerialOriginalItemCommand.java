package com.xk.goods.application.action.command.serial;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import com.xk.goods.application.dto.serial.SerialGroupOriginalItemTemplate;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.interfaces.dto.req.serial.SerialGroupTeamReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SaveSerialOriginalItemCommand extends AbstractActionCommand {

    List<SaveSerialOriginalItemDto> itemDtoList;

}
