package com.xk.goods.application.handler.command.serial;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.myco.mydata.domain.enums.commons.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.application.action.command.serial.SaveSerialOriginalItemCommand;
import com.xk.goods.application.action.query.business.BusinessResBusinessQuery;
import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;
import com.xk.goods.domain.model.serialitem.entity.SerialItemEntity;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.serial.SerialItemRootRepository;
import com.xk.goods.domain.repository.team.TeamMemberRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Optimized handler for saving serial original items with batch processing capabilities
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialOriginalItemHandler
        implements IActionCommandHandler<SaveSerialOriginalItemCommand, Void> {

    // Constants for batch processing
    private static final int DEFAULT_BATCH_SIZE = 1000;

    private final SerialItemRootRepository serialItemRootRepository;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final TeamMemberRootQueryRepository teamMemberRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<SaveSerialOriginalItemCommand> mono) {
        return mono.flatMap(command -> {
            if (CollectionUtils.isEmpty(command.getItemDtoList())) {
                log.info("No items to process, skipping execution");
                return Mono.empty();
            }

            log.info("Processing {} serial original items", command.getItemDtoList().size());

            // Use functional approach for batch processing
            return processBatchItems(command.getItemDtoList())
                .doOnSuccess(unused -> log.info("Successfully processed all serial original items"))
                .doOnError(error -> log.error("Failed to process serial original items", error));
        });
    }

    /**
     * Process items in batches using functional programming approach
     */
    public Mono<Void> processBatchItems(List<SaveSerialOriginalItemDto> itemDtoList) {
        // Function to group items by team member for efficient processing
        Function<List<SaveSerialOriginalItemDto>, Map<String, List<SaveSerialOriginalItemDto>>> groupByTeamMember =
            items -> items.stream()
                .collect(Collectors.groupingBy(item ->
                    String.format("%s|%s|%d",
                        item.getMemberCnName(),
                        item.getMemberEnName(),
                        item.getTeamType())));

        // Function to process each group
        Function<Map.Entry<String, List<SaveSerialOriginalItemDto>>, Mono<Void>> processGroup =
            entry -> processItemGroup(entry.getKey(), entry.getValue());

        Map<String, List<SaveSerialOriginalItemDto>> groupedItems = groupByTeamMember.apply(itemDtoList);

        return Flux.fromIterable(groupedItems.entrySet())
            .flatMap(processGroup)
            .then();
    }
    /**
     * Process a group of items that share the same team member
     */
    public Mono<Void> processItemGroup(String groupKey, List<SaveSerialOriginalItemDto> items) {
        if (items.isEmpty()) {
            return Mono.empty();
        }

        SaveSerialOriginalItemDto firstItem = items.get(0);

        // Function to find team member
        Function<SaveSerialOriginalItemDto, Mono<TeamMemberEntity>> findTeamMember = item -> {
            Pagination pagination = new Pagination();
            TeamMemberEntity memberEntity = new TeamMemberEntity();
            memberEntity.setMemberType(item.getTeamType());
            memberEntity.setMemberEnName(item.getMemberEnName());
            memberEntity.setMemberCnName(item.getMemberCnName());
            pagination.setCriteria(memberEntity);

            return teamMemberRootQueryRepository.searchTeamMemberInCondition(pagination)
                .next()
                .defaultIfEmpty(new TeamMemberEntity());
        };

        return findTeamMember.apply(firstItem)
            .flatMap(teamMember -> processItemsWithTeamMember(items, teamMember))
            .doOnSuccess(unused -> log.debug("Processed group: {} with {} items", groupKey, items.size()));
    }

    /**
     * Process items with known team member information
     */
    public Mono<Void> processItemsWithTeamMember(List<SaveSerialOriginalItemDto> items, TeamMemberEntity teamMember) {
        // Function to get member resources
        Function<Long, Mono<Map<String, String>>> getMemberResources = memberId -> {
            if (memberId == null) {
                return Mono.just(Map.of());
            }

            BusinessResBusinessQuery resQuery = BusinessResBusinessQuery.builder()
                .businessId(memberId)
                .businessGroupTypeEnum(BusinessGroupTypeEnum.MEMBER)
                .build();

            return queryManyDispatcher
                .executeQuery(Mono.just(resQuery), BusinessResBusinessQuery.class, BusinessResDto.class)
                .collectList()
                .map(this::extractResourceAddresses);
        };

        return getMemberResources.apply(teamMember.getTeamMemberId())
            .flatMap(resources -> batchSaveItems(items, teamMember, resources));
    }

    /**
     * Extract resource addresses from business resources
     */
    public Map<String, String> extractResourceAddresses(List<BusinessResDto> resources) {
        Function<Integer, String> findResourceAddr = resType ->
            resources.stream()
                .filter(res -> resType.equals(res.getBusinessResType()))
                .findFirst()
                .map(BusinessResDto::getAddr)
                .orElse(null);

        return Map.of(
            "memberPicAddr", findResourceAddr.apply(BusinessResTypeEnum.MEMBER_PICTURE.getCode()),
            "memberAvatarAddr", findResourceAddr.apply(BusinessResTypeEnum.MEMBER_AVATAR.getCode())
        );
    }

    /**
     * Batch save items with optimized database operations
     */
    public Mono<Void> batchSaveItems(List<SaveSerialOriginalItemDto> items,
                                     TeamMemberEntity teamMember,
                                     Map<String, String> resources) {

        // Function to create SerialItemRoot from DTO
        Function<SaveSerialOriginalItemDto, SerialItemRoot> createSerialItemRoot = item -> {
            SerialOriginalItemEntity originalEntity = converter.convert(item, SerialOriginalItemEntity.class);

            // Set team member and resource information
            originalEntity.setTeamMemberId(teamMember.getTeamMemberId());
            originalEntity.setMemberPicAddr(resources.get("memberPicAddr"));
            originalEntity.setMemberAvatarAddr(resources.get("memberAvatarAddr"));
            originalEntity.setUpdateId(item.getUserId());
            originalEntity.setCreateId(item.getUserId());
            originalEntity.setSeriesCategoryId(item.getSeriesCategoryId());

            SerialItemEntity serialItemEntity = SerialItemEntity.builder()
                .serialItemId(item.getSerialItemId())
                .serialGroupId(item.getSerialGroupId())
                .serialItemType(SerialItemTypeEnum.ORIGINAL.getCode())
                .status(StatusEnum.ENABLE.getValue())
                .updateId(item.getUserId())
                .createId(item.getUserId())
                .build();

            SerialItemIdentifier identifier = SerialItemIdentifier.builder()
                .serialItemId(item.getSerialItemId())
                .build();

            return SerialItemRoot.builder()
                .identifier(identifier)
                .serialItemEntity(serialItemEntity)
                .serialOriginalItemEntity(originalEntity)
                .build();
        };

        // Process items in batches for better performance
        return Flux.fromIterable(items)
            .map(createSerialItemRoot)
            .buffer(DEFAULT_BATCH_SIZE)
            .flatMap(this::saveBatch)
            .then()
            .doOnSuccess(unused -> log.info("Successfully saved {} items", items.size()));
    }

    /**
     * Save a batch of SerialItemRoot objects using optimized batch operations
     */
    public Mono<Void> saveBatch(List<SerialItemRoot> batch) {
        return serialItemRootRepository.batchSave(batch)
            .doOnSuccess(unused -> log.debug("Batch saved {} items", batch.size()))
            .doOnError(error -> log.error("Failed to batch save {} items", batch.size(), error));
    }
}
