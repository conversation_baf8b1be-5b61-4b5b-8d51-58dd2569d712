package com.xk.goods.application.handler.command.serial;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.xk.goods.application.action.query.business.BusinessResBusinessQueryMany;
import com.xk.goods.application.dto.serial.SaveSerialOriginalItemDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.application.action.command.serial.SaveSerialOriginalItemCommand;
import com.xk.goods.application.action.query.business.BusinessResBusinessQuery;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.domain.model.serialitem.SerialItemRoot;
import com.xk.goods.domain.model.serialitem.entity.SerialItemEntity;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;
import com.xk.goods.domain.model.team.entity.TeamMemberEntity;
import com.xk.goods.domain.repository.serial.SerialItemRootRepository;
import com.xk.goods.domain.repository.team.TeamMemberRootQueryRepository;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.serial.SerialItemTypeEnum;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaveSerialOriginalItemHandler
        implements IActionCommandHandler<SaveSerialOriginalItemCommand, Void> {

    private final SerialItemRootRepository serialItemRootRepository;
    private final ActionQueryManyDispatcher<IActionQueryMany> queryManyDispatcher;
    private final TeamMemberRootQueryRepository teamMemberRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<SaveSerialOriginalItemCommand> mono) {
        return mono.flatMap(dto -> {
            if (CollectionUtils.isEmpty(dto.getItemDtoList())){
                return Mono.empty();
            }
            return teamMemberRootQueryRepository.searchAll().collectList().flatMap(teamMemberEntities -> {
                Map<String, List<TeamMemberEntity>> teamMemberMap = teamMemberEntities.stream().collect(Collectors.groupingBy(teamMember -> String.format("%s%s%s", teamMember.getMemberEnName(), teamMember.getMemberCnName(), teamMember.getMemberType())));
                ArrayList<SerialOriginalItemEntity> itemEntityList = new ArrayList<>();
                ArrayList<SerialOriginalItemEntity> itemEntityAllList = new ArrayList<>();
                for (SaveSerialOriginalItemDto command : dto.getItemDtoList()) {
                    TeamMemberEntity teamMemberEntity = teamMemberMap.getOrDefault(String.format("%s%s%s", command.getMemberEnName(), command.getMemberCnName(), command.getTeamType()), List.of(new TeamMemberEntity())).getLast();
                    SerialOriginalItemEntity itemEntity = converter.convert(command, SerialOriginalItemEntity.class);
                    itemEntity.setTeamMemberId(teamMemberEntity.getTeamMemberId());

                    // 设置默认值
                    itemEntity.setUpdateId(command.getUserId());
                    itemEntity.setCreateId(command.getUserId());
                    itemEntity.setSeriesCategoryId(command.getSeriesCategoryId());

                    if (teamMemberEntity.getTeamMemberId() != null) {
                        itemEntityList.add(itemEntity);
                    } else {
                        itemEntityAllList.add(itemEntity);
                    }
                }
                Set<Long> teamMemberIdList = itemEntityList.stream().map(SerialOriginalItemEntity::getTeamMemberId).collect(Collectors.toSet());
                BusinessResBusinessQueryMany resQuery = BusinessResBusinessQueryMany.builder()
                        .businessIds(teamMemberIdList)
                        .businessGroupTypeEnum(BusinessGroupTypeEnum.MEMBER).build();
                return queryManyDispatcher.executeQuery(Mono.just(resQuery), BusinessResBusinessQueryMany.class, BusinessResDto.class).collectList()
                        .flatMap(businessResDtos -> {

                            return Mono.empty();
                });

            });
        });


        return mono.flatMap(command -> {
            Pagination pagination = new Pagination();
            TeamMemberEntity memberEntity = new TeamMemberEntity();
            memberEntity.setMemberType(command.getTeamType());
            memberEntity.setMemberEnName(command.getMemberEnName());
            memberEntity.setMemberCnName(command.getMemberCnName());
            pagination.setCriteria(memberEntity);
            return teamMemberRootQueryRepository.searchTeamMemberInCondition(pagination).next()
                    .defaultIfEmpty(new TeamMemberEntity()).flatMap(teamMemberEntity -> {
                        SerialOriginalItemEntity itemEntity =
                                converter.convert(command, SerialOriginalItemEntity.class);
                        itemEntity.setTeamMemberId(teamMemberEntity.getTeamMemberId());

                        // 查询球队的资源信息并设置到实体中
                        Mono<Void> setResourcesMono;
                        if (teamMemberEntity.getTeamMemberId() != null) {
                            BusinessResBusinessQuery resQuery = BusinessResBusinessQuery.builder()
                                    .businessId(teamMemberEntity.getTeamMemberId())
                                    .businessGroupTypeEnum(BusinessGroupTypeEnum.MEMBER).build();
                            setResourcesMono = queryManyDispatcher
                                    .executeQuery(Mono.just(resQuery),
                                            BusinessResBusinessQuery.class, BusinessResDto.class)
                                    .collectList()
                                    .doOnNext(resources -> {
                                        // 设置球员图片
                                        List<BusinessResDto> memberPicList = resources.stream()
                                                .filter(tmp -> BusinessResTypeEnum.MEMBER_PICTURE.getCode()
                                                        .equals(tmp.getBusinessResType()))
                                                .toList();
                                        if (!memberPicList.isEmpty()) {
                                            itemEntity.setMemberPicAddr(memberPicList.getFirst().getAddr());
                                            log.debug("Set member picture: {}", memberPicList.getFirst().getAddr());
                                        }

                                        // 设置球员头像
                                        List<BusinessResDto> memberAvatarList = resources.stream()
                                                .filter(tmp -> BusinessResTypeEnum.MEMBER_AVATAR.getCode()
                                                        .equals(tmp.getBusinessResType()))
                                                .toList();
                                        if (!memberAvatarList.isEmpty()) {
                                            itemEntity.setMemberAvatarAddr(memberAvatarList.getFirst().getAddr());
                                            log.debug("Set member avatar: {}", memberAvatarList.getFirst().getAddr());
                                        }
                                    })
                                    .then(); // 转换为 Mono<Void>
                        } else {
                            // 如果没有 teamMemberId，直接返回完成的 Mono
                            setResourcesMono = Mono.empty();
                            log.debug("No teamMemberId found, skipping resource query");
                        }

                        // 设置其他必要字段并执行保存
                        return setResourcesMono.then(Mono.fromRunnable(() -> {
                            itemEntity.setUpdateId(command.getUserId());
                            itemEntity.setCreateId(command.getUserId());
                            itemEntity.setSeriesCategoryId(command.getSeriesCategoryId());
                        })).then(Mono.defer(() -> {
                            SerialItemEntity serialItemEntity = SerialItemEntity.builder()
                                    .serialItemId(command.getSerialItemId())
                                    .serialGroupId(command.getSerialGroupId())
                                    .serialItemType(SerialItemTypeEnum.ORIGINAL.getCode())
                                    .status(0)
                                    .updateId(command.getUserId())
                                    .createId(command.getUserId())
                                    .build();
                            SerialItemIdentifier identifier = SerialItemIdentifier.builder()
                                    .serialItemId(command.getSerialItemId()).build();
                            log.debug("Saving SerialOriginalItem with ID: {}, member: {}",
                                    command.getSerialItemId(), command.getMemberCnName());
                            return serialItemRootRepository.save(SerialItemRoot.builder()
                                    .identifier(identifier).serialItemEntity(serialItemEntity).serialOriginalItemEntity(itemEntity).build());
                        }));
                    });
        });
    }
}
