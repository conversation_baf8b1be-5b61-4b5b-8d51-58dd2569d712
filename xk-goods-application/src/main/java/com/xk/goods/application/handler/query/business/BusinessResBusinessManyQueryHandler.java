package com.xk.goods.application.handler.query.business;

import java.util.function.Function;

import com.xk.goods.application.action.query.business.BusinessResBusinessQueryMany;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.goods.application.action.query.business.BusinessResBusinessQuery;
import com.xk.goods.domain.model.business.entity.BusinessResEntity;
import com.xk.goods.domain.repository.business.BusinessResRootQueryRepository;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class BusinessResBusinessManyQueryHandler
        implements IActionQueryManyHandler<BusinessResBusinessQueryMany, BusinessResDto> {

    private final BusinessResRootQueryRepository queryRepository;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    @Override
    public Flux<BusinessResDto> execute(Mono<BusinessResBusinessQueryMany> mono) {
        return mono.flatMapMany(query -> {
            Flux<BusinessResEntity> findByBusiness = queryRepository
                    .searchByBusinessGroup(
                            BusinessResEntity.builder().businessId(query.getBusinessIds())
                                    .businessGroupType(query.getBusinessGroupTypeEnum()).build())
                    .filter(v -> CollectionUtils.isEmpty(query.getBusinessResTypeList())
                            || query.getBusinessResTypeList().contains(v.getBusinessResType()));

            Function<BusinessResEntity, Mono<BusinessResDto>> queryResource =
                    entity -> sysResourceRootQueryRepository
                            .findById(IntegerIdentifier.builder().id(entity.getResId()).build())
                            .map(resource -> BusinessResDto.builder().resId(resource.getResId())
                                    .addr(resource.getAddr())
                                    .businessResType(entity.getBusinessResType().getCode())
                                    .build());

            return findByBusiness.flatMap(queryResource);
        });
    }
}
