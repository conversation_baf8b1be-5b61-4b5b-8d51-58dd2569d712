package com.xk.goods.application.action.query.business;

import java.util.List;
import java.util.Set;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessResBusinessQueryMany implements IActionQueryMany {

    /**
     * 需要过滤的映射业务类型，为空查询所有
     */
    private List<BusinessResTypeEnum> businessResTypeList;

    /**
     * 分组类型，不能为空
     */
    private BusinessGroupTypeEnum businessGroupTypeEnum;

    /**
     * 业务id，不能为空
     */
    private Set<Long> businessIds;
}
