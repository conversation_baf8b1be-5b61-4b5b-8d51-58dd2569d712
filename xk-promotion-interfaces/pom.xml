<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.promotion</groupId>
        <artifactId>xk-promotion</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>xk-promotion-interfaces</artifactId>
    <packaging>jar</packaging>
    <name>xk-promotion-interfaces</name>
    <description>xk-promotion-interfaces</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.promotion.interfaces.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.promotion.interfaces.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.promotion.interfaces.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>PromotionInterfacesConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.promotion</groupId>
            <artifactId>xk-promotion-domain-enum</artifactId>
        </dependency>
    </dependencies>


</project>
