package com.xk.promotion.interfaces.api.query.coupon;

import com.xk.promotion.interfaces.dto.req.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryUserService;

import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/promotion/coupon/query/app")
public interface CouponQueryUserDocService extends CouponQueryUserService {

    /**
     * 用户：查询商家或者运营平台优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponCenter")
    Mono<Pagination> searchCouponCenter(@RequestBody Mono<CouponPlatformReqDto> mono);

    /**
     * 用户：查询商家优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCorpCoupon")
    Mono<Pagination> searchCorpCoupon(@RequestBody Mono<CouponCorpIdReqDto> mono);

    /**
     * 用户：查询商家优惠券(商户)
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/corp/searchCorpCoupon")
    Mono<Pagination> corpSearchCorpCoupon(@RequestBody Mono<CouponCorpIdReqDto> mono);

    /**
     * 用户：查询榜单优惠券（商户）
     * @param mono mono
     * @return Mono<Pagination>
     */
    @Override
    @PostMapping("/corp/searchRankingCoupon")
    Mono<Pagination> corpSearchRankingCoupon(@RequestBody Mono<RankingCouponReqDto> mono);

    /**
     * 用户查询商品优惠券
     * @param mono mono mono mono
     * @return
     */
    @Override
    @PostMapping("/searchGoodsCoupon")
    Mono<Pagination> searchGoodsCoupon(@RequestBody Mono<CouponGoodsReqDto> mono);

    /**
     * 用户：查询自己的已领取优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchReceived")
    Mono<Pagination> searchReceived(@RequestBody Mono<CouponReceivedReqDto> mono);

    /**
     * 用户：查询可用优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchUsable")
    Mono<Pagination> searchUsable(@RequestBody Mono<CouponUsableReqDto> mono);

    /**
     * 用户：根据兑换码查询优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponByCode")
    Mono<CouponDetailRspDto> searchCouponByCode(@RequestBody Mono<CouponExchangeReqDto> mono);

    /**
     * 用户：筛选卡社
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCorps")
    Mono<Pagination> searchCorps(@RequestBody Mono<CouponReceivedReqDto> mono);
}
