package com.xk.promotion.interfaces.api.query.coupon;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.promotion.interfaces.dto.req.*;
import com.xk.promotion.interfaces.dto.rsp.CouponCommonCodeRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRuleRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponUniqueCodeRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryManagerService;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/promotion/coupon/query/manager")
public interface CouponQueryManagerDocService extends CouponQueryManagerService {

    /**
     * 运营：查询运营优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/search")
    Mono<Pagination> search(@RequestBody Mono<CouponPagerReqDto> mono);

    /**
     * 运营：查询商户优惠券领取记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchReceived")
    Mono<Pagination> searchReceived(@RequestBody Mono<CouponReceivedReqDto> mono);

    /**
     * 运营：公开兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponCode")
    Mono<CouponUniqueCodeRspDto> searchCouponCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：优惠券详情
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/detail")
    Mono<CouponDetailRspDto> detail(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询优惠券配置规则
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchRule")
    Mono<CouponRuleRspDto> searchRule(@RequestBody Mono<RequireSessionDto> mono);

    /**
     * 运营：查询优惠券使用规则说明
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchInstruction")
    Mono<CouponRuleRspDto> searchInstruction(@RequestBody Mono<RequireSessionDto> mono);

    /**
     * 运营：查询优惠券指定商家
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponCorps")
    Mono<Pagination> searchCouponCorps(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询优惠券指定商品
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponGoods")
    Mono<Pagination> searchCouponGoods(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询优惠券记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponReceived")
    Mono<Pagination> searchCouponReceived(@RequestBody Mono<CouponAllReceivedReqDto> mono);

    /**
     * 运营：领券中心查询 申请列表
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchApply")
    Mono<Pagination> searchApply(@RequestBody Mono<CouponApplyReqDto> mono);

    /**
     * 运营：查询公共兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCommonCode")
    Mono<CouponCommonCodeRspDto> searchCommonCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询唯一兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchUniqueCode")
    Mono<CouponUniqueCodeRspDto> searchUniqueCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询唯一兑换码(分页)
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchUniqueCodePager")
    Mono<Pagination> searchUniqueCodePager(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：查询唯一兑换码剩余生成数量
     * @param mono mono
     * @return Mono<Integer>
     */
    @Override
    @PostMapping("/searchRemainNum")
    Mono<Long> searchRemainNum(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：领券中心查询
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostMapping("/searchCouponCenter")
    Mono<Pagination> searchCouponCenter(@RequestBody Mono<CouponPlatformReqDto> mono);

    /**
     * 运营：添加平台券列表
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostMapping("/searchAddPlatformCoupon")
    Mono<Pagination> searchAddPlatformCoupon(@RequestBody Mono<SearchCouponReqDto> mono);
}
