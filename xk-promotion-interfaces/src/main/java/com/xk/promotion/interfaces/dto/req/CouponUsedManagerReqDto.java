package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponUsedManagerReqDto extends RequireSessionDtoPager {
    /**
     * 优惠券ID
     */
    @NotNull
    private Integer couponId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private Long goodsName;
}