package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponManagerCorpReqDto extends RequireSessionDto {

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    @NotNull
    private Integer ownerType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 兑换码
     */
    private String couponCode;

    /**
     * 是否可以领取：0不可领取；1可以领取；
     */
    private Integer receiveStatus;
}
