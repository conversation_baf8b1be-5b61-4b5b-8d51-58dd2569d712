package com.xk.promotion.interfaces.query.coupon;

import com.xk.promotion.interfaces.dto.req.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/query/app")
public interface CouponQueryUserService {

    /**
     * 用户：查询商家或者运营平台优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCouponCenter")
    Mono<Pagination> searchCouponCenter(@RequestBody Mono<CouponPlatformReqDto> mono);

    /**
     * 用户：查询商家优惠券
     * 功能：不仅展示商家的优惠券，还需要展示运营配置的展示在商家的优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCorpCoupon")
    Mono<Pagination> searchCorpCoupon(@RequestBody Mono<CouponCorpIdReqDto> mono);

    /**
     * 用户：查询商家优惠券(商户)
     * 功能：不仅展示商家的优惠券，还需要展示运营配置的展示在商家的优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/corp/searchCorpCoupon")
    Mono<Pagination> corpSearchCorpCoupon(@RequestBody Mono<CouponCorpIdReqDto> mono);

    /**
     * 用户：查询榜单优惠券（商户）
     * @param mono mono
     * @return Mono<Pagination>
     */
    @PostExchange("/corp/searchRankingCoupon")
    Mono<Pagination> corpSearchRankingCoupon(@RequestBody Mono<RankingCouponReqDto> mono);

    /**
     * 用户：查询商家优惠券
     * 功能：不仅展示商家的优惠券，还需要展示运营配置的展示在商家的优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchGoodsCoupon")
    Mono<Pagination> searchGoodsCoupon(@RequestBody Mono<CouponGoodsReqDto> mono);

    /**
     * 用户：查询自己的已领取优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchReceived")
    Mono<Pagination> searchReceived(@RequestBody Mono<CouponReceivedReqDto> mono);

    /**
     * 用户：查询可用优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchUsable")
    Mono<Pagination> searchUsable(@RequestBody Mono<CouponUsableReqDto> mono);

    /**
     * 用户：根据兑换码查询优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCouponByCode")
    Mono<CouponDetailRspDto> searchCouponByCode(@RequestBody Mono<CouponExchangeReqDto> mono);

    /**
     * 用户：筛选卡社
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCorps")
    Mono<Pagination> searchCorps(@RequestBody Mono<CouponReceivedReqDto> mono);
}
