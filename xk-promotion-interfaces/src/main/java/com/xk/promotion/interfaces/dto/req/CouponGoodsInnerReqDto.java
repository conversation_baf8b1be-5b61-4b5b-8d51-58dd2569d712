package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponGoodsInnerReqDto extends AbstractSession {
    /**
     * 优惠券领取Id
     */
    private Long couponUserId;

    /**
     * 优惠券Id
     */
    private Long couponId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long goodsId;
}
