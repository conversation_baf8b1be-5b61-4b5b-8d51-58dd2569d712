package com.xk.promotion.interfaces.dto.req;

import java.util.Date;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponPagerReqDto extends RequireSessionDtoPager {
    /**
     * 优惠券所属平台（1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 优惠券状态（0：可用，1：不可用）
     */
    private Integer status;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 创建开始时间
     */
    private Date startTime;

    /**
     * 创建结束时间
     */
    private Date endTime;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 库存
     */
    private Integer stockNum;
}
