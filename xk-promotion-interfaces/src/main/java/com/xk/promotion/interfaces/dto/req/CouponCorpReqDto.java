package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponCorpReqDto extends RequireSessionDto {


    /**
     * 优惠券状态：0禁用；1可用
     */
    private Integer status;

    /**
     * 优惠券领取状态：0不可领取；1可领取；【禁用状态下展示禁用，因此这个筛选项下status需要是1】
     */
    private Integer receivedStatus;

}
