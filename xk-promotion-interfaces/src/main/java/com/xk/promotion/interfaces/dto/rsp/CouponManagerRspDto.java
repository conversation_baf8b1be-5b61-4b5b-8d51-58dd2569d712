package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponManagerRspDto {
    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long amount;

    /**
     * 是否有门槛（0：否，1：是）
     */
    private Integer isThreshold;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    private Integer ownerType;

        /**
     * 优惠券使用范围类型（1店铺通用；2指定商品；3指定类型）
     */
    private Integer scopeType;

        /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

        /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 已领取数量
     */
    private Integer receivedNum;

    /**
     * 已使用数量
     */
    private Integer usedNum;

    /**
     * 每人领取上限
     */
    private Integer personNum;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 领取后有效期：单位小时
     */
    private Integer periodTime;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 优惠券兑换码
     */
    private String couponCode;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态（0：禁用，1：正常）
     */
    private Integer status;
}
