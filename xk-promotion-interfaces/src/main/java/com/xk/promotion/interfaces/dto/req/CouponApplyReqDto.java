package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponApplyReqDto extends RequireSessionDtoPager {
    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券;11商家通用券；12商家指定券）
     */
    private Integer scopeType;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    /**
     * 审核状态（1：待审核，2：审核通过；3审核拒绝）
     */
    private Integer auditStatus;
}
