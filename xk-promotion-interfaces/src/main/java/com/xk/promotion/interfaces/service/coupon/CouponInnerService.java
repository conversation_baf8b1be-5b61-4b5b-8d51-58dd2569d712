package com.xk.promotion.interfaces.service.coupon;

import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/inner")
public interface CouponInnerService {


    /**
     * 内部：修改优惠券使用状态
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateUsedStatus")
    Mono<Boolean> updateUsedStatus(@RequestBody Mono<UpdateUsedStatusReqDto> mono);

}
