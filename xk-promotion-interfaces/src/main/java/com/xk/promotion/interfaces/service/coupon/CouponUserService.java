package com.xk.promotion.interfaces.service.coupon;

import com.xk.promotion.interfaces.dto.req.CouponExchangeReqDto;
import com.xk.promotion.interfaces.dto.req.CouponIdReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/app")
public interface CouponUserService {
    /**
     * 兑换码兑换优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/exchange")
    Mono<Void> saveExchange(@RequestBody Mono<CouponExchangeReqDto> mono);

    /**
     * 领取优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/receive")
    Mono<Void> saveReceive(@RequestBody Mono<CouponIdReqDto> mono);

}
