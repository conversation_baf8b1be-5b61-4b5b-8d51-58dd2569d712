package com.xk.promotion.interfaces.service.coupon;

import com.xk.promotion.interfaces.dto.req.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/corp")
public interface CouponCorpService {
    /**
     * 商家：禁用优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateForbidden")
    Mono<Void> updateForbidden(@RequestBody Mono<CouponForbiddenReqDto> mono);

    /**
     * 商家：禁用兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateCodeForbidden")
    Mono<Void> updateCodeForbidden(@RequestBody Mono<CouponCodeForbiddenReqDto> mono);

    /**
     * 商家：删除优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/delete")
    Mono<Void> delete(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商家：生成兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/createCode")
    Mono<Void> createCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商家：创建商家优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/save")
    Mono<Integer> save(@RequestBody Mono<CouponSaveReqDto> mono);

    /**
     * 商家：更新是否显示在店铺主页
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateShowHomePage")
    Mono<Void> updateShowHomePage(@RequestBody Mono<CouponShowReqDto> mono);

    /**
     * 商家：手动下发优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/addToUser")
    Mono<Void> addToUser(@RequestBody Mono<CouponSendReqDto> mono);

    /**
     * 商家：创建申请
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/createApply")
    Mono<Void> createApply(@RequestBody Mono<CouponCreateApplyReqDto> mono);

    /**
     * 运营：更新优惠券规则
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateRule")
    Mono<Void> updateRule(@RequestBody Mono<CouponRuleReqDto> mono);

}
