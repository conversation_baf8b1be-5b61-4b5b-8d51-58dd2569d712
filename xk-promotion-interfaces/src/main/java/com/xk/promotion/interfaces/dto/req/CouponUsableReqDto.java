package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponUsableReqDto extends RequireSessionDtoPager {

    /**
     * 金额
     */
    private Long amount;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商品id
     */
    private Long goodsId;
}
