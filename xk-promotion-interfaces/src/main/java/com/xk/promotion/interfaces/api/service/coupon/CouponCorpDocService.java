package com.xk.promotion.interfaces.api.service.coupon;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.promotion.interfaces.dto.req.*;
import com.xk.promotion.interfaces.service.coupon.CouponCorpService;

import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/promotion/coupon/corp")
public interface CouponCorpDocService extends CouponCorpService {
    /**
     * 商家：禁用优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/updateForbidden")
    Mono<Void> updateForbidden(@RequestBody Mono<CouponForbiddenReqDto> mono);
    /**
     * 商家：禁用兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/updateCodeForbidden")
    Mono<Void> updateCodeForbidden(@RequestBody Mono<CouponCodeForbiddenReqDto> mono);

    /**
     * 商家：删除优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/delete")
    Mono<Void> delete(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商家：生成兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/createCode")
    Mono<Void> createCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商家：创建优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/save")
    Mono<Integer> save(@RequestBody Mono<CouponSaveReqDto> mono);

    /**
     * 商家：更新是否显示在店铺主页
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/updateShowHomePage")
    Mono<Void> updateShowHomePage(@RequestBody Mono<CouponShowReqDto> mono);

    /**
     * 商家：手动下发优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/addToUser")
    Mono<Void> addToUser(@RequestBody Mono<CouponSendReqDto> mono);

    /**
     * 商家：创建申请
     * @param mono mono
     * @return
     */
    @Override
    @PostMapping("/createApply")
    Mono<Void> createApply(@RequestBody Mono<CouponCreateApplyReqDto> mono);

    /**
     * 商家：更新优惠券规则
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/updateRule")
    Mono<Void> updateRule(@RequestBody Mono<CouponRuleReqDto> mono);
}
