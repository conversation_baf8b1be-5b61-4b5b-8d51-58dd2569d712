package com.xk.promotion.interfaces.api.query.coupon;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.promotion.interfaces.dto.req.CouponAllReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRuleRspDto;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.promotion.interfaces.dto.req.CouponIdReqDto;
import com.xk.promotion.interfaces.dto.req.CouponPagerReqDto;
import com.xk.promotion.interfaces.dto.req.CouponReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponCommonCodeRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponUniqueCodeRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryCorpService;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/promotion/coupon/query/corp")
public interface CouponQueryCorpDocService extends CouponQueryCorpService {

    /**
     * 商户：查询优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/search")
    Mono<Pagination> search(@RequestBody Mono<CouponPagerReqDto> mono);

    /**
     * 商户：查询优惠券详情
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/detail")
    Mono<CouponDetailRspDto> detail(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券领取记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchReceived")
    Mono<Pagination> searchReceived(@RequestBody Mono<CouponReceivedReqDto> mono);

    /**
     * 商户：查询公共兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCommonCode")
    Mono<CouponCommonCodeRspDto> searchCommonCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchUniqueCode")
    Mono<CouponUniqueCodeRspDto> searchUniqueCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码(分页)
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchUniqueCodePager")
    Mono<Pagination> searchUniqueCodePager(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码剩余生成数量
     * @param mono mono
     * @return Mono<Integer>
     */
    @Override
    @PostMapping("/searchRemainNum")
    Mono<Long> searchRemainNum(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券指定商家
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponCorps")
    Mono<Pagination> searchCouponCorps(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券指定商品
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponGoods")
    Mono<Pagination> searchCouponGoods(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchCouponReceived")
    Mono<Pagination> searchCouponReceived(@RequestBody Mono<CouponAllReceivedReqDto> mono);

    /**
     * 商户：查询优惠券配置规则
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchRule")
    Mono<CouponRuleRspDto> searchRule(@RequestBody Mono<RequireSessionDto> mono);

    /**
     * 商户：查询优惠券使用规则说明
     *
     * @param mono mono
     * @return 优惠券
     */
    @Override
    @PostMapping("/searchInstruction")
    Mono<CouponRuleRspDto> searchInstruction(@RequestBody Mono<RequireSessionDto> mono);

}
