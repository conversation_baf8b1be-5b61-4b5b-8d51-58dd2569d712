package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponUniqueCodeRspDto {

    /**
     * 兑换码剩余生成数量
     * */
    private Integer remainNum;

    /**
     * 优惠券列表
     * */
    List<CouponCodeDto> couponCodeDtoList;
}
