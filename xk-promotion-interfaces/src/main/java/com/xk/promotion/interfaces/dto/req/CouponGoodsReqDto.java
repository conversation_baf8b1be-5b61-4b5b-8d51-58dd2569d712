package com.xk.promotion.interfaces.dto.req;


import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CouponGoodsReqDto extends RequireSessionDtoPager {

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 商品id
     */
    private Long goodsId;

}
