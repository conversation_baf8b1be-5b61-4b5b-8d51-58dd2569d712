package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.domain.model.action.session.AbstractSession;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponExchangeReqDto extends AbstractSession {

    /**
     * 优惠券兑换码
     */
    @NotNull
    @Size(min = 16, max = 16, message = "请输入正确的16位兑换码")
    private String exchangeCode;
}
