package com.xk.promotion.interfaces.api.service.coupon;

import com.xk.promotion.interfaces.dto.req.CouponExchangeReqDto;
import com.xk.promotion.interfaces.dto.req.CouponIdReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponUserService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/promotion/coupon")
public interface CouponUserDocService extends CouponUserService {
    /**
     * 兑换码兑换优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/app/exchange")
    Mono<Void> saveExchange(@RequestBody Mono<CouponExchangeReqDto> mono);

    /**
     * 领取优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/app/receive")
    Mono<Void> saveReceive(@RequestBody Mono<CouponIdReqDto> mono);

}
