package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponReceivedUserRspDto {

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）,查询全部为空
     */
    private Integer ownerType;

    /**
     * 优惠券状态：1待使用；2快过期；3已过期；
     */
    private Integer couponStatus;

    /**
     * 是否有门槛（0：否，1：是）
     */
    private Integer isThreshold;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;
    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long amount;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 领取后有效期：单位小时
     */
    private Integer periodTime;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 优惠券使用范围类型（1店铺通用；2指定商品；3指定类型）
     */
    private Integer scopeType;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;


}
