package com.xk.promotion.interfaces.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * @author: killer
 **/
public class PromotionInterfacesConfig {

    @Bean
    public WebClient promotionWebClient(WebClient.Builder loadBalancedWebClientBuilder) {
        return loadBalancedWebClientBuilder.baseUrl("http://xkPromotion").build();
    }

    @Bean
    public HttpServiceProxyFactory promotionHttpServiceProxyFactory(WebClient promotionWebClient) {
        WebClientAdapter adapter = WebClientAdapter.create(promotionWebClient);
        return HttpServiceProxyFactory.builderFor(adapter).build();
    }

}
