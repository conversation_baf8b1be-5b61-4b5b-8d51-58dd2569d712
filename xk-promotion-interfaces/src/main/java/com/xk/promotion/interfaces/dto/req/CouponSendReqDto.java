package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponSendReqDto extends RequireSessionDto {
    /**
     * 优惠券ID
     */
    @NotNull
    private Integer couponId;

    /**
     * 发放列表
     */
    @NotNull
    private List<CouponSendDto> couponSendDtoList;
}
