package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponPlatformReqDto extends RequireSessionDtoPager {
    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券;11商家通用券；12商家指定券）
     */
    private Integer scopeType;

    /**
     * 排序方式 1默认排序 2权重排序
     */
    private Integer sortType;
}
