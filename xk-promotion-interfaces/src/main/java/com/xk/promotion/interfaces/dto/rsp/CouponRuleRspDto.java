package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRuleRspDto {

    /**
     * 无门槛优惠券单条金额上限
     */
    private Long commonPerCouponMaxAmount = 0L;

    /**
     * 有门槛优惠券单条金额上限
     */
    private Long thresholdPerCouponMaxAmount = 0L;

    /**
     * 有门槛满减优惠券占比
     */
    private Integer thresholdRatio = 0;

    /**
     * 单日优惠券总条数上限
     */
    private Integer dailyCouponMaxNum = 0;

    /**
     * 单日优惠券总金额上限
     */
    private Long dailyCouponMaxAmount = 0L;

    /**
     * 单条优惠券金额总和
     */
    private Long perCouponMaxAmount = 0L;

    /**
     * 优惠券创建合规性提示
     */
    private String instruction;

    /**
     * 单商品优惠券使用数量上限
     */
    private Integer perGoodsCouponMaxNum = 0;
}
