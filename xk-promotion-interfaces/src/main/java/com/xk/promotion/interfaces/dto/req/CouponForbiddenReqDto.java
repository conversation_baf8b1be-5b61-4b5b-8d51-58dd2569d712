package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponForbiddenReqDto extends RequireSessionDto {
    /**
     * 优惠券Id
     */
    @NotNull
    private Long couponId;

    /**
     * 优惠券状态：0正常；1禁用；
     */
    @NotNull
    private Integer status;
}
