package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponGoodsRspDto {
        /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     * */
    private Integer productType;

    /**
     * 商品状态
     * */
    private Integer goodsStatus;
}
