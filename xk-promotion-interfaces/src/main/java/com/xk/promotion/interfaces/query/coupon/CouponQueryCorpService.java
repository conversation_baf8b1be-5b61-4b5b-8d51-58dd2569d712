package com.xk.promotion.interfaces.query.coupon;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import com.xk.promotion.interfaces.dto.req.CouponAllReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRuleRspDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.promotion.interfaces.dto.req.CouponIdReqDto;
import com.xk.promotion.interfaces.dto.req.CouponPagerReqDto;
import com.xk.promotion.interfaces.dto.req.CouponReceivedReqDto;
import com.xk.promotion.interfaces.dto.rsp.CouponCommonCodeRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponDetailRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponUniqueCodeRspDto;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/query/corp")
public interface CouponQueryCorpService {

    /**
     * 商户：查询优惠券
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/search")
    Mono<Pagination> search(@RequestBody Mono<CouponPagerReqDto> mono);

    /**
     * 商户：查询优惠券详情
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/detail")
    Mono<CouponDetailRspDto> detail(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券领取记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchReceived")
    Mono<Pagination> searchReceived(@RequestBody Mono<CouponReceivedReqDto> mono);

    /**
     * 商户：查询公共兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCommonCode")
    Mono<CouponCommonCodeRspDto> searchCommonCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchUniqueCode")
    Mono<CouponUniqueCodeRspDto> searchUniqueCode(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码（分页）
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchUniqueCodePager")
    Mono<Pagination> searchUniqueCodePager(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询唯一兑换码剩余生成数量
     * @param mono mono
     * @return Mono<Integer>
     */
    @PostExchange("/searchRemainNum")
    Mono<Long> searchRemainNum(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券指定商家
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCouponCorps")
    Mono<Pagination> searchCouponCorps(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券指定商品
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCouponGoods")
    Mono<Pagination> searchCouponGoods(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 商户：查询优惠券记录
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchCouponReceived")
    Mono<Pagination> searchCouponReceived(@RequestBody Mono<CouponAllReceivedReqDto> mono);

    /**
     * 商户：查询优惠券配置规则
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchRule")
    Mono<CouponRuleRspDto> searchRule(@RequestBody Mono<RequireSessionDto> mono);

    /**
     * 商户：查询优惠券配置规则(使用说明)
     *
     * @param mono mono
     * @return 优惠券
     */
    @PostExchange("/searchInstruction")
    Mono<CouponRuleRspDto> searchInstruction(@RequestBody Mono<RequireSessionDto> mono);

}
