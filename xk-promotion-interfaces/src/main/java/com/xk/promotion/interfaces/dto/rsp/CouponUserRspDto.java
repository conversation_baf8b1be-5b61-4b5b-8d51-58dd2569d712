package com.xk.promotion.interfaces.dto.rsp;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponUserRspDto {
    /**
     * 优惠券领取id
     */
    private Long couponUserId;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long discountAmount;

    /**
     * 金额门槛
     */
    private Long thresholdAmount;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户手机号
     * */
    private String mobile;

    /**
     * 领取类型（1手动领取；2兑换码兑换；3平台下发；4商户下发）
     */
    private Integer receivedType;

    /**
     * 兑换码id
     */
    private Long exchangeCodeId;

    /**
     * 是否使用：1未使用；2已使用;3已过期
     */
    private Integer usedStatus;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 优惠券使用时间
     */
    private Date usedTime;

    /**
     * 状态（0：禁用，1：正常）
     */
    private Integer status;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 领取时间状态 1、new：三天内领取；2、快到期：两天内快到期；3、未生效：还未到优惠券开始时间；
     * */
    private Integer receivedTimeStatus;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券;11商家通用券；12商家指定券）
     */
    private Integer scopeType;

    /**
     * 优惠券所属平台 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 优惠券到期类型：1、未生效；2新到券；3快到期；4即将到期
     */
    private Integer timeType;

    /**
     * 查询优惠券过期类型：1、已使用；2已过期；3已失效
     */
    private Integer expiredType;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 商家id
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 商家logo
     */
    private String corpLogo;
}
