package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponCodeForbiddenReqDto extends RequireSessionDto {
    /**
     * 优惠券兑换码Id
     */
    @NotNull
    private Long exchangeCodeId;

    /**
     * 优惠券兑换码状态：0正常；1禁用；
     */
    @NotNull
    private Integer status;
}
