package com.xk.promotion.interfaces.dto.rsp;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponApplyRspDto {

    /**
     * 申请id
     */
    private Long couponApplyId;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券所属平台 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    private Integer couponType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名
     */
    private String corpName;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long discountAmount;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 已领取数量
     */
    private Integer receivedNum;

    /**
     * 库存数量
     */
    private Integer stockNum;

    /**
     * 已使用数量
     */
    private Integer usedNum;

    /**
     * 每人领取上限
     */
    private Integer personNum;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 领取后有效期天数
     */
    private Integer periodNum;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 审核状态（1：待审核，2：审核通过；3审核拒绝）
     */
    private Integer auditStatus;

    /**
     * 状态（0：启用，1：禁用）
     */
    private Integer status;


    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}
