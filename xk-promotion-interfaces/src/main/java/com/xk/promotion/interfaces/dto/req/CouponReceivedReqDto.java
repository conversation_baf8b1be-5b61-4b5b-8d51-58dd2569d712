package com.xk.promotion.interfaces.dto.req;

import java.util.List;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponReceivedReqDto extends RequireSessionDtoPager {

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 领取方式（1手动领取；2兑换码兑换；3平台下发；4商户下发）
     */
    private Integer receivedType;

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）,查询全部为空
     */
    private Integer platformType;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 使用状态：（1未使用; 2已使用；3已过期）
     */
    private List<Integer> usedStatusList;
    private Integer usedStatus;

    /**
     * 是否查询历史优惠券
     * */
    private Integer isHistory;

    /**
     * 优惠券到期类型：1、未生效；2新到券；3快到期；4即将到期
     * */
    private Integer timeType;

    /**
     * 查询优惠券过期类型：1、已使用；2已过期；3已失效
     * */
    private Integer expiredType;
}
