package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponCodeReqDto extends RequireSessionDto {
    /**
     * 优惠券ID
     */
    @NotNull
    private Long couponId;

    /**
     * 兑换码类型：1公共兑换码；2一次性兑换码；
     * */
    @NotNull
    private Integer codeType;
}
