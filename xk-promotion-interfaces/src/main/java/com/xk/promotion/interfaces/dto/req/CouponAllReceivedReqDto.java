package com.xk.promotion.interfaces.dto.req;

import java.util.Date;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CouponAllReceivedReqDto extends RequireSessionDtoPager {
    /**
     * 使用状态：（1未使用; 2已使用；3已过期）
     */
    private Integer usedStatus;

    /**
     * 领取开始时间
     */
    private Date startTime;

    /**
     * 领取结束时间
     */
    private Date endTime;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 领取类型（1手动领取；2兑换码兑换；3平台下发；4商户下发；）
     */
    private Integer receivedType;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    private Integer scopeType;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

}
