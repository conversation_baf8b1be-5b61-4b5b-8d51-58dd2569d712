package com.xk.promotion.interfaces.service.coupon;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.promotion.interfaces.dto.req.*;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/manager")
public interface CouponManagerService {
    /**
     * 运营：禁用优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateForbidden")
    Mono<Void> updateForbidden(@RequestBody Mono<CouponForbiddenReqDto> mono);

    /**
     * 运营：禁用兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateCodeForbidden")
    Mono<Void> updateCodeForbidden(@RequestBody Mono<CouponCodeForbiddenReqDto> mono);

    /**
     * 运营：删除优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/delete")
    Mono<Void> delete(@RequestBody Mono<CouponIdReqDto> mono);

    /**
     * 运营：创建优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/save")
    Mono<Integer> save(@RequestBody Mono<CouponSaveReqDto> mono);

    /**
     * 运营：更新是否显示在店铺主页
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateShowHomePage")
    Mono<Void> updateShowHomePage(@RequestBody Mono<CouponShowReqDto> mono);

    /**
     * 运营：更新是否显示在领券中心
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateShowCenter")
    Mono<Void> updateShowCenter(@RequestBody Mono<CouponShowReqDto> mono);

    /**
     * 运营：手动下发优惠券
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/addToUser")
    Mono<Void> addToUser(@RequestBody Mono<CouponSendReqDto> mono);

    /**
     * 运营：更新优惠券规则
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateRule")
    Mono<Void> updateRule(@RequestBody Mono<CouponRuleReqDto> mono);


    /**
     * 运营：调整权重
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/updateWeight")
    Mono<Void> updateWeight(@RequestBody Mono<UpdateWeightReqDto> mono);

    /**
     * 运营：审核申请
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/auditApply")
    Mono<Void> updateApply(@RequestBody Mono<CouponAuditReqDto> mono);

    /**
     * 运营：生成兑换码
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/createCode")
    Mono<Void> createCode(@RequestBody Mono<CouponIdReqDto> mono);
}
