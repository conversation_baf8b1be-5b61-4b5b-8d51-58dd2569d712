package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponCorpIdReqDto extends RequireSessionDtoPager {

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;
}
