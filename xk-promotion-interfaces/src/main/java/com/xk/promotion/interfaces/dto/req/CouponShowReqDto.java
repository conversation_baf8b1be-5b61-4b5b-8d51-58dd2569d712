package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponShowReqDto extends RequireSessionDto {
    /**
     * 优惠券ID
     */
    @NotNull
    private Integer couponId;

    /**
     * 是否显示（0：否，1：是）
     */
    @NotNull
    private Integer showStatus;
}
