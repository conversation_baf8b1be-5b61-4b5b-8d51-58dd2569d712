package com.xk.promotion.interfaces.query.coupon;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.xk.promotion.interfaces.dto.req.CouponGoodsInnerReqDto;
import com.xk.promotion.interfaces.dto.rsp.CheckCouponGoodsRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRspDto;

import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@HttpExchange("/promotion/coupon/query/inner")
public interface CouponQueryInnerService {
    /**
     * 内部：检查用户是否能使用此优惠券购买商品
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/checkCouponGoods")
    Mono<CheckCouponGoodsRspDto> checkCouponGoods(@RequestBody Mono<CouponGoodsInnerReqDto> mono);

    /**
     * 内部：查询用户优惠券详情
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @PostExchange("/getCouponDetail")
    Mono<CouponRspDto> getCouponDetail(@RequestBody Mono<CouponGoodsInnerReqDto> mono);
}
