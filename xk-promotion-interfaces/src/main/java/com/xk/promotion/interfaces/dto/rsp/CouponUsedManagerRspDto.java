package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponUsedManagerRspDto {

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型：ORIGINAL("原盒", 1), RUBBING("搓卡密", 2), BIAN_FENG("边锋盒子", 3), SPECIAL("福盒", 4),
     */
    private Integer groupType;

    /**
     * 商品类型名称
     * */
    private String groupTypeName;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单实际支付金额
     */
    private Long orderRealPay;

    /**
     * 使用时间
     */
    private Date usedTime;

}
