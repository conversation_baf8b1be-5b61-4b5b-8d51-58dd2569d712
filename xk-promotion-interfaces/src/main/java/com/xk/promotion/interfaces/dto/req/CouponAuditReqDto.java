package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.pager.RequireSessionDtoPager;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponAuditReqDto extends RequireSessionDtoPager {
    /**
     * 申请ID
     */
    private Long couponApplyId;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 申请状态
     */
    private Integer auditStatus;

}
