package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRelateGoodsRspDto {

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品上下架状态 1上架 2下架
     */
    private Integer goodsStatus;

    /**
     * 商家id
     */
    private Long corpId;

    /**
     * 商家名
     */
    private String corpName;

    /**
     * 商家状态
     */
    private Integer corpStatus;

    /**
     * 状态（0：启用，1：禁用）
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}
