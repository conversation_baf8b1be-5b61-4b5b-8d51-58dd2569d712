package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponRelateCorpRspDto {

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 商家id
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 商户状态（1-正常 0-禁用）
     */
    private Integer corpStatus;

    /**
     * 状态（0：启用，1：禁用）
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}
