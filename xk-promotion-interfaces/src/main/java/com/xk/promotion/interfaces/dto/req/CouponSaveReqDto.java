package com.xk.promotion.interfaces.dto.req;

import java.util.Date;
import java.util.List;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponSaveReqDto extends RequireSessionDto {

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    @NotNull
    private Integer couponType;

    /**
     * 优惠券名称
     */
    @NotNull
    @Size(max = 10, message = "优惠券名称不能超过10个字符")
    private String name;

    /**
     * 优惠券金额
     */
    @NotNull
    @Min(value = 0, message = "优惠券金额不能小于0")
    private Long discountAmount;

    /**
     * 门槛金额
     */
    @Min(value = 0, message = "门槛金额不能小于0")
    private Long thresholdAmount;

    /**
     * 总数量
     */
    @NotNull
    @Min(value = 1, message = "总数量不能小于1")
    private Integer totalNum;

    /**
     * 每人领取上限
     */
    @NotNull
    @Min(value = 1, message = "每人领取上限不能小于1")
    private Integer personNum;

    /**
     * 优惠券使用范围类型（1平台通用券；2指定商家券；3指定商品券）
     */
    @NotNull
    private Integer scopeType;

    /**
     * 指定商家列表
     */
    private List<IdAndName> corpIdList;

    /**
     * 指定商品列表
     */
    private List<IdAndName> goodsIdList;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    @NotNull
    private Integer periodType;

    /**
     * 领取后有效期数量：单位天
     */
    private Integer periodNum;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 使用说明
     */
    @NotNull
    @Size(max = 200, message = "规则说明文案不能超过200个字符")
    private String instruction;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

    @Data
    public static class IdAndName{
        private Long id;
        private String name;
    }
}
