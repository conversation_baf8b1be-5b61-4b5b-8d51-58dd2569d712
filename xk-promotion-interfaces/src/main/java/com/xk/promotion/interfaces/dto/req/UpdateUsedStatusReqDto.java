package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateUsedStatusReqDto extends AbstractSession {

    /**
     * 优惠券领取id
     */
    private Long couponUserId;

    /**
     * 否使用：1未使用；2已使用;3已过期
     */
    private Integer usedStatus;
}
