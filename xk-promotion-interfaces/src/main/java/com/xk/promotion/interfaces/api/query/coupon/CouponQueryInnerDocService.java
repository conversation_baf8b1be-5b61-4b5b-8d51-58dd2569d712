package com.xk.promotion.interfaces.api.query.coupon;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.xk.promotion.interfaces.dto.req.CouponGoodsInnerReqDto;
import com.xk.promotion.interfaces.dto.rsp.CheckCouponGoodsRspDto;
import com.xk.promotion.interfaces.dto.rsp.CouponRspDto;
import com.xk.promotion.interfaces.query.coupon.CouponQueryInnerService;

import reactor.core.publisher.Mono;

/**
 * 优惠券内部服务
 */
@Controller
@RequestMapping("/promotion/coupon/query/inner")
public interface CouponQueryInnerDocService extends CouponQueryInnerService {
    /**
     * 内部：检查用户是否能使用此优惠券购买商品
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/checkCouponGoods")
    Mono<CheckCouponGoodsRspDto> checkCouponGoods(@RequestBody Mono<CouponGoodsInnerReqDto> mono);

    /**
     * 内部：查询用户优惠券详情
     *
     * @param mono mono
     * @return Mono<Void>
     */
    @Override
    @PostMapping("/getCouponDetail")
    Mono<CouponRspDto> getCouponDetail(@RequestBody Mono<CouponGoodsInnerReqDto> mono);
}
