package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponRuleReqDto extends RequireSessionDto {
    /**
     * 无门槛优惠券单条金额上限
     */
    @NotNull
    @Min(value = 1, message = "无门槛优惠券单条金额上限必须大于0")
    private Long commonPerCouponMaxAmount;

    /**
     * 有门槛优惠券单条金额上限
     */
    @NotNull
    @Min(value = 1, message = "有门槛优惠券单条金额上限必须大于0")
    private Long thresholdPerCouponMaxAmount;

    /**
     * 有门槛满减优惠券占比
     */
    @NotNull
    @Min(value = 1, message = "有门槛满减优惠券占比必须大于0")
    private Integer thresholdRatio;

    /**
     * 单日优惠券总条数上限
     */
    @NotNull
    @Min(value = 1, message = "单日优惠券总条数上限必须大于0")
    private Integer dailyCouponMaxNum;

    /**
     * 单日优惠券总金额上限
     */
    @NotNull
    @Min(value = 1, message = "单日优惠券总金额上限必须大于0")
    private Long dailyCouponMaxAmount;

    /**
     * 单条优惠券金额总和
     */
    @NotNull
    @Min(value = 1, message = "单条优惠券金额总和必须大于0")
    private Long perCouponMaxAmount;

    /**
     * 优惠券创建合规性提示
     */
    @NotNull
    private String instruction;

    /**
     * 单商品优惠券使用数量上限
     */
    @NotNull
    @Min(value = 1, message = "单商品优惠券使用数量上限必须大于0")
    private Integer perGoodsCouponMaxNum;

}
