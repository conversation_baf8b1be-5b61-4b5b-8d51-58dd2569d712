package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponCodeDto {
    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 兑换码
     */
    private String couponCode;

    /**
     * 领取状态：0未领取；1已领取
     */
    private Integer receivedStatus;

    /**
     * 领取人ID
     */
    private Long receivedUserId;

    /**
     * 手机号
     */
    private String receivedMobile;

    /**
     * 领取人姓名
     */
    private String receivedUserName;

    /**
     * 兑换码状态
     */
    private Integer status;
}
