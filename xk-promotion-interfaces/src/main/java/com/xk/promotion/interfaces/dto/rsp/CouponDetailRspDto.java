package com.xk.promotion.interfaces.dto.rsp;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponDetailRspDto {
    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券金额
     */
    private Long discountAmount;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 商家头像
     */
    private String corpLogo;

    /**
     * 使用说明
     */
    private String instruction;

    /**
     * 每人领取上限
     */
    private Integer personNum;

    /**
     * 优惠券所属平台 （1运营平台；2商家平台）
     */
    private Integer platformType;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    private Integer periodType;

    /**
     * 领取后有效期：单位天
     */
    private Integer periodNum;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 优惠券类型（1无门槛优惠券；2满减优惠券；3折扣优惠券）
     */
    private Integer couponType;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;

    /**
     * 优惠券使用范围类型（1店铺通用；2指定商品；3指定类型）
     */
    private Integer scopeType;

    /**
     * 状态：0禁用；1正常
     */
    private Integer status;

    /**
     * 已领取数量
     */
    private Integer receivedNum;

    /**
     * 总数量
     */
    private Integer totalNum;

    /**
     * 用户是否可领取（0已达上限不可领取；1可领取）
     */
    private Integer receiveStatus;

    /**
     * 使用状态
     */
    private Integer usedStatus;

    /**
     * 指定商家列表
     */
    private List<IdAndNameDto> corpIdList;

    /**
     * 指定商品列表
     */
    private List<IdAndNameDto> goodsIdList;
}
