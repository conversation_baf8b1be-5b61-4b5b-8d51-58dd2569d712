package com.xk.promotion.interfaces.dto.rsp;

import lombok.AllArgsConstructor;
import lombok.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CouponReceivedRspDto {

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 领取方式（1手动领取；2兑换码兑换；3平台下发；4商户下发）
     */
    private Integer receivedType;

    /**
     * 领取时间
     */
    private Date createTime;

    /**
     * 使用状态：（1未使用; 2已使用；3已过期）
     */
    private Integer usedStatus;
}
