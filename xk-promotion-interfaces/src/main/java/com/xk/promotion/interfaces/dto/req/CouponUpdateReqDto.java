package com.xk.promotion.interfaces.dto.req;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponUpdateReqDto extends RequireSessionDto {

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 优惠券所属类型 （1运营平台；2商家平台）
     */
    @NotNull
    private Integer ownerType;

    /**
     * 优惠券类型（1无门槛优惠券；2有门槛优惠券；）
     */
    @NotNull
    private Integer couponType;

    /**
     * 优惠券名称
     */
    @NotNull
    @Size(max = 10, message = "优惠券名称不能超过10个字符")
    private String name;

    /**
     * 优惠券金额
     */
    @NotNull
    private Long amount;

    /**
     * 是否有门槛（0：否，1：是）
     */
    @NotNull
    private Integer isThreshold;

    /**
     * 门槛金额
     */
    private Long thresholdAmount;

    /**
     * 总数量
     */
    @NotNull
    private Integer totalNum;

    /**
     * 每人领取上限
     */
    @NotNull
    private Integer personNum;

    /**
     * 优惠券使用范围类型（1店铺通用；2指定商品；3指定类型）
     */
    @NotNull
    private Integer scopeType;

    /**
     * 有效期规则（1时间范围；2领取后期限）
     */
    @NotNull
    private Integer periodType;

    /**
     * 领取后有效期：单位小时
     */
    private Integer periodTime;

    /**
     * 优惠券有效期开始时间
     */
    private Date startTime;

    /**
     * 优惠券有效期结束时间
     */
    private Date endTime;

    /**
     * 使用说明
     */
    @NotNull
    @Size(max = 30, message = "规则说明文案不能超过30个字符")
    private String instruction;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 已领取数量
     */
    private Integer receivedNum;

    /**
     * 已使用数量
     */
    private Integer usedNum;

    /**
     * 是否在店铺首页显示（0：否，1：是）
     */
    private Integer isShowHomepage;

    /**
     * 是否在领券中心显示（0：否，1：是）
     */
    private Integer isShowCenter;

}
