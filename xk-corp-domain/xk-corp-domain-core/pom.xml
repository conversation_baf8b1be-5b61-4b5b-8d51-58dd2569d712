<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.corp</groupId>
        <artifactId>xk-corp-domain</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-corp-domain-core</artifactId>
    <packaging>jar</packaging>
    <name>xk-corp-domain-core</name>
    <description>xk-corp-domain-core</description>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.corp</groupId>
            <artifactId>xk-corp-domain-event</artifactId>
        </dependency>
    </dependencies>
</project>
