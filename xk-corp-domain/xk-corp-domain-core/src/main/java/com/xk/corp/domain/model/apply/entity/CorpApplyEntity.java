package com.xk.corp.domain.model.apply.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.CreateValObj;
import com.xk.corp.domain.model.UpdateValObj;
import com.xk.corp.domain.model.apply.id.CorpApplyIdentifier;
import com.xk.corp.enums.corp.AuditStatusEnum;
import com.xk.corp.enums.corp.CorpPayTypeEnum;
import com.xk.enums.common.CommonStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Builder
@Data
@AllArgsConstructor
public class CorpApplyEntity implements Entity<CorpApplyIdentifier> {

    /**
     * 公司申请ID
     */
    private Long corpApplyId;

    /**
     * 公司全称
     */
    private String corpName;

    /**
     * 商户详细介绍
     */
    private String corpDescribe;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 微信ID
     */
    private String wechatId;

    /**
     * 微信二维码
     */
    private String wechatQrCode;

    /**
     * 申请人用户ID
     */
    private Long applyUserId;

    /**
     * 申请提交时间
     */
    private Date applyTime;

    /**
     * 累计申请次数
     */
    private Integer applyCount;

    /**
     * LOGO存储路径
     */
    private String logo;

    /**
     * 补充说明图片路径
     */
    private String additionalPic;

    /**
     * 营业执照存储路径
     */
    private String businessLicense;

    /**
     * 是否添加客服(0-否 1-是)
     */
    private CommonStatusEnum addSupport;

    /**
     * 是否入驻其他平台(0-否 1-是)
     */
    private CommonStatusEnum regOtherPlatform;

    /**
     * 支付偏好(0-任意 1-银行卡 2-支付宝 3-微信支付)
     */
    private CorpPayTypeEnum payTypeLike;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝实名名称
     */
    private String alipayAccountName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 银行账户名称
     */
    private String bankAccountName;

    /**
     * 开户行全称
     */
    private String bankName;

    /**
     * 审核状态（0-待审核 1-通过 2-拒绝）
     */
    private AuditStatusEnum auditStatus;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 驳回原因
     */
    private String refuseRemark;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    private CreateValObj createValObj;

    private UpdateValObj updateValObj;

    @Override
    public @NonNull CorpApplyIdentifier getIdentifier() {
        return CorpApplyIdentifier.builder().corpApplyId(corpApplyId).build();
    }

    @Override
    public Validatable<CorpApplyIdentifier> validate() {
        return this;
    }

    public void setDefaultValue() {
        this.auditStatus = AuditStatusEnum.WAIT_AUDIT;
        if (createValObj != null && createValObj.getCreateTime() == null) {
            createValObj.setDefaultValue();
        }
    }
}
