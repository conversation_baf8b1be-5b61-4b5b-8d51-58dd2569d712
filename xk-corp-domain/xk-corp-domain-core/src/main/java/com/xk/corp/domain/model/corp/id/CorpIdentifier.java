package com.xk.corp.domain.model.corp.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorpIdentifier implements Identifier<CorpIdentifier> {

    private Long corpInfoId;

    @Override
    public @NonNull CorpIdentifier getIdentifier() {
        return CorpIdentifier.builder().corpInfoId(corpInfoId).build();
    }
}
