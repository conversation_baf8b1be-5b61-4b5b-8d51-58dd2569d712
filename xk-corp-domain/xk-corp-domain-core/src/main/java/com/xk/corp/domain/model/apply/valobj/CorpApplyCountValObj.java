package com.xk.corp.domain.model.apply.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpApplyCountValObj {

    /**
     * 首次申请数量
     */
    private Integer firstCount;

    /**
     * 多次申请数量
     */
    private Integer multiCount;

    /**
     * 通过数量
     */
    private Integer passCount;

    /**
     * 未通过数量
     */
    private Integer unPassCount;
}
