package com.xk.corp.domain.model.corp.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.CreateValObj;
import com.xk.corp.domain.model.UpdateValObj;
import com.xk.corp.domain.model.corp.id.CorpInfoIdentifier;
import com.xk.corp.domain.model.corp.valobj.LegalRepValObj;
import com.xk.enums.common.CommonStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Builder
@Data
@AllArgsConstructor
public class CorpInfoEntity implements Entity<CorpInfoIdentifier> {

    /**
     * 公司ID
     */
    private Long corpInfoId;

    private String logo;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 商户状态（1-正常 0-禁用）
     */
    private CommonStatusEnum corpStatus;

    /**
     * 商户暂压状态（1-正常 0-禁用）
     */
    private CommonStatusEnum delayStatus;

    /**
     * 商户上架状态 （1-正常 0-禁用）
     */
    private CommonStatusEnum launchStatus;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人手机号
     */
    private String contactPhone;

    /**
     * 微信id
     */
    private String wechatId;

    /**
     * 回款周期
     */
    private Integer paymentCycle;

    /**
     * 管理员用户ID
     */
    private Long adminUserId;

    private CreateValObj createValObj;

    private UpdateValObj updateValObj;

    private LegalRepValObj legalRepValObj;

    @Override
    public @NonNull CorpInfoIdentifier getIdentifier() {
        return CorpInfoIdentifier.builder().corpInfoId(corpInfoId).build();
    }

    @Override
    public Validatable<CorpInfoIdentifier> validate() {
        return this;
    }

    public void setDefaultValue() {
        this.corpStatus = CommonStatusEnum.ENABLE;
        this.launchStatus = CommonStatusEnum.ENABLE;
        this.delayStatus = CommonStatusEnum.ENABLE;
        if (createValObj != null && createValObj.getCreateTime() == null) {
            createValObj.setDefaultValue();
        }
    }
}
