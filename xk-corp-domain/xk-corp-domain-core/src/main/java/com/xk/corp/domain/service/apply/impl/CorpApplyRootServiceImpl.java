package com.xk.corp.domain.service.apply.impl;

import java.util.Comparator;
import java.util.List;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.corp.domain.model.apply.entity.CorpApplyEntity;
import com.xk.corp.domain.model.apply.valobj.CorpApplyCountValObj;
import com.xk.corp.domain.repository.apply.CorpApplyRootQueryRepository;
import com.xk.corp.domain.service.apply.CorpApplyRootService;
import com.xk.corp.domain.support.CorpSequenceEnum;
import com.xk.corp.enums.corp.AuditStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpApplyRootServiceImpl implements CorpApplyRootService {

    private final IdentifierGenerateService identifierGenerateService;
    private final CorpApplyRootQueryRepository rootQueryRepository;

    @Override
    public Mono<Long> generateApplyId() {
        return Mono.just(identifierGenerateService
                .generateIdentifier(IdentifierRoot.builder().identifier(CorpSequenceEnum.CORP_APPLY)
                        .type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);
    }

    @Override
    public Mono<CorpApplyCountValObj> searchCount() {
        return rootQueryRepository.searchAllCount().collectList()
                .map(v -> CorpApplyCountValObj.builder().firstCount(countApplyCountEqOne(v))
                        .multiCount(countApplyCountGtOne(v)).passCount(countAuditPassed(v))
                        .unPassCount(countAuditRejected(v)).build());
    }

    @Override
    public Mono<CorpApplyEntity> findBySession() {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> rootQueryRepository
                        .searchByApplyUserId(CorpApplyEntity.builder().applyUserId(userId).build())
                        .collectList()
                        .flatMap(list -> list.stream()
                                .max(Comparator.comparing(CorpApplyEntity::getCorpApplyId))
                                .map(Mono::just).orElse(Mono.empty())));
    }

    @Override
    public Mono<CorpApplyEntity> findById(LongIdentifier longIdentifier) {
        return rootQueryRepository.findById(longIdentifier);
    }

    private int countApplyCountEqOne(List<CorpApplyEntity> applies) {
        return (int) applies.stream()
                .filter(a -> a.getApplyCount() == 1 && AuditStatusEnum.WAIT_AUDIT
                        .equals(a.getAuditStatus()))
                .count();
    }

    private int countApplyCountGtOne(List<CorpApplyEntity> applies) {
        return (int) applies.stream()
                .filter(a -> a.getApplyCount() != 1 && AuditStatusEnum.WAIT_AUDIT
                        .equals(a.getAuditStatus()))
                .count();
    }

    private int countAuditPassed(List<CorpApplyEntity> applies) {
        return (int) applies.stream()
                .filter(a -> AuditStatusEnum.AUDIT_SUCCESS.equals(a.getAuditStatus())).count();
    }

    private int countAuditRejected(List<CorpApplyEntity> applies) {
        return (int) applies.stream()
                .filter(a -> AuditStatusEnum.AUDIT_FAIL.equals(a.getAuditStatus())).count();
    }
}
