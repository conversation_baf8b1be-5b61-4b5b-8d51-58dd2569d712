package com.xk.corp.domain.commons;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.support.DefaultConversionService;

import com.xk.corp.enums.corp.CorpPayTypeEnum;
import com.xk.enums.common.CommonStatusEnum;

/**
 * PropertyUtil
 *
 * <AUTHOR> date 2024/07/21
 */
public class BeanUtil<T> {


    // 创建全局 ConversionService 并注册自定义转换器
    private static final ConversionService conversionService = createConversionService();

    private static ConversionService createConversionService() {
        DefaultConversionService service = new DefaultConversionService();
        service.addConverter(String.class, CommonStatusEnum.class, source -> {
            try {
                return CommonStatusEnum.getEnum(Integer.parseInt(source));
            } catch (IllegalArgumentException e) {
                throw new ConversionFailedException(TypeDescriptor.valueOf(String.class),
                        TypeDescriptor.valueOf(CommonStatusEnum.class), source, e);
            }
        });
        service.addConverter(String.class, CorpPayTypeEnum.class, source -> {
            try {
                return CorpPayTypeEnum.getEnum(Integer.parseInt(source));
            } catch (IllegalArgumentException e) {
                throw new ConversionFailedException(TypeDescriptor.valueOf(String.class),
                        TypeDescriptor.valueOf(CommonStatusEnum.class), source, e);
            }
        });
        return service;
    }

    public static <T> T mapTo(Map<String, String> configMap, Class<T> clazz) {
        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            BeanWrapperImpl beanWrapper = new BeanWrapperImpl(instance);
            // 关键步骤：设置 ConversionService
            beanWrapper.setConversionService(conversionService);

            for (Map.Entry<String, String> entry : configMap.entrySet()) {
                String propertyName = entry.getKey();
                if (StringUtils.isEmpty(propertyName)) {
                    continue;
                }
                String propertyValue = entry.getValue();

                if (beanWrapper.isWritableProperty(propertyName)) {
                    // 通过 ConversionService 自动转换类型
                    beanWrapper.setPropertyValue(propertyName, propertyValue);
                }
            }
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("创建或映射对象失败", e);
        }
    }
}
