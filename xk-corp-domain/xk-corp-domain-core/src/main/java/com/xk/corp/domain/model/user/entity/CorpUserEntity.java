package com.xk.corp.domain.model.user.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.xk.auth.enums.corp.CorpUserRoleEnum;
import com.xk.corp.domain.model.CreateValObj;
import com.xk.corp.domain.model.UpdateValObj;
import com.xk.corp.domain.model.corp.id.CorpUserIdentifier;
import com.xk.enums.common.CommonStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Builder
@Data
@AllArgsConstructor
public class CorpUserEntity implements Entity<CorpUserIdentifier> {

    /**
     * 商户成员ID（主键）
     */
    private Long corpUserId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 用户ID（全局唯一）
     */
    private Long userId;

    /**
     * 成员权限角色
     */
    private CorpUserRoleEnum corpUserRole;

    /**
     * 员工身份名称
     */
    private String corpUserRoleName;

    /**
     * 成员业务权限角色
     */
    private String corpUserBusinessRole;

    /**
     * 成员适用平台
     */
    private String corpUserPlatformType;

    /**
     * 成员姓名
     */
    private String corpUserName;

    /**
     * 成员手机号
     */
    private String corpUserPhone;

    /**
     * 成员状态（0-禁用 1-启用）
     */
    private CommonStatusEnum corpUserStatus;

    private CreateValObj createValObj;

    private UpdateValObj updateValObj;

    @Override
    public @NonNull CorpUserIdentifier getIdentifier() {
        return CorpUserIdentifier.builder().corpUserId(corpUserId).build();
    }

    @Override
    public Validatable<CorpUserIdentifier> validate() {
        return this;
    }

    public void setDefaultValue() {
        if (this.corpUserStatus == null) {
            this.corpUserStatus = CommonStatusEnum.ENABLE;
        }
        if (createValObj != null && createValObj.getCreateTime() == null) {
            createValObj.setDefaultValue();
        }
    }
}
