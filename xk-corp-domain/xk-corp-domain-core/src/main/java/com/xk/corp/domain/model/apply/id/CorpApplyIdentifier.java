package com.xk.corp.domain.model.apply.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorpApplyIdentifier implements Identifier<CorpApplyIdentifier> {

    private Long corpApplyId;

    @Override
    public @NonNull CorpApplyIdentifier getIdentifier() {
        return CorpApplyIdentifier.builder().corpApplyId(corpApplyId).build();
    }
}
