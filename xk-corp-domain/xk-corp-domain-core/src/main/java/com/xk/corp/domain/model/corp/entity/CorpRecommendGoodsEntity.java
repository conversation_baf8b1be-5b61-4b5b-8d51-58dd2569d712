package com.xk.corp.domain.model.corp.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import lombok.AllArgsConstructor;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CorpRecommendGoodsEntity implements Entity<LongIdentifier> {
    /**
     * 推荐商家主键
     */
    private Long corpRecommendId;

    /**
     * 商品ID字符串，分号拼接
     */
    private String goodsIds;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(corpRecommendId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() {
        return this;
    }
}
