package com.xk.corp.domain.service.corp.impl;

import java.util.Map;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.corp.domain.commons.BeanUtil;
import com.xk.corp.domain.model.corp.CorpRoot;
import com.xk.corp.domain.model.corp.entity.CorpInfoEntity;
import com.xk.corp.domain.model.corp.valobj.BusinessConfigValObj;
import com.xk.corp.domain.model.corp.valobj.CustomizeConfigValObj;
import com.xk.corp.domain.model.corp.valobj.PaymentAccountValObj;
import com.xk.corp.domain.model.user.entity.CorpUserEntity;
import com.xk.corp.domain.repository.corp.CorpRootQueryRepository;
import com.xk.corp.domain.repository.user.CorpUserRootQueryRepository;
import com.xk.corp.domain.service.corp.CorpRootService;
import com.xk.corp.domain.support.CorpSequenceEnum;
import com.xk.corp.enums.corp.CorpPayTypeEnum;
import com.xk.domain.model.config.BusinessConfigEntity;
import com.xk.domain.repository.config.BusinessConfigRootQueryRepository;
import com.xk.enums.config.BusinessConfigGroupTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class CorpRootServiceImpl implements CorpRootService {

    private final CorpRootQueryRepository corpRootQueryRepository;
    private final CorpUserRootQueryRepository corpUserRootQueryRepository;
    private final BusinessConfigRootQueryRepository businessConfigRootQueryRepository;
    private final IdentifierGenerateService identifierGenerateService;

    @Override
    public Mono<Long> generateCorpId() {
        return Mono.just(identifierGenerateService.generateIdentifier(IdentifierRoot.builder()
                .identifier(CorpSequenceEnum.CORP_INFO).type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);
    }

    @Override
    public Mono<CorpRoot> getRoot(LongIdentifier identifier) {

        Supplier<Mono<Map<CorpPayTypeEnum, PaymentAccountValObj>>> findPaymentAccount =
                () -> corpRootQueryRepository.searchPaymentByCorpInfoId(identifier)
                        .collectMap(PaymentAccountValObj::getPayType, v -> v);

        Supplier<Mono<Map<String, String>>> findConfig = () -> businessConfigRootQueryRepository
                .findList(BusinessConfigEntity.builder()
                        .groupType(BusinessConfigGroupTypeEnum.CORP.getCode())
                        .groupId(String.valueOf(identifier.id()))
                        .businessType(BusinessTypeEnum.XING_KA.getValue()).build())
                .collectMap(BusinessConfigEntity::getKey, BusinessConfigEntity::getVal);

        Supplier<Mono<CorpInfoEntity>> findCorpInfo =
                () -> corpRootQueryRepository.findById(identifier);

        return Mono.justOrEmpty(identifier)
                .flatMap(id -> Mono
                        .zip(findCorpInfo.get(), findPaymentAccount.get(), findConfig.get())
                        .flatMap(tuple -> {
                            BusinessConfigValObj configValObj =
                                    BeanUtil.mapTo(tuple.getT3(), BusinessConfigValObj.class);
                            CustomizeConfigValObj customizeConfigValObj =
                                    BeanUtil.mapTo(tuple.getT3(), CustomizeConfigValObj.class);

                            CorpRoot corpRoot = CorpRoot.builder().identifier(identifier)
                                    .corpInfoEntity(tuple.getT1())
                                    .paymentAccountValObj(tuple.getT2())
                                    .businessConfigValObj(configValObj)
                                    .customizeConfigValObj(customizeConfigValObj).build();
                            return Mono.just(corpRoot);
                        }));
    }

    @Override
    public Mono<Long> findIdBySession() {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> corpUserRootQueryRepository
                        .findByUserId(LongIdentifier.builder().id(userId).build())
                        .map(CorpUserEntity::getCorpInfoId));
    }

    @Override
    public Mono<CorpInfoEntity> findEntityBySession() {
        return findIdBySession().flatMap(corpInfoId -> corpRootQueryRepository
                .findById(LongIdentifier.builder().id(corpInfoId).build()));
    }

    @Override
    public Mono<Long> findCorpIdByUserId(Mono<Long> userIdMono) {
        return userIdMono.flatMap(userId -> corpUserRootQueryRepository
                .findByUserId(LongIdentifier.builder().id(userId).build())
                .map(CorpUserEntity::getCorpInfoId));
    }

    @Override
    public Mono<CorpInfoEntity> findEntityByUserId(Mono<Long> userIdMono) {
        return findCorpIdByUserId(userIdMono).flatMap(corpInfoId -> corpRootQueryRepository
                .findById(LongIdentifier.builder().id(corpInfoId).build()));
    }

    @Override
    public Mono<Boolean> checkIfAdmin() {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> findEntityBySession().flatMap(corpInfoEntity -> Mono
                        .just(userId.equals(corpInfoEntity.getAdminUserId()))));
    }

    @Override
    public Mono<Boolean> checkCorpNameExist(CorpInfoEntity entity) {
        return corpRootQueryRepository.findByCorpName(entity).hasElement();
    }

    @Override
    public Mono<Boolean> checkCorpPhoneExist(CorpInfoEntity entity) {
        return corpRootQueryRepository.findByContactPhone(entity).hasElement();
    }

    @Override
    public Mono<CorpRoot> getCorpObject(Mono<LongIdentifier> longIdentifierMono) {
        return corpRootQueryRepository.getCorpObjectById(longIdentifierMono);
    }

}
