package com.xk.corp.domain.commons;


import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * domain相关错误码
 * 定义错误码区间 11000-11999
 *
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum XkCorpDomainErrorEnum implements ExceptionIdentifier {
    DOMAIN_ERROR(11000, "domain错误"),
    CORP_NOT_FOUND(11001, "公司不存在"),
    CORP_DATA_ACCESS_ERROR(11002, "公司数据访问错误"),
    DEPT_HAVA_EMPLOYEE_CANNOT_DELETE(11003, "部门下有员工，不能删除"),
    DEPT_CANT_DELETE(11004, "部门不能删除"),
    CORP_USER_NOT_FOUND(11005, "公司员工不存在"),

    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
