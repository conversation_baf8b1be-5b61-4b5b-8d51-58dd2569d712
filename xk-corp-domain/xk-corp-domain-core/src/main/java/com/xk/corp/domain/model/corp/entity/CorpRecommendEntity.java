package com.xk.corp.domain.model.corp.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
public class CorpRecommendEntity implements Entity<LongIdentifier> {
    /**
     * 推荐商家主键
     */
    private Long corpRecommendId;

    /**
     * 推荐序号
     */
    private Integer corpRecommendNo;

    /**
     * 推荐类型: 1首页热门商家 2推荐热门商家 3优选新商家;4首页banner；5首页商品
     */
    private Integer corpRecommendType;

    /**
     * 板块 1球队；2动漫
     */
    private Integer blockType;

    /**
     * 类型 1福盒；2边锋盒子；3搓卡密；4原盒
     */
    private Integer groupType;

    /**
     * 商家ID
     */
    private Long corpId;

    /**
     * 商家名称
     */
    private String corpName;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(corpRecommendId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() {
        return this;
    }
}
