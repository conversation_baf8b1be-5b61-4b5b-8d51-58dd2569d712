package com.xk.corp.domain.support;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum CorpSequenceEnum implements SequenceIdentifier {

    CORP_INFO("corp_info","corp_info_id","CorpInfoMapper"),
    CORP_APPLY("corp_apply","corp_apply_id","CorpApplyMapper"),
    CORP_USER("corp_user","corp_user_id","CorpUserMapper"),
    CORP_RECOMMEND("corp_recommend","corp_recommend_id","CorpRecommendMapper"),
    ;

    private final String table;
    private final String pk;
    private final String className;

    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    public String getTable() {
        return this.table;
    }

    public String getPk() {
        return this.pk;
    }

    public String getClassName() {
        return this.className;
    }

}
