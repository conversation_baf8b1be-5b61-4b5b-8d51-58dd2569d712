package com.xk.corp.domain.repository.user;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.corp.domain.model.corp.id.CorpInfoIdentifier;
import com.xk.corp.domain.model.corp.id.CorpUserIdentifier;
import com.xk.corp.domain.model.user.entity.CorpUserEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface CorpUserRootQueryRepository extends IQueryRepository {

    /**
     * 根据id查询商户成员实体
     * 
     * @param identifier id
     * @return 商户成员
     */
    Mono<CorpUserEntity> findById(CorpUserIdentifier identifier);

    /**
     * 根据userId查询商户成员实体
     *
     * @param identifier userId
     * @return 商户成员
     */
    Mono<CorpUserEntity> findByUserId(LongIdentifier identifier);

    /**
     * 分页查询商户实体
     * 
     * @param pagination 分页器
     * @return 所有商户成员
     */
    Flux<CorpUserEntity> searchPager(Pagination pagination);

    /**
     * 根据手机号查询商户成员实体
     *
     * @param corpUserEntity 手机号
     * @return 商户成员
     */
    Mono<CorpUserEntity> findByPhone(CorpUserEntity corpUserEntity);

    /**
     * 根据商户查找管理员
     * 
     * @param identifier identifier
     * @return Mono<CorpUserEntity>
     */
    Mono<CorpUserEntity> findAdmin(CorpInfoIdentifier identifier);

    /**
     * 通过公司id获取所有员工
     * @param identifier identifier
     * @return Flux<CorpUserIdentifier>
     */
    Flux<CorpUserEntity> findByCorpId(LongIdentifier identifier);
}
