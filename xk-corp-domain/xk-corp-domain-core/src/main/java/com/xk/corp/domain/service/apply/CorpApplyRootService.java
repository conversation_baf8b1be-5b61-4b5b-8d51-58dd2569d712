package com.xk.corp.domain.service.apply;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.corp.domain.model.apply.entity.CorpApplyEntity;
import com.xk.corp.domain.model.apply.valobj.CorpApplyCountValObj;

import reactor.core.publisher.Mono;

public interface CorpApplyRootService {
    /**
     * 生成入驻申请id
     * 
     * @return 入驻申请Id
     */
    Mono<Long> generateApplyId();

    /**
     * 查询申请数量
     * 
     * @return 首次申请数量、多次申请数量、通过数量、未通过数量
     */
    Mono<CorpApplyCountValObj> searchCount();

    /**
     * 根据session查询最新入驻申请(根据id排序)
     *
     * @return 最新入驻申请
     */
    Mono<CorpApplyEntity> findBySession();

    /**
     * 根据id查找入驻申请
     * 
     * @param longIdentifier id
     * @return 单条入驻申请
     */
    Mono<CorpApplyEntity> findById(LongIdentifier longIdentifier);
}
