package com.xk.corp.domain.model.corp.entity;

import java.util.Date;
import java.util.List;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.corp.id.CorpInfoIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class CorpWonderfulMomentEntity implements Entity<CorpInfoIdentifier> {

    private List<String> picUrls;

    private Date createTime;

    private String name;

    private Long corpInfoId;

    private Integer winnerStatus;


    @Override
    public @NonNull CorpInfoIdentifier getIdentifier() {
        return CorpInfoIdentifier.builder().corpInfoId(corpInfoId).build();
    }

    @Override
    public Validatable<CorpInfoIdentifier> validate() {
        return this;
    }
}
