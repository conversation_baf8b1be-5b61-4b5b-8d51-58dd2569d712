package com.xk.corp.domain.model.corp;

import java.util.List;
import java.util.Map;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.corp.entity.CorpInfoEntity;
import com.xk.corp.domain.model.corp.valobj.BusinessConfigValObj;
import com.xk.corp.domain.model.corp.valobj.CorpBlockUserValObj;
import com.xk.corp.domain.model.corp.valobj.CustomizeConfigValObj;
import com.xk.corp.domain.model.corp.valobj.PaymentAccountValObj;
import com.xk.corp.enums.corp.CorpPayTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class CorpRoot extends DomainRoot<LongIdentifier> {

    private final CorpInfoEntity corpInfoEntity;

    private final Map<CorpPayTypeEnum, PaymentAccountValObj> paymentAccountValObj;

    private final BusinessConfigValObj businessConfigValObj;

    private final CustomizeConfigValObj customizeConfigValObj;

    private final List<CorpBlockUserValObj> corpBlockUserValObjList;

    @Builder
    public CorpRoot(@NonNull LongIdentifier identifier, CorpInfoEntity corpInfoEntity,
            Map<CorpPayTypeEnum, PaymentAccountValObj> paymentAccountValObj,
            BusinessConfigValObj businessConfigValObj, CustomizeConfigValObj customizeConfigValObj,
            List<CorpBlockUserValObj> corpBlockUserValObjList) {
        super(identifier);
        this.corpInfoEntity = corpInfoEntity;
        this.paymentAccountValObj = paymentAccountValObj;
        this.businessConfigValObj = businessConfigValObj;
        this.customizeConfigValObj = customizeConfigValObj;
        this.corpBlockUserValObjList = corpBlockUserValObjList;
    }

    @Override
    public Validatable<LongIdentifier> validate() {
        return this;
    }

}
