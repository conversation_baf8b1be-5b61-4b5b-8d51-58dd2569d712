package com.xk.corp.domain.model.user;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.corp.id.CorpUserIdentifier;
import com.xk.corp.domain.model.user.entity.CorpUserEntity;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class CorpUserRoot extends DomainRoot<CorpUserIdentifier> {

    private final CorpUserEntity corpUserEntity;

    @Builder
    public CorpUserRoot(@NonNull CorpUserIdentifier identifier, CorpUserEntity corpUserEntity) {
        super(identifier);
        this.corpUserEntity = corpUserEntity;
    }

    @Override
    public Validatable<CorpUserIdentifier> validate() {
        return this;
    }
}
