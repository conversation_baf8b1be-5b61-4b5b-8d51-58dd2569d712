package com.xk.corp.domain.model.corp.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LegalRepValObj {

    /**
     * 法人代表姓名
     */
    private String legalRepName;

    /**
     * 法人身份证号
     */
    private String legalRepIdCard;

    /**
     * 身份证正面URL
     */
    private String legalRepIdCardFront;

    /**
     * 身份证反面URL
     */
    private String legalRepIdCardBack;

    /**
     * 营业执照URL
     */
    private String businessLicense;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;
}
