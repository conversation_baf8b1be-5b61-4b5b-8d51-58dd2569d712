package com.xk.corp.domain.repository.corp;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.corp.domain.model.corp.CorpRoot;
import com.xk.corp.domain.model.corp.entity.CorpInfoEntity;
import com.xk.corp.domain.model.corp.valobj.BusinessConfigValObj;
import com.xk.corp.domain.model.corp.valobj.PaymentAccountValObj;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface CorpRootQueryRepository extends IQueryRepository {

    /**
     * 根据id查询商户实体
     * 
     * @param identifier id
     * @return 商户实体
     */
    Mono<CorpInfoEntity> findById(LongIdentifier identifier);

    /**
     * 查询商户信息
     * 
     * @param pagination 条件
     * @return 商户
     */
    Flux<CorpInfoEntity> searchCorpInfo(Pagination pagination);

    /**
     * 根据商户id查询商户支付信息
     * 
     * @param identifier 商户id
     * @return 商户所有支付信息
     */
    Flux<PaymentAccountValObj> searchPaymentByCorpInfoId(LongIdentifier identifier);

    /**
     * 根据商户名称查询商户实体
     *
     * @param entity 商户名称
     * @return 商户实体
     */
    Mono<CorpInfoEntity> findByCorpName(CorpInfoEntity entity);

    /**
     * 根据商户联系人手机号查询商户实体
     *
     * @param entity 联系人手机号
     * @return 商户实体
     */
    Mono<CorpInfoEntity> findByContactPhone(CorpInfoEntity entity);

    /**
     * 根据支付账号查找商户id
     * 
     * @param valObj 支付账号
     * @return 所有商户id
     */
    Flux<Long> searchCorpInfoIdByPayAccount(PaymentAccountValObj valObj);

    /**
     * 根据公司标识符查询公司配置信息
     * @param identifier identifier
     * @return Mono<BusinessConfigValObj>
     */
    Mono<BusinessConfigValObj> getBusinessConfigValObj(LongIdentifier identifier);

    /**
     * 根据id查询公司信息对象
     * @param identifierMono identifierMono
     * @return Mono<CorpRoot>
     */
    Mono<CorpRoot> getCorpObjectById(Mono<LongIdentifier> identifierMono);


    Flux<Long> getListAll();

}
