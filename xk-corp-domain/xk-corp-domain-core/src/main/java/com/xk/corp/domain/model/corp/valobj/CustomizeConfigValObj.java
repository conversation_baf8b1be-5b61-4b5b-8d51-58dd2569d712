package com.xk.corp.domain.model.corp.valobj;

import com.xk.corp.enums.corp.ThemeColorEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomizeConfigValObj {

    /**
     * 公司logo URL
     * 
     */
    private String logo;

    /**
     * 展示简介
     */
    private String displayRemark;

    /**
     * 主题色
     */
    private ThemeColorEnum themeColor;

    /**
     * 卡背图片URL
     */
    private String cardBack;
}
