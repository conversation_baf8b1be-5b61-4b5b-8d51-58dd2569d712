package com.xk.corp.domain.repository.recommend;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.corp.domain.model.corp.entity.CorpRecommendEntity;
import com.xk.corp.domain.model.corp.entity.CorpRecommendGoodsEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
public interface CorpRecommendRootQueryRepository extends IQueryRepository {

    /**
     * 查询商户列表
     *
     * @param pagination 条件
     * @return 商户
     */
    Flux<CorpRecommendEntity> searchCorpRecommends(Pagination pagination);

    /**
     * 查询商户推荐位信息
     * */
    Mono<CorpRecommendEntity> getCorpRecommend(LongIdentifier longIdentifier);

    /**
     * 查询商户推荐商品
     * */
    Mono<CorpRecommendGoodsEntity> getCorpRecommendGoods(LongIdentifier longIdentifier);

    /**
     * 查询所有商户推荐商品
     * */
    Flux<CorpRecommendGoodsEntity> searchAllCorpRecommendGoods();

}
