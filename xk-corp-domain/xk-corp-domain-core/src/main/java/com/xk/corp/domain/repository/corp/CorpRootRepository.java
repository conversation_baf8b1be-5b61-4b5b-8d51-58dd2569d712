package com.xk.corp.domain.repository.corp;

import com.myco.mydata.domain.repository.IRepository;
import com.xk.corp.domain.model.corp.CorpRoot;
import com.xk.corp.domain.model.corp.valobj.CorpBlockUserValObj;

import reactor.core.publisher.Mono;

public interface CorpRootRepository extends IRepository<CorpRoot> {

    /**
     * 商家拉黑用户
     * 
     * @param valObj mono
     * @return Mono<Void>
     */
    Mono<Void> addBlockUser(CorpBlockUserValObj valObj);

    /**
     * 商家取消拉黑用户
     *
     * @param valObj mono
     * @return Mono<Void>
     */
    Mono<Void> removeBlockUser(CorpBlockUserValObj valObj);
}
