package com.xk.corp.domain.service.user;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.corp.domain.model.corp.id.CorpInfoIdentifier;
import com.xk.corp.domain.model.corp.id.CorpUserIdentifier;
import com.xk.corp.domain.model.user.CorpUserRoot;
import com.xk.corp.domain.model.user.entity.CorpUserEntity;

import reactor.core.publisher.Mono;

public interface CorpUserRootService {

    /**
     * 生成商户成员id
     * 
     * @return 商户成员id
     */
    Mono<Long> generateCorpUserId();

    /**
     * 根据id获取用户
     * 
     * @param identifier id
     * @return 用户
     */
    Mono<CorpUserEntity> findById(CorpUserIdentifier identifier);

    /**
     * 根据userId获取用户
     *
     * @param identifier id
     * @return 用户
     */
    Mono<CorpUserEntity> findByUserId(LongIdentifier identifier);

    /**
     * 校验是否修改了手机号
     * 
     * @param entity 用户修改的手机号
     * @return 是否修改
     */
    Mono<Boolean> checkEditPhone(CorpUserEntity entity);

    /**
     * 校验当前session的用户与传入的用户是否同一商家
     *
     * @param identifier id
     * @return 是否同一商家
     */
    Mono<Boolean> checkIfSameCorp(CorpUserIdentifier identifier);

    /**
     * 校验用户是否是管理员
     * 
     * @param identifier id
     * @return 是否管理员
     */
    Mono<Boolean> checkIfAdmin(CorpUserIdentifier identifier);

    /**
     * 根据用户id校验用户是否是管理员
     *
     * @param identifier id
     * @return 是否管理员
     */
    Mono<Boolean> checkIfAdmin(LongIdentifier identifier);

    /**
     * 校验用户是否是管理员
     *
     * @param mono mono
     * @return 是否管理员
     */
    Mono<Boolean> checkIfAdmin(Mono<CorpUserEntity> mono);

    /**
     * 根据商户信息查找管理员
     * 
     * @param identifier identifier
     * @return Mono<CorpUserEntity>
     */
    Mono<CorpUserEntity> findAdmin(CorpInfoIdentifier identifier);

    /**
     * 更新商户管理员
     * 
     * @param userRoot userRoot 员工
     * @param adminRoot adminRoot 管理员
     * @return Mono<Void>
     */
    Mono<Void> updateAdmin(CorpUserRoot userRoot, CorpUserRoot adminRoot);

    /**
     * 校验商户员工手机号是否存在
     * 
     * @param corpUserEntity corpUserEntity
     * @return Mono<Void>
     */
    Mono<Boolean> checkCorpUserPhoneExist(CorpUserEntity corpUserEntity);
}
