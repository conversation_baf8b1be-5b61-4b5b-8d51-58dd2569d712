package com.xk.corp.domain.support;

import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.xk.corp.domain.commons.XkCorpDomainErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.ExceptionRoot;
import com.myco.mydata.domain.model.exception.wrapper.DomainWrapperThrowable;

/**
 * 领域异常
 * @author: killer
 **/
public class XkCorpDomainException extends DomainWrapperThrowable {

    public XkCorpDomainException(XkCorpDomainErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkCorpDomainException(XkCorpDomainErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkCorpDomainException(ExceptionIdentifier exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
