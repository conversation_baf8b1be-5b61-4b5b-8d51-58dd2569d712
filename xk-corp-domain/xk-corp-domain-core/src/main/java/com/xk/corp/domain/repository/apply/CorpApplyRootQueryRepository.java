package com.xk.corp.domain.repository.apply;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.corp.domain.model.apply.entity.CorpApplyEntity;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface CorpApplyRootQueryRepository extends IQueryRepository {

    /**
     * 根据申请用户id查询申请次数
     * 
     * @param entity entity
     * @return 所有申请数据
     */
    Flux<CorpApplyEntity> searchByApplyUserId(CorpApplyEntity entity);

    /**
     * 分页查询入驻申请
     * 
     * @param pagination 分页器
     * @return 所有申请数据
     */
    Flux<CorpApplyEntity> searchCorpApply(Pagination pagination);

    /**
     * 分页查询入驻申请
     *
     * @param identifier 主键
     * @return 单条申请数据
     */
    Mono<CorpApplyEntity> findById(LongIdentifier identifier);

    /**
     * 查询所有入驻申请的数量
     * 
     * @return 申请次数和审核状态
     */
    Flux<CorpApplyEntity> searchAllCount();
}
