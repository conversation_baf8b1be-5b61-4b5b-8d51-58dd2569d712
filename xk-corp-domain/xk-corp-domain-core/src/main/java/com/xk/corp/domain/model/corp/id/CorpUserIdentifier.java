package com.xk.corp.domain.model.corp.id;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CorpUserIdentifier implements Identifier<CorpUserIdentifier> {

    private Long corpUserId;

    @Override
    public @NonNull CorpUserIdentifier getIdentifier() {
        return CorpUserIdentifier.builder().corpUserId(corpUserId).build();
    }
}
