package com.xk.corp.domain.service.user.impl;

import java.util.Objects;
import java.util.function.Supplier;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.support.DomainStaticBeanFactory;
import com.xk.auth.enums.corp.CorpUserRoleEnum;
import com.xk.corp.domain.commons.XkCorpDomainErrorEnum;
import com.xk.corp.domain.event.user.CorpUpdateAdminEvent;
import com.xk.corp.domain.model.corp.id.CorpInfoIdentifier;
import com.xk.corp.domain.model.corp.id.CorpUserIdentifier;
import com.xk.corp.domain.model.user.CorpUserRoot;
import com.xk.corp.domain.model.user.entity.CorpUserEntity;
import com.xk.corp.domain.repository.user.CorpUserRootQueryRepository;
import com.xk.corp.domain.repository.user.CorpUserRootRepository;
import com.xk.corp.domain.service.user.CorpUserRootService;
import com.xk.corp.domain.support.CorpSequenceEnum;
import com.xk.corp.domain.support.XkCorpDomainException;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class CorpUserRootServiceImpl implements CorpUserRootService {

    private final CorpUserRootQueryRepository corpUserRootQueryRepository;
    private final CorpUserRootRepository corpUserRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Long> generateCorpUserId() {
        IdentifierRoot identifierRoot = IdentifierRoot.builder()
                .identifier(CorpSequenceEnum.CORP_USER).type(IdentifierGenerateEnum.CACHE).build();
        return Mono.just((Long) DomainStaticBeanFactory.getIdentifierGenerateService()
                .generateIdentifier(identifierRoot));
    }

    @Override
    public Mono<CorpUserEntity> findById(CorpUserIdentifier identifier) {
        return corpUserRootQueryRepository.findById(identifier);
    }

    @Override
    public Mono<CorpUserEntity> findByUserId(LongIdentifier identifier) {
        return corpUserRootQueryRepository.findByUserId(identifier);
    }

    @Override
    public Mono<Boolean> checkEditPhone(CorpUserEntity entity) {
        return corpUserRootQueryRepository
                .findById(CorpUserIdentifier.builder().corpUserId(entity.getCorpUserId()).build())
                .filter(query -> !Objects.equals(query.getCorpUserPhone(),
                        entity.getCorpUserPhone()))
                .hasElement();
    }

    @Override
    public Mono<Boolean> checkIfSameCorp(CorpUserIdentifier identifier) {
        return ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> findByUserId(LongIdentifier.builder().id(userId).build()))
                .switchIfEmpty(
                        Mono.error(new XkCorpDomainException(XkCorpDomainErrorEnum.CORP_NOT_FOUND)))
                .flatMap(corpUser -> findById(identifier)
                        .switchIfEmpty(Mono.error(new XkCorpDomainException(
                                XkCorpDomainErrorEnum.CORP_USER_NOT_FOUND)))
                        .map(queryUser -> Objects.equals(queryUser.getCorpInfoId(),
                                corpUser.getCorpInfoId())));
    }

    @Override
    public Mono<Boolean> checkIfAdmin(CorpUserIdentifier identifier) {
        return this.checkIfAdmin(corpUserRootQueryRepository.findById(identifier));
    }

    @Override
    public Mono<Boolean> checkIfAdmin(LongIdentifier identifier) {
        return this.checkIfAdmin(corpUserRootQueryRepository.findByUserId(identifier));
    }

    @Override
    public Mono<Boolean> checkIfAdmin(Mono<CorpUserEntity> mono) {
        return mono
                .filter(query -> Objects.equals(CorpUserRoleEnum.MANAGER, query.getCorpUserRole()))
                .hasElement();
    }

    @Override
    public Mono<CorpUserEntity> findAdmin(CorpInfoIdentifier identifier) {
        return corpUserRootQueryRepository.findAdmin(identifier);
    }

    @Override
    public Mono<Void> updateAdmin(CorpUserRoot userRoot, CorpUserRoot adminRoot) {

        Supplier<Mono<Void>> publishEvent = () -> {
            CorpUserEntity user = userRoot.getCorpUserEntity();
            CorpUserEntity admin = adminRoot.getCorpUserEntity();
            return eventRootService.publisheByMono(EventRoot.builder()
                    .domainEvent(CorpUpdateAdminEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(CorpUpdateAdminEvent.class))
                            .corpInfoId(user.getCorpInfoId()).newAdminId(user.getUserId())
                            .newAdminMobile(user.getCorpUserPhone()).oldAdminId(admin.getUserId())
                            .build())
                    .isQueue(true).build()).then();
        };

        return corpUserRootRepository.update(adminRoot)
                .then(corpUserRootRepository.update(userRoot)).then(publishEvent.get());
    }

    @Override
    public Mono<Boolean> checkCorpUserPhoneExist(CorpUserEntity corpUserEntity) {
        return corpUserRootQueryRepository.findByPhone(corpUserEntity).hasElement();
    }
}
