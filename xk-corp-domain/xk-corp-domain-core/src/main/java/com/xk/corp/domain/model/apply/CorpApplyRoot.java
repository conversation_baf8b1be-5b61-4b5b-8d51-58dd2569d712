package com.xk.corp.domain.model.apply;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.xk.corp.domain.model.apply.entity.CorpApplyEntity;

import lombok.Builder;
import lombok.Getter;

@Getter
public class CorpApplyRoot extends DomainRoot<LongIdentifier> {

    private final CorpApplyEntity corpApplyEntity;

    @Builder
    public CorpApplyRoot(LongIdentifier identifier, CorpApplyEntity corpApplyEntity) {
        super(identifier);
        this.corpApplyEntity = corpApplyEntity;
    }

    @Override
    public Validatable<LongIdentifier> validate() {
        return this;
    }
}
