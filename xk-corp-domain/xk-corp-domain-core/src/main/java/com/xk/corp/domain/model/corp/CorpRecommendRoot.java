package com.xk.corp.domain.model.corp;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.corp.domain.model.corp.entity.CorpRecommendEntity;
import com.xk.corp.domain.model.corp.entity.CorpRecommendGoodsEntity;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class CorpRecommendRoot extends DomainRoot<LongIdentifier> {

    private final CorpRecommendEntity corpRecommendEntity;

    private final CorpRecommendGoodsEntity corpRecommendGoodsEntity;

    @Builder
    public CorpRecommendRoot(@NonNull LongIdentifier identifier,
            CorpRecommendEntity corpRecommendEntity,
            CorpRecommendGoodsEntity corpRecommendGoodsEntity) {
        super(identifier);
        this.corpRecommendEntity = corpRecommendEntity;
        this.corpRecommendGoodsEntity = corpRecommendGoodsEntity;
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
