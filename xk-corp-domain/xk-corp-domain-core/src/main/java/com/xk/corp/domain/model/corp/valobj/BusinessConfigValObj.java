package com.xk.corp.domain.model.corp.valobj;

import com.xk.corp.enums.corp.CorpPayTypeEnum;
import com.xk.enums.common.CommonStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessConfigValObj {

    /**
     * logo
     */
    private String logo;

    /**
     * 卡背图 URL
     */
    private String cardBack;

    /**
     * 发货二维码 URL
     */
    private String deliverQrCode;

    /**
     * 支付偏好
     */
    private CorpPayTypeEnum payTypeLike;

    /**
     * 下架机台权限
     */
    private CommonStatusEnum removeRole;

    /**
     * 是否可创建边锋盒子
     */
    private CommonStatusEnum createRole;

    /**
     * 优惠券支付权限
     */
    private CommonStatusEnum couponRole;

    /**
     * 卡商踢单功能权限
     */
    private CommonStatusEnum cancelOrderRole;

    /**
     * 卡密奖励配置权限
     */
    private CommonStatusEnum rewardRole;

}
