package com.xk.corp.domain.service.corp;

import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.corp.domain.model.corp.CorpRoot;
import com.xk.corp.domain.model.corp.entity.CorpInfoEntity;

import reactor.core.publisher.Mono;

public interface CorpRootService {

    /**
     * 生成商户id
     * 
     * @return id
     */
    Mono<Long> generateCorpId();

    /**
     * 根据商户信息查找root
     * 
     * @param identifier identifier
     * @return root
     */
    Mono<CorpRoot> getRoot(LongIdentifier identifier);

    /**
     * 根据session查找商户id
     * 
     * @return 商户id
     */
    Mono<Long> findIdBySession();

    /**
     * 根据session查找商户实体
     *
     * @return 商户实体
     */
    Mono<CorpInfoEntity> findEntityBySession();

    /**
     * 根据userId查找商户id
     * 
     * @param userIdMono 用户idmono
     * @return 商户id
     */
    Mono<Long> findCorpIdByUserId(Mono<Long> userIdMono);

    /**
     * 根据userId查找商户id
     *
     * @param userIdMono 用户idmono
     * @return 商户id
     */
    Mono<CorpInfoEntity> findEntityByUserId(Mono<Long> userIdMono);

    /**
     * 校验当前商户是否是商户管理员
     * 
     * @return bool
     */
    Mono<Boolean> checkIfAdmin();

    /**
     * 校验当前商户名称是否存在
     *
     * @return bool
     */
    Mono<Boolean> checkCorpNameExist(CorpInfoEntity entity);

    /**
     * 校验当前商户手机号是否存在
     *
     * @return bool
     */
    Mono<Boolean> checkCorpPhoneExist(CorpInfoEntity entity);


    /**
     * 根据商户id获取商户对象
     * @param longIdentifierMono longIdentifierMono
     * @return Mono<CorpRoot>
     */
    Mono<CorpRoot> getCorpObject(Mono<LongIdentifier> longIdentifierMono);
}
