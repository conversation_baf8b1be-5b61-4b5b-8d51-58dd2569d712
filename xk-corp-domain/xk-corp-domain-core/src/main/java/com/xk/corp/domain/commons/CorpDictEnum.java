package com.xk.corp.domain.commons;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

@Getter
@AllArgsConstructor
public enum CorpDictEnum implements DictObjectEnum {

    DEFAULT_CARD_BACK_URL("xka/ks/img/config/cardBack/2-4618-dd0b4dac66e7021f2d531ebafe1386be.png");

    private static final Map<String, CorpDictEnum> MAP;

    static {
        MAP = Arrays.stream(CorpDictEnum.values())
                    .collect(Collectors.toMap(CorpDictEnum::name, enumValue -> enumValue));
    }

    private final String defaultValue;

    public static CorpDictEnum getEnum(String name) {
        return MAP.get(name);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return name();
    }
}
