package com.xk.corp.domain.model.corp.valobj;

import java.util.Map;

import com.xk.corp.domain.model.CreateValObj;
import com.xk.corp.domain.model.UpdateValObj;
import com.xk.corp.enums.corp.CorpPayTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentAccountValObj {
    /**
     * 支付类型（1-银行卡 2-支付宝 3-微信支付）
     */
    private CorpPayTypeEnum payType;

    /**
     * 支付账号（银行卡号/支付宝账号/微信OpenID等）
     */
    private String payAccount;

    /**
     * 账户持有人姓名
     */
    private String payOwnerName;

    /**
     * 所属银行名称（支付类型为银行卡时必填）
     */
    private String payBankName;

    private CreateValObj createValObj;

    private UpdateValObj updateValObj;

    public static Map<CorpPayTypeEnum, PaymentAccountValObj> getEnumMap(CorpPayTypeEnum payType,
            String payAccount, String payOwnerName) {
        return Map.of(payType, PaymentAccountValObj.builder().payType(payType)
                .payAccount(payAccount).payOwnerName(payOwnerName).payBankName(null).build());
    }

    public static Map<CorpPayTypeEnum, PaymentAccountValObj> getEnumMap(CorpPayTypeEnum payType,
            String payAccount, String payOwnerName, String payBankName) {
        return Map.of(payType,
                PaymentAccountValObj.builder().payType(payType).payBankName(payBankName)
                        .payAccount(payAccount).payOwnerName(payOwnerName).build());
    }
}
