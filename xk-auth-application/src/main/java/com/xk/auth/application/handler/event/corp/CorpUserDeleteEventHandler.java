package com.xk.auth.application.handler.event.corp;

import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.auth.application.action.command.auth.role.DeletedRoleCommand;
import com.xk.auth.application.action.command.auth.role.DeletedRoleItemCommand;
import com.xk.auth.application.action.command.auth.userrole.DeletedUserRoleCommand;
import com.xk.corp.domain.event.user.CorpUserDeleteEvent;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.repository.auth.role.RoleRootQueryRepository;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.domain.service.auth.userrole.UserRoleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpUserDeleteEventHandler extends AbstractEventVerticle<CorpUserDeleteEvent> {

    private final RoleRootQueryRepository roleRootQueryRepository;
    private final RoleRootService roleRootService;
    private final UserRoleRootService userRoleRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<CorpUserDeleteEvent> mono) {

        return mono.flatMap(event -> {

            Supplier<Mono<Long>> deleteUserRole = () -> {
                return userRoleRootService
                        .selectByUserId(UserRoleEntity.builder().userId(event.getUserId()).build())
                        .flatMap(entity -> {
                            DeletedUserRoleCommand command = DeletedUserRoleCommand.builder()
                                    .userRoleId(entity.getUserRoleId()).build();
                            return commandDispatcher
                                    .executeCommand(Mono.just(command),
                                            DeletedUserRoleCommand.class, Void.class)
                                    .then(Mono.just(entity.getRoleId()));
                        });
            };

            Function<Long, Mono<Long>> deleteRole = (roleId) -> {
                return commandDispatcher
                        .executeCommand(Mono.just(DeletedRoleCommand.builder().roleId(roleId).build()),
                                DeletedRoleCommand.class, Void.class)
                        .thenReturn(roleId);
            };

            Function<Long, Mono<Void>> deleteRoleItem = (roleId) -> {
                return roleRootQueryRepository
                        .findRoleItemByRoleId(RoleItemEntity.builder().roleId(roleId).build())
                        .flatMap(roleItem -> commandDispatcher.executeCommand(Mono.just(roleItem),
                                DeletedRoleItemCommand.class, Void.class))
                        .then();
            };

            Function<Throwable, Mono<Void>> onError = (e) -> {
                log.warn("CorpUserDeleteEvent事件处理失败,userId:{}", event.getUserId(), e);
                return Mono.empty();
            };

            return deleteUserRole.get().flatMap(
                    roleId -> deleteRole.apply(roleId).flatMap(deleteRole).flatMap(deleteRoleItem))
                    .onErrorResume(onError);
        });
    }
}
