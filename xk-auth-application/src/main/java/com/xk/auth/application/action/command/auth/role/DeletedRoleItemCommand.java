package com.xk.auth.application.action.command.auth.role;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.interfaces.dto.req.auth.role.DeleteRoleItemReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liuca<PERSON>ong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = RoleItemEntity.class),
        @AutoMapper(target = DeleteRoleItemReqDto.class),
})
public class DeletedRoleItemCommand extends AbstractActionCommand {

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 权限id
     */
    private String itemId;

}
