package com.xk.auth.application.support;

import com.xk.auth.application.commons.XkAuthApplicationErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class XkAuthApplicationException extends ApplicationWrapperThrowable {

    public XkAuthApplicationException(XkAuthApplicationErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkAuthApplicationException(XkAuthApplicationErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
