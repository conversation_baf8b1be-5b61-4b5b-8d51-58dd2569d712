package com.xk.auth.application.handler.command.auth.item;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.auth.application.action.command.auth.item.UpdateItemCommand;
import com.xk.domain.model.auth.item.ItemEntity;
import com.xk.domain.model.auth.item.ItemRoot;
import com.xk.domain.repository.auth.item.ItemRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class UpdateItemHandler implements IActionCommandHandler<UpdateItemCommand, Void> {

    private final ItemRootRepository itemRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<UpdateItemCommand> command) {
        return this.execute(command, ItemEntity.class,
                this.converter::convert,
                (entity) -> ItemRoot.builder()
                        .identifier(StringIdentifier.builder().id(entity.getItemId()).build())
                        .itemEntity(entity)
                        .build(),
                itemRootRepository::update
        );
    }
}
