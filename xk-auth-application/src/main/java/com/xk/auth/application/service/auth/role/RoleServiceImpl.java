package com.xk.auth.application.service.auth.role;

import java.util.Date;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.commons.util.CollectionUtil;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.action.query.auth.group.RoleGroupByIdQuery;
import com.xk.application.action.query.auth.role.RoleByIdQuery;
import com.xk.application.action.query.auth.role.RoleItemByIdQuery;
import com.xk.application.action.query.auth.role.RoleListQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.action.command.auth.group.CreateRoleGroupCommand;
import com.xk.auth.application.action.command.auth.group.DeletedRoleGroupCommand;
import com.xk.auth.application.action.command.auth.role.*;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.auth.interfaces.service.auth.role.RoleService;
import com.xk.domain.model.auth.group.RoleGroupEntity;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.service.auth.group.GroupRootService;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.domain.service.auth.userrole.UserRoleRootService;
import com.xk.interfaces.dto.req.auth.role.*;
import com.xk.interfaces.dto.rsp.auth.group.RoleGroupRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleItemRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 * @CreateTime: 2024-07-15
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final RoleRootService roleRootService;
    private final UserRoleRootService userRoleRootService;

    private final GroupRootService groupRootService;

    /**
     * 添加角色
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> save(Mono<SaveRoleReqDto> dto) {

        // 校验名称是否重复
        Mono<Object> checkMono = dto.flatMap(saveRoleReqDto -> {
            RoleListQuery roleListQuery = new RoleListQuery();
            roleListQuery.setBusinessType(saveRoleReqDto.getBusinessType());
            roleListQuery.setPlatformType(saveRoleReqDto.getPlatformType());
            return actionQueryManyDispatcher
                    .executeQuery(Mono.just(roleListQuery), RoleListQuery.class, RoleRspDto.class)
                    .collectList().flatMap(list -> {
                        if (CollectionUtil.isNullOrEmpty(list)) {
                            return Mono.empty();
                        }
                        if (list.stream().anyMatch(roleEntity -> roleEntity.getRoleName()
                                .equals(saveRoleReqDto.getRoleName()))) {
                            return Mono.error(new XkApplicationException(
                                    XkApplicationErrorEnum.ROLE_NAME_ALREADY_EXIST));
                        }
                        if (list.stream().anyMatch(roleEntity -> roleEntity.getRoleNameEn()
                                .equals(saveRoleReqDto.getRoleNameEn()))) {
                            return Mono.error(new XkApplicationException(
                                    XkApplicationErrorEnum.ROLE_EN_NAME_ALREADY_EXIST));
                        }
                        return Mono.empty();
                    });
        });

        Mono<Void> voidMono = ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> roleRootService.generateRoleId().flatMap(roleId -> {
                    return commandDispatcher.executeCommand(dto, CreateRoleCommand.class,
                            command -> {
                                command.setRoleId(roleId);
                                command.setCreateId(userId);
                                command.setCreateTime(new Date());
                                return command;
                            });
                }));
        return checkMono.then(voidMono);
    }

    /**
     * 添加角色分组关系
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> saveGroupRole(Mono<SaveGroupRoleReqDto> dto) {
        return dto.flatMap(saveGroupRoleReqDto -> {
            return Flux.fromIterable(saveGroupRoleReqDto.getGroupIds()).flatMap(groupId -> {
                // 查询
                RoleGroupByIdQuery byIdQuery = RoleGroupByIdQuery.builder().groupId(groupId)
                        .roleId(saveGroupRoleReqDto.getRoleId()).build();
                return actionQueryDispatcher.executeQuery(Mono.just(byIdQuery),
                        RoleGroupByIdQuery.class, RoleGroupRspDto.class).hasElement()
                        .flatMap(hasElement -> {
                            if (hasElement) {
                                return Mono.empty();
                            }
                            return commandDispatcher.executeCommand(
                                    Mono.just(CreateRoleGroupCommand.builder().groupId(groupId)
                                            .roleId(saveGroupRoleReqDto.getRoleId()).build()),
                                    CreateRoleGroupCommand.class);
                        });
            }).then();
        });
    }

    /**
     * 修改角色
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> update(Mono<UpdateRoleReqDto> dto) {

        // 校验名称是否重复
        Mono<Object> checkMono = dto.flatMap(updateRoleReqDto -> {
            // 查询
            return actionQueryDispatcher.executeQuery(
                    Mono.just(RoleByIdQuery.builder().roleId(updateRoleReqDto.getRoleId()).build()),
                    RoleByIdQuery.class, RoleRspDto.class).flatMap(roleRspDto -> {
                        RoleListQuery roleListQuery = new RoleListQuery();
                        roleListQuery.setBusinessType(roleRspDto.getBusinessType());
                        roleListQuery.setPlatformType(roleRspDto.getPlatformType());
                        return actionQueryManyDispatcher
                                .executeQuery(Mono.just(roleListQuery), RoleListQuery.class,
                                        RoleRspDto.class)
                                .filter(roleEntity -> !roleEntity.getRoleId()
                                        .equals(roleRspDto.getRoleId()))
                                .collectList().flatMap(list -> {
                                    if (CollectionUtil.isNullOrEmpty(list)) {
                                        return Mono.empty();
                                    }
                                    if (list.stream()
                                            .anyMatch(roleEntity -> roleEntity.getRoleName()
                                                    .equals(updateRoleReqDto.getRoleName()))) {
                                        return Mono.error(new XkApplicationException(
                                                XkApplicationErrorEnum.ROLE_NAME_ALREADY_EXIST));
                                    }
                                    if (list.stream()
                                            .anyMatch(roleEntity -> roleEntity.getRoleNameEn()
                                                    .equals(updateRoleReqDto.getRoleNameEn()))) {
                                        return Mono.error(new XkApplicationException(
                                                XkApplicationErrorEnum.ROLE_EN_NAME_ALREADY_EXIST));
                                    }
                                    return Mono.empty();
                                });
                    });
        });


        Mono<Void> voidMono = ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            return commandDispatcher.executeCommand(dto, UpdateRoleCommand.class, command -> {
                command.setUpdateId(userId);
                command.setUpdateTime(new Date());
                return command;
            });
        });
        return checkMono.then(voidMono);
    }

    /**
     * 删除角色
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> delete(Mono<RoleIdReqDto> dto) {

        return commandDispatcher.executeCommand(dto, DeletedRoleCommand.class)
                // 删除角色权限
                .then(Mono.defer(() -> {
                    return dto.flatMap(req -> {
                        return roleRootService
                                .findRoleItemByRoleId(
                                        RoleItemEntity.builder().roleId(req.getRoleId()).build())
                                .flatMap(itemEntity -> {
                                    return commandDispatcher.executeCommand(
                                            Mono.just(DeletedRoleItemCommand.builder()
                                                    .roleId(itemEntity.getRoleId())
                                                    .itemId(itemEntity.getItemId()).build()),
                                            DeletedRoleItemCommand.class);
                                }).then(Mono.just(req.getRoleId()));
                    });
                }))
                // 删除用户角色
                .flatMap(roleId -> {
                    return userRoleRootService
                            .selectByRoleId(UserRoleEntity.builder().roleId(roleId).build())
                            .flatMap(userRoleEntity -> {
                                return commandDispatcher
                                        .executeCommand(
                                                Mono.just(UpdateUserRoleCommand.builder()
                                                        .userRoleId(userRoleEntity.getUserRoleId())
                                                        .roleId(0L).build()),
                                                UpdateUserRoleCommand.class);
                            }).then(Mono.just(roleId));
                })
                // 删除角色分组
                .flatMap(roleId -> {
                    return groupRootService
                            .findRoleGroupByRoleId(RoleGroupEntity.builder().roleId(roleId).build())
                            .flatMap(roleGroupEntity -> {
                                return commandDispatcher.executeCommand(
                                        Mono.just(DeletedRoleGroupCommand.builder().roleId(roleId)
                                                .groupId(roleGroupEntity.getGroupId()).build()),
                                        DeletedRoleGroupCommand.class);
                            }).then();
                });
    }

    /**
     * 添加角色权限关系
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> saveRoleItem(Mono<SaveRoleItemReqDto> dto) {
        return dto.flatMap(saveRoleItemReqDto -> {
            return Flux.fromIterable(saveRoleItemReqDto.getItemIds()).flatMap(itemId -> {
                return actionQueryDispatcher
                        .executeQuery(
                                Mono.just(RoleItemByIdQuery.builder().itemId(itemId)
                                        .roleId(saveRoleItemReqDto.getRoleId()).build()),
                                RoleItemByIdQuery.class, RoleItemRspDto.class)
                        .hasElement().flatMap(hasElement -> {
                            if (hasElement) {
                                return Mono.empty();
                            }
                            return commandDispatcher.executeCommand(
                                    Mono.just(CreateRoleItemCommand.builder().itemId(itemId)
                                            .roleId(saveRoleItemReqDto.getRoleId()).build()),
                                    CreateRoleItemCommand.class);
                        });
            }).then();
        });
    }

    /**
     * 删除角色权限关系
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> deleteRoleItem(Mono<DeleteRoleItemReqDto> dto) {
        return commandDispatcher.executeCommand(dto, DeletedRoleItemCommand.class);
    }
}
