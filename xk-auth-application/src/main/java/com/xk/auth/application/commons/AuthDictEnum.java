package com.xk.auth.application.commons;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AuthDictEnum implements DictObjectEnum {

    BASE_CORP_MANAGER_AUTH("-1"),
    BASE_CORP_USER_AUTH("-1")
    ;

    private static final Map<String, AuthDictEnum> MAP;

    static {
        MAP = Arrays.stream(AuthDictEnum.values())
                .collect(Collectors.toMap(AuthDictEnum::name, enumValue -> enumValue));
    }

    private final String defaultValue;

    public static AuthDictEnum getEnum(String name) {
        return MAP.get(name);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return name();
    }
}
