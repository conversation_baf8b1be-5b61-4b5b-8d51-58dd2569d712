package com.xk.auth.application.handler.command.auth.role;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.role.CreateRoleCommand;
import com.xk.domain.model.auth.role.RoleEntity;
import com.xk.domain.model.auth.role.RoleRoot;
import com.xk.domain.repository.auth.role.RoleRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class CreateRoleHandler implements IActionCommandHandler<CreateRoleCommand, Void> {

    private final Converter converter;

    private final RoleRootRepository roleRootRepository;


    @Override
    public Mono<Void> execute(Mono<CreateRoleCommand> command) {
        return this.execute(command, RoleEntity.class,
                this.converter::convert,
                (entity) -> RoleRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getRoleId()).build())
                        .roleEntity(entity)
                        .build(),
                roleRootRepository::save
        );
    }
}
