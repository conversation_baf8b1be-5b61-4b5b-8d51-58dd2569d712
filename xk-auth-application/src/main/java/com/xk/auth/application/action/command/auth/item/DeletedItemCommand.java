package com.xk.auth.application.action.command.auth.item;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.item.ItemEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.item.ItemIdReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = ItemEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = ItemIdReqDto.class),
})
public class DeletedItemCommand extends AbstractActionCommand {

    /**
     * 权限id
     */
    private String itemId;
}
