package com.xk.auth.application.handler.command.auth.group;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.group.CreateRoleGroupCommand;
import com.xk.domain.model.auth.group.GroupRoot;
import com.xk.domain.model.auth.group.RoleGroupEntity;
import com.xk.domain.repository.auth.group.GroupRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateRoleGroupHandler implements IActionCommandHandler<CreateRoleGroupCommand, Void> {

    private final GroupRootRepository groupRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateRoleGroupCommand> command) {
        return this.execute(command, RoleGroupEntity.class,
                this.converter::convert,
                (entity) -> GroupRoot.builder().identifier(LongIdentifier.builder().id(entity.getGroupId()).build())
                        .roleGroupEntities(Collections.singletonList(entity)).build(),
                groupRootRepository::saveRoleGroup
        );
    }
}
