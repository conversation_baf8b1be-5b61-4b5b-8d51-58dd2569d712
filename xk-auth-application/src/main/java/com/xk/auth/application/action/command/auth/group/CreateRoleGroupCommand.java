package com.xk.auth.application.action.command.auth.group;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.group.RoleGroupEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = RoleGroupEntity.class)
})
public class CreateRoleGroupCommand extends AbstractActionCommand {

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 分组id
     */
    private Long groupId;
}
