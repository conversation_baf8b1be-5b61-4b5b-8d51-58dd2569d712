package com.xk.auth.application.service.auth.userrole;

import java.util.Date;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.action.query.auth.group.GroupByIdQuery;
import com.xk.application.action.query.auth.role.RoleByIdQuery;
import com.xk.application.action.query.auth.userrole.UserRoleByUserIdQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.action.command.auth.userrole.CreateUserRoleCommand;
import com.xk.auth.application.action.command.auth.userrole.DeletedUserRoleCommand;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.auth.interfaces.service.auth.userrole.UserRoleService;
import com.xk.enums.auth.MenuAuthStatusEnum;
import com.xk.domain.service.auth.userrole.UserRoleRootService;
import com.xk.interfaces.dto.req.auth.userrole.UpdateUserRoleReqDto;
import com.xk.interfaces.dto.rsp.auth.group.GroupRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleRspDto;
import com.xk.interfaces.dto.rsp.auth.userrole.UserRoleRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 * @CreateTime: 2024-07-15
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class UserRoleServiceImpl implements UserRoleService {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final UserRoleRootService userRoleRootService;

    /**
     * 更新用户角色
     *
     * @param dto
     * @return
     */
    @BusiCode
    @Override
    public Mono<Void> updateUserRole(Mono<UpdateUserRoleReqDto> dto) {
        return dto.flatMap(updateUserRoleReqDto -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            if (updateUserRoleReqDto.getGroupId() == null && updateUserRoleReqDto.getRoleId() == null) {
                return Mono.empty();
            }
            //校验
            Mono<Void> checkGroupMono = updateUserRoleReqDto.getGroupId() == null ? Mono.empty() : actionQueryDispatcher.executeQuery(
                            Mono.just(GroupByIdQuery.builder().groupId(updateUserRoleReqDto.getGroupId()).build()),
                            GroupByIdQuery.class, GroupRspDto.class)
                    .switchIfEmpty(Mono.error(new XkApplicationException(XkApplicationErrorEnum.GROUP_NOT_EXIST)))
                    .flatMap(groupRspDto -> {
                        if (!Objects.equals(groupRspDto.getPlatformType(), PlatformTypeEnum.PC_BOSS_OMS.getValue())) {
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                    new RuntimeException("分组平台类型错误")));
                        }
                        return Mono.empty();
                    });

            Mono<Void> checkRoleMono = updateUserRoleReqDto.getRoleId() == null ? Mono.empty() : actionQueryDispatcher.executeQuery(
                            Mono.just(RoleByIdQuery.builder().roleId(updateUserRoleReqDto.getRoleId()).build()),
                            RoleByIdQuery.class, RoleRspDto.class)
                    .switchIfEmpty(Mono.error(new XkApplicationException(XkApplicationErrorEnum.ROLE_NOT_EXIST)))
                    .flatMap(roleRspDto -> {
                        if (!Objects.equals(roleRspDto.getPlatformType(), PlatformTypeEnum.PC_BOSS_OMS.getValue())) {
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                    new RuntimeException("角色平台类型错误")));
                        }
                        return Mono.empty();
                    });


            Mono<Void> voidMono = actionQueryDispatcher.executeQuery(
                            Mono.just(UserRoleByUserIdQuery.builder().userId(updateUserRoleReqDto.getUserId()).build()),
                            UserRoleByUserIdQuery.class, UserRoleRspDto.class)
                    .switchIfEmpty(userRoleRootService.generateUserRoleId()
                            .flatMap(userRoleId -> commandDispatcher.executeCommand(dto, CreateUserRoleCommand.class,
                                    command -> {
                                        command.setCreateId(userId);
                                        command.setCreateTime(new Date());
                                        command.setUserRoleId(userRoleId);
                                        command.setStatus(MenuAuthStatusEnum.NORMAL.getCode());
                                        return command;
                                    }).then(Mono.empty())))
                    .flatMap(userRoleRspDto -> {
                        UpdateUserRoleCommand userRoleCommand = UpdateUserRoleCommand.builder()
                                .userRoleId(userRoleRspDto.getUserRoleId())
                                .groupId(updateUserRoleReqDto.getGroupId())
                                .roleId(updateUserRoleReqDto.getRoleId())
                                .status(MenuAuthStatusEnum.NORMAL.getCode())
                                .updateId(userId)
                                .updateTime(new Date())
                                .build();
                        return commandDispatcher.executeCommand(Mono.just(userRoleCommand),
                                UpdateUserRoleCommand.class);
                    });
            return checkGroupMono.then(checkRoleMono).then(voidMono);
        }));
    }

    /**
     * 删除用户角色
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> deleteUserRole(Mono<UpdateUserRoleReqDto> dto) {
        return dto.flatMap(updateUserRoleReqDto -> {
            return actionQueryDispatcher.executeQuery(
                            Mono.just(UserRoleByUserIdQuery.builder().userId(updateUserRoleReqDto.getUserId()).build()),
                            UserRoleByUserIdQuery.class, UserRoleRspDto.class)
                    .flatMap(userRoleRspDto -> {
                        DeletedUserRoleCommand userRoleCommand = DeletedUserRoleCommand.builder()
                                .userRoleId(userRoleRspDto.getUserRoleId())
                                .build();
                        return commandDispatcher.executeCommand(Mono.just(userRoleCommand),
                                DeletedUserRoleCommand.class);
                    });
        });
    }
}
