package com.xk.auth.application.handler.command.auth.role;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.role.DeletedRoleItemCommand;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.model.auth.role.RoleRoot;
import com.xk.domain.repository.auth.role.RoleRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeletedRoleItemHandler implements IActionCommandHandler<DeletedRoleItemCommand, Void> {

    private final RoleRootRepository roleRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedRoleItemCommand> command) {
        return this.execute(command, RoleItemEntity.class,
                this.converter::convert,
                (entity) -> RoleRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getRoleId()).build())
                        .roleItemEntities(List.of(entity))
                        .build(),
                roleRootRepository::removeRoleItem
        );
    }
}
