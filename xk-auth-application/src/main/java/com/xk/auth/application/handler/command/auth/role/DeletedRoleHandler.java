package com.xk.auth.application.handler.command.auth.role;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.role.DeletedRoleCommand;
import com.xk.domain.model.auth.role.RoleEntity;
import com.xk.domain.model.auth.role.RoleRoot;
import com.xk.domain.repository.auth.role.RoleRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class DeletedRoleHandler implements IActionCommandHandler<DeletedRoleCommand, Void> {

    private final RoleRootRepository roleRootRepository;

    private final Converter converter;


    @Override
    public Mono<Void> execute(Mono<DeletedRoleCommand> command) {
        return this.execute(command, RoleEntity.class, converter::convert,
                entity -> RoleRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getRoleId()).build())
                        .build(),
                roleRootRepository::remove);
    }

}
