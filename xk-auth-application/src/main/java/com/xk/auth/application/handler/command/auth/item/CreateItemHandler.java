package com.xk.auth.application.handler.command.auth.item;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.auth.application.action.command.auth.item.CreateItemCommand;
import com.xk.domain.model.auth.item.ItemEntity;
import com.xk.domain.model.auth.item.ItemRoot;
import com.xk.domain.repository.auth.item.ItemRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class CreateItemHandler implements IActionCommandHandler<CreateItemCommand, Void> {

    private final Converter converter;

    private final ItemRootRepository itemRootRepository;


    @Override
    public Mono<Void> execute(Mono<CreateItemCommand> command) {
        return this.execute(command, ItemEntity.class,
                this.converter::convert,
                (ItemEntity) -> ItemRoot.builder()
                        .identifier(StringIdentifier.builder().id(ItemEntity.getItemId()).build())
                        .itemEntity(ItemEntity)
                        .build(),
                itemRootRepository::save
        );
    }
}
