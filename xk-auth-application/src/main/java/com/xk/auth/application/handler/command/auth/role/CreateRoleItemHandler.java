package com.xk.auth.application.handler.command.auth.role;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.role.CreateRoleItemCommand;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.model.auth.role.RoleRoot;
import com.xk.domain.repository.auth.role.RoleRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateRoleItemHandler implements IActionCommandHandler<CreateRoleItemCommand, Void> {

    private final RoleRootRepository roleRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateRoleItemCommand> command) {
        return this.execute(command, RoleItemEntity.class,
                this.converter::convert,
                (entity) -> RoleRoot.builder().identifier(LongIdentifier.builder().id(entity.getRoleId()).build())
                        .roleItemEntities(Collections.singletonList(entity)).build(),
                roleRootRepository::saveRoleItem
        );
    }
}
