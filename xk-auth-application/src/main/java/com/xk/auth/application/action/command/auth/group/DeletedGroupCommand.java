package com.xk.auth.application.action.command.auth.group;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.group.GroupEntity;
import com.xk.interfaces.dto.req.auth.group.GroupIdReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON>ong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = GroupEntity.class),
        @AutoMapper(target = GroupIdReqDto.class),
})
public class DeletedGroupCommand extends AbstractActionCommand {

    private Long groupId;
}
