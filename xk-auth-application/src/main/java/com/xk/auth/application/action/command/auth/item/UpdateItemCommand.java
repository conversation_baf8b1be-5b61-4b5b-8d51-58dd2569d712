package com.xk.auth.application.action.command.auth.item;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.item.ItemEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.item.UpdateItemReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = ItemEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = UpdateItemReqDto.class),
})
public class UpdateItemCommand extends AbstractActionCommand {

    /**
     * 权限id
     */
    private String itemId;

    /**
     * 名称
     */
    private String itemName;

    /**
     * 英文名称
     */
    private String itemNameEn;

    /**
     * 描述
     */
    private String itemDesc;

    /**
     * 接口所属服务
     */
    private String serverName;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 是否显示uri(1 显示 0 不显示)
     */
    private Integer showUri;

    /**
     * 在菜单里是否显示(1 显示 0 不显示）
     */
    private Integer showInMenu;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 位置
     */
    private String itemPosition;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 是否记录日志 0否 1是
     */
    private Integer isLog;
    /**
     * 是否验证 0否 1是
     */
    private Integer isAuth;
}
