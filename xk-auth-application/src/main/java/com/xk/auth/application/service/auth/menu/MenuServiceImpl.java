package com.xk.auth.application.service.auth.menu;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.application.action.query.auth.item.ItemByIdQuery;
import com.xk.application.action.query.auth.menu.MenuByIdQuery;
import com.xk.application.action.query.auth.menu.MenuItemByIdQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.action.command.auth.menu.*;
import com.xk.auth.application.action.command.auth.role.CreateRoleItemCommand;
import com.xk.auth.application.action.command.auth.role.DeletedRoleItemCommand;
import com.xk.auth.interfaces.service.auth.menu.MenuService;
import com.xk.domain.model.auth.menu.MenuEntity;
import com.xk.domain.model.auth.menu.MenuItemEntity;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.service.auth.menu.MenuRootService;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.enums.auth.MenuAuthStatusEnum;
import com.xk.interfaces.dto.req.auth.menu.*;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuItemRspDto;
import com.xk.interfaces.dto.rsp.auth.menu.MenuRspDto;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 * @CreateTime: 2024-07-15
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final IdentifierGenerateService identifierGenerateService;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final MenuRootService menuRootService;
    private final RoleRootService roleRootService;
    private final EventRootService eventRootService;

    /**
     * 添加菜单
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> save(Mono<SaveMenuReqDto> dto) {
        return dto.flatMap(saveMenuReqDto -> {
            Mono<Void> checkParentMono = saveMenuReqDto.getParentId() == null ? Mono.empty()
                    : actionQueryDispatcher
                            .executeQuery(
                                    Mono.just(MenuByIdQuery.builder()
                                            .menuId(saveMenuReqDto.getParentId()).build()),
                                    MenuByIdQuery.class, MenuRspDto.class)
                            .switchIfEmpty(Mono.error(new XkApplicationException(
                                    XkApplicationErrorEnum.PARENT_ID_NOT_EXIST)))
                            .flatMap(menuRspDto -> {
                                if (!Objects.equals(menuRspDto.getPlatformType(),
                                        saveMenuReqDto.getPlatformType()) || !Objects.equals(
                                                menuRspDto.getBusinessType(),
                                                saveMenuReqDto.getBusinessType())) {
                                    return Mono.error(new XkApplicationException(
                                            XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                            new RuntimeException("父级菜单平台类型或业务类型错误")));
                                }
                                return Mono.empty();
                            });
            Mono<Void> saveMenuMono = ReadSynchronizationUtils.getUserIdMono()
                    .flatMap(userId -> menuRootService.generateMenuId().flatMap(menuId -> {
                        return commandDispatcher.executeCommand(dto, CreateMenuCommand.class,
                                command -> {
                                    command.setMenuId(menuId);
                                    command.setStatus(MenuAuthStatusEnum.NORMAL.getCode());
                                    command.setCreateId(userId);
                                    command.setCreateTime(new Date());
                                    if (command.getParentId() == null) {
                                        command.setParentId(0L);
                                    }
                                    return command;
                                });
                    }));
            return checkParentMono.then(saveMenuMono);
        });
    }

    /**
     * 添加菜单权限关联
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> saveMenuItem(Mono<SaveMenuItemReqDto> dto) {
        return dto.flatMap(saveMenuItemReqDto -> {
            return actionQueryDispatcher.executeQuery(
                    Mono.just(
                            MenuByIdQuery.builder().menuId(saveMenuItemReqDto.getMenuId()).build()),
                    MenuByIdQuery.class, MenuRspDto.class).flatMap(menuRspDto -> {
                        return Flux.fromIterable(saveMenuItemReqDto.getItemIds())
                                .flatMap(itemId -> {
                                    return actionQueryDispatcher.executeQuery(
                                            Mono.just(
                                                    ItemByIdQuery.builder().itemId(itemId).build()),
                                            ItemByIdQuery.class, ItemRspDto.class)
                                            .flatMap(itemRspDto -> {
                                                if (!Objects.equals(menuRspDto.getPlatformType(),
                                                        itemRspDto.getPlatformType()) || !Objects
                                                                .equals(menuRspDto
                                                                        .getBusinessType(),
                                                                        itemRspDto
                                                                                .getBusinessType())) {
                                                    return Mono.error(new XkApplicationException(
                                                            XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                                            new RuntimeException("权限平台类型或业务类型错误")));
                                                }
                                                // 查询
                                                MenuItemByIdQuery byIdQuery = MenuItemByIdQuery
                                                        .builder().itemId(itemId)
                                                        .menuId(saveMenuItemReqDto.getMenuId())
                                                        .build();
                                                return actionQueryDispatcher
                                                        .executeQuery(Mono.just(byIdQuery),
                                                                MenuItemByIdQuery.class,
                                                                MenuItemRspDto.class)
                                                        .hasElement().flatMap(hasElement -> {
                                                            if (hasElement) {
                                                                return Mono.empty();
                                                            }
                                                            return commandDispatcher.executeCommand(
                                                                    Mono.just(CreateMenuItemCommand
                                                                            .builder()
                                                                            .menuId(saveMenuItemReqDto
                                                                                    .getMenuId())
                                                                            .itemId(itemId)
                                                                            .build()),
                                                                    CreateMenuItemCommand.class);
                                                        });
                                            });

                                }).then();
                    });
        });
    }

    /**
     * 删除菜单权限关联
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> deleteMenuItem(Mono<MenuItemReqDto> dto) {
        return dto.flatMap(req -> {
            Set<Long> menuIds = new HashSet<>(); // 用于收集所有的 menuId
            // 初始化队列，存储待处理的菜单 ID
            Queue<Long> menuQueue = new LinkedList<>();
            menuQueue.add(req.getMenuId());

            return Flux.generate(() -> menuQueue, (queue, sink) -> {
                Long currentMenuId = queue.poll();
                if (currentMenuId == null) {
                    sink.complete(); // 队列为空，终止流
                } else {
                    sink.next(currentMenuId); // 发出当前菜单 ID
                }
                return queue;
            }).flatMap(menuId -> {
                menuIds.add((Long) menuId); // 收集当前菜单 ID

                // 查找子菜单并加入队列
                return menuRootService
                        .findByParentId(MenuEntity.builder().parentId((Long) menuId).build())
                        .doOnNext(subMenu -> {
                            if (!menuIds.contains(subMenu.getMenuId())) { // 防止重复添加
                                menuQueue.add(subMenu.getMenuId()); // 子菜单加入队列
                            }
                        }).then();
            }, 1) // 确保顺序执行
                    .then(Mono.defer(() -> {
                        // 在所有菜单 ID 收集完成后执行删除
                        return Flux.fromIterable(menuIds).flatMap(menuId -> {
                            return actionQueryDispatcher
                                    .executeQuery(
                                            Mono.just(MenuItemByIdQuery.builder().menuId(menuId)
                                                    .itemId(req.getItemId()).build()),
                                            MenuItemByIdQuery.class, MenuItemRspDto.class)
                                    .flatMap(menuItemRspDto -> {
                                        DeletedMenuItemCommand command =
                                                DeletedMenuItemCommand.builder().menuId(menuId)
                                                        .itemId(req.getItemId()).build();
                                        return commandDispatcher.executeCommand(Mono.just(command),
                                                DeletedMenuItemCommand.class);

                                    });
                        }) // 顺序删除每个菜单
                                .then();
                    }));
        });
    }

    @BusiCode
    @Override
    public Mono<Void> saveMenuByRoleId(Mono<MenuRoleReqDto> mono) {
        return mono.flatMap(dto -> {
            Supplier<Mono<List<MenuItemEntity>>> getCurrentUserMenuItem =
                    () -> menuRootService.findMenuItemBySession().collectList();

            Supplier<Mono<Void>> removeUserRole = () -> roleRootService
                    .findRoleItemByRoleId(RoleItemEntity.builder().roleId(dto.getRoleId()).build())
                    .flatMap(roleItem -> {
                        return commandDispatcher.executeCommand(Mono.just(roleItem),
                                DeletedRoleItemCommand.class);
                    }).collectList().then();

            Function<List<MenuItemEntity>, Mono<Void>> saveUserRole = (list) -> {
                //已经添加的权限不再添加,正常不会出现
                Set<String> processedIds = ConcurrentHashMap.newKeySet();
                return Flux.fromIterable(list).flatMap(menuItem -> {
                    Set<Long> menuIds = dto.getMenuIds();
                    if (!CollectionUtils.contains(menuIds, menuItem.getMenuId()) || !processedIds
                            .add(menuItem.getItemId())) {
                        return Mono.empty();
                    }

                    CreateRoleItemCommand command = CreateRoleItemCommand.builder()
                            .itemId(menuItem.getItemId()).roleId(dto.getRoleId()).build();
                    return commandDispatcher.executeCommand(Mono.just(command),
                            CreateRoleItemCommand.class);
                }).then();
            };

            return removeUserRole.get()
                    .then(getCurrentUserMenuItem.get().flatMap(saveUserRole).then());
        });
    }

    /**
     * 修改菜单
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> update(Mono<UpdateMenuReqDto> dto) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            return commandDispatcher.executeCommand(dto, UpdateMenuCommand.class, command -> {
                command.setUpdateId(userId);
                command.setUpdateTime(new Date());
                return command;
            });
        });
    }

    /**
     * 删除菜单
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> delete(Mono<MenuIdReqDto> dto) {
        return dto.flatMap(req -> {
            Set<Long> menuIds = new HashSet<>(); // 用于收集所有的 menuId

            // 初始化队列，存储待处理的菜单 ID
            Queue<Long> menuQueue = new LinkedList<>();
            menuQueue.add(req.getMenuId());

            return Flux.generate(() -> menuQueue, (queue, sink) -> {
                Long currentMenuId = queue.poll();
                if (currentMenuId == null) {
                    sink.complete(); // 队列为空，终止流
                } else {
                    sink.next(currentMenuId); // 发出当前菜单 ID
                }
                return queue;
            }).flatMap(menuId -> {
                menuIds.add((Long) menuId); // 收集当前菜单 ID

                // 查找子菜单并加入队列
                return menuRootService
                        .findByParentId(MenuEntity.builder().parentId((Long) menuId).build())
                        .doOnNext(subMenu -> {
                            if (!menuIds.contains(subMenu.getMenuId())) { // 防止重复添加
                                menuQueue.add(subMenu.getMenuId()); // 子菜单加入队列
                            }
                        }).then();
            }, 1) // 确保顺序执行
                    .then(Mono.defer(() -> {
                        // 在所有菜单 ID 收集完成后执行删除
                        return Flux.fromIterable(menuIds).flatMap(this::deleteSingleMenuAndItems) // 顺序删除每个菜单
                                .then();
                    }));
        });
    }

    private Mono<Void> deleteSingleMenuAndItems(Long menuId) {
        Mono<Void> deletedMenu = commandDispatcher.executeCommand(
                Mono.just(DeletedMenuCommand.builder().menuId(menuId).build()),
                DeletedMenuCommand.class);
        Mono<Void> deletedMenus = menuRootService
                .findMenuItemByMenuId(MenuItemEntity.builder().menuId(menuId).build())
                .flatMap(menuItem -> {
                    return commandDispatcher.executeCommand(
                            Mono.just(DeletedMenuItemCommand.builder().menuId(menuItem.getMenuId())
                                    .itemId(menuItem.getItemId()).build()),
                            DeletedMenuItemCommand.class);
                }).then();
        return deletedMenu.then(deletedMenus);
    }
}
