package com.xk.auth.application.action.command.auth.group;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.group.RoleGroupEntity;
import com.xk.interfaces.dto.req.auth.group.RoleGroupReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON>ong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = RoleGroupEntity.class),
        @AutoMapper(target = RoleGroupReqDto.class),
})
public class DeletedRoleGroupCommand extends AbstractActionCommand {

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 分组id
     */
    private Long groupId;
}
