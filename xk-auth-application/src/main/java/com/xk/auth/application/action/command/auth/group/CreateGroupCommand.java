package com.xk.auth.application.action.command.auth.group;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.group.GroupEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.group.SaveGroupReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = GroupEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = SaveGroupReqDto.class),
})
public class CreateGroupCommand extends AbstractActionCommand {

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组英文名称
     */
    private String nameEn;

    /**
     * 显示状态；1显示 0不显示
     */
    private Integer isEnabled;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 分组类型（1:角色，2：功能）
     */
    private Integer groupType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;
}
