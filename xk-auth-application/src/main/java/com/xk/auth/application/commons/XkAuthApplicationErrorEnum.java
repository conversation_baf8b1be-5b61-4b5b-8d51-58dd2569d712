package com.xk.auth.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum XkAuthApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),
    CORP_USER_ROLE_NOT_CONFIG(13001, "商户成员默认角色没有配置"),
    PLATFORM_ERROR(13002, "接口平台不一致"),

    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
