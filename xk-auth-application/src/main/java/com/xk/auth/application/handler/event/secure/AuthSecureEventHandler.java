package com.xk.auth.application.handler.event.secure;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.application.action.query.auth.group.RoleGroupByGroupIdQuery;
import com.xk.application.action.query.auth.item.ItemByIdQuery;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.commons.XkAuthApplicationErrorEnum;
import com.xk.auth.application.support.XkAuthApplicationException;
import com.xk.auth.domain.event.auth.log.AuthLogEvent;
import com.xk.auth.enums.YesOrNotEnum;
import com.xk.domain.model.auth.item.ItemRoot;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.repository.auth.item.ItemRootQueryRepository;
import com.xk.infrastructure.data.po.auth.item.AuthItem;
import com.xk.interfaces.dto.rsp.auth.group.RoleGroupRspDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import com.xk.interfaces.dto.rsp.auth.userrole.UserRoleRspDto;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.session.Session;
import com.myco.mydata.domain.service.session.SessionRootDomainService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.event.auth.AuthSecureEvent;
import com.xk.domain.service.auth.role.RoleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 权限验证事件处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthSecureEventHandler extends AbstractEventVerticle<AuthSecureEvent> {

    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    private final RoleRootService roleRootService;

    private final SessionRootDomainService sessionRootDomainService;

    private final ItemRootQueryRepository itemRootQueryRepository;

    private EventRootService eventRootService;

    @Override
    public Mono<Void> handle(Mono<AuthSecureEvent> eventMono) {

        // 1、发送打印日志的事件
        Function<String, Mono<Void>> publishEvent = logInfo -> {
            EventRoot eventRoot = EventRoot.builder().domainEvent(AuthLogEvent.builder().logInfo(logInfo).build()).build();
            return eventRootService.publisheByMono(eventRoot).then().doOnSuccess(event ->
                    log.info("AuthLogEvent 事件发布完成 {}", logInfo));
        };

        return eventMono.flatMap(event -> sessionRootDomainService
                .getSession(StringIdentifier.builder().id(event.getSessionId()).build())
                .flatMap(session -> {
                    // 生成ItemId
                    String itemId = ItemRoot.generateItemId(session.getPlatformType().getValue(), event.getUri());
                    return actionQueryDispatcher.executeQuery(Mono.just(ItemByIdQuery.builder().itemId(itemId).build()), ItemByIdQuery.class, ItemRspDto.class)
                            .flatMap(itemRspDto -> {
                                if (itemRspDto == null || !Objects.equals(itemRspDto.getPlatformType(), session.getPlatformType().getValue())) {
                                    return Mono.error(new XkAuthApplicationException(XkAuthApplicationErrorEnum.PLATFORM_ERROR));
                                }
                                // 检查是否需要打印日志
                                if (itemRspDto.getIsLog() == YesOrNotEnum.YES.getCode()) {
                                    String logInfo = String.format("接口调用，平台：%s,路径：%s", session.getPlatformType().name(), event.getUri());
                                    return publishEvent.apply(logInfo);
                                }
                                return Mono.empty();
                            });
                }));
    }
}
