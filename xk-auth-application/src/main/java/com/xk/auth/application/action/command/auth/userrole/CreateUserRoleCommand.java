package com.xk.auth.application.action.command.auth.userrole;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.interfaces.dto.req.auth.userrole.UpdateUserRoleReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = UserRoleEntity.class),
        @AutoMapper(target = UpdateUserRoleReqDto.class),
})
public class CreateUserRoleCommand extends AbstractActionCommand {

    /**
     * 用户授权id
     */
    private Long userRoleId;

    /**
     * 用户UID
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 分组id
     */
    private Long groupId;

    /**
     * 描述
     */
    private String authorityDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;
}
