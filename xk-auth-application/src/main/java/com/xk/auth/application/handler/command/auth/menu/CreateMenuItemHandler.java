package com.xk.auth.application.handler.command.auth.menu;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.menu.CreateMenuItemCommand;
import com.xk.domain.model.auth.menu.MenuItemEntity;
import com.xk.domain.model.auth.menu.MenuRoot;
import com.xk.domain.repository.auth.menu.MenuRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateMenuItemHandler implements IActionCommandHandler<CreateMenuItemCommand, Void> {

    private final MenuRootRepository menuRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateMenuItemCommand> command) {
        return this.execute(command, MenuItemEntity.class,
                this.converter::convert,
                (entity) -> MenuRoot.builder().identifier(LongIdentifier.builder().id(entity.getMenuId()).build())
                        .menuItemEntities(Collections.singletonList(entity)).build(),
                menuRootRepository::saveMenuItem
        );
    }
}
