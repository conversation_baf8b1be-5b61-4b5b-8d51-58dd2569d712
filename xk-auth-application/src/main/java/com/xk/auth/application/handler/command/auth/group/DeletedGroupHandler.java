package com.xk.auth.application.handler.command.auth.group;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.group.DeletedGroupCommand;
import com.xk.domain.model.auth.group.GroupEntity;
import com.xk.domain.model.auth.group.GroupRoot;
import com.xk.domain.repository.auth.group.GroupRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class DeletedGroupHandler implements IActionCommandHandler<DeletedGroupCommand, Void> {

    private final GroupRootRepository groupRootRepository;

    private final Converter converter;


    @Override
    public Mono<Void> execute(Mono<DeletedGroupCommand> command) {
        return this.execute(command, GroupEntity.class, converter::convert,
                entity -> GroupRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getGroupId()).build())
                        .build(),
                groupRootRepository::remove);
    }

}
