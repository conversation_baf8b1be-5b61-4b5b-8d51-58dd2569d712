package com.xk.auth.application.action.command.auth.role;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.role.RoleEntity;
import com.xk.interfaces.dto.req.auth.role.RoleIdReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = RoleEntity.class),
        @AutoMapper(target = RoleIdReqDto.class),
})
public class DeletedRoleCommand extends AbstractActionCommand {

    /**
     * 角色id
     */
    private Long roleId;
}
