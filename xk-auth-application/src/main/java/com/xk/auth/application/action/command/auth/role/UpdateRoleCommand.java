package com.xk.auth.application.action.command.auth.role;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.role.RoleEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.role.UpdateRoleReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * @Author: liuca<PERSON>ong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = RoleEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = UpdateRoleReqDto.class),
})
public class UpdateRoleCommand extends AbstractActionCommand {

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色英文名称
     */
    private String roleNameEn;

    /**
     * 描述
     */
    private String roleDesc;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 排序
     */
    private Integer sort;

    private Date createTime;

    private Long createId;

    private Date updateTime;

    private Long updateId;

    /**
     * 1:正常；2：停用：3：删除
     */
    private Integer status;
}
