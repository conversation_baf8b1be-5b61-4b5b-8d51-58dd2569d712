package com.xk.auth.application.handler.event.corp;

import java.util.Date;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.LongFunction;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.util.StringUtils;
import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.auth.application.action.command.auth.role.CreateRoleCommand;
import com.xk.auth.application.action.command.auth.role.CreateRoleItemCommand;
import com.xk.auth.application.action.command.auth.userrole.CreateUserRoleCommand;
import com.xk.auth.application.commons.AuthDictEnum;
import com.xk.auth.application.commons.XkAuthApplicationErrorEnum;
import com.xk.auth.application.support.XkAuthApplicationException;
import com.xk.auth.enums.corp.CorpUserRoleEnum;
import com.xk.corp.domain.event.user.CorpUserCreateEvent;
import com.xk.domain.model.auth.role.RoleEntity;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.repository.auth.role.RoleRootQueryRepository;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.domain.service.auth.userrole.UserRoleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpUserCreateEventHandler extends AbstractEventVerticle<CorpUserCreateEvent> {

    private final DictObjectDomainService dictObjectDomainService;
    private final RoleRootQueryRepository roleRootQueryRepository;
    private final RoleRootService roleRootService;
    private final UserRoleRootService userRoleRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<CorpUserCreateEvent> mono) {

        return mono.flatMap(event -> {
            Supplier<Mono<Long>> queryUserAuth = () -> {
                DictObjectEnum objectEnum = CorpUserRoleEnum.MANAGER.equals(event.getCorpUserRole())
                        ? AuthDictEnum.BASE_CORP_MANAGER_AUTH
                        : AuthDictEnum.BASE_CORP_USER_AUTH;
                return dictObjectDomainService.getSystemConfigToLong(objectEnum)
                        .switchIfEmpty(Mono.error(new XkAuthApplicationException(
                                XkAuthApplicationErrorEnum.CORP_USER_ROLE_NOT_CONFIG)));
            };

            LongFunction<Mono<RoleEntity>> queryBaseRole = baseRoleId -> roleRootQueryRepository
                    .findById(LongIdentifier.builder().id(baseRoleId).build())
                    .switchIfEmpty(Mono.error(new XkAuthApplicationException(
                            XkAuthApplicationErrorEnum.CORP_USER_ROLE_NOT_CONFIG)));

            Function<RoleEntity, Mono<Long>> createRole =
                    roleEntity -> roleRootService.generateRoleId().flatMap(
                            roleId -> commandDispatcher.executeCommand(Mono.just(roleEntity),
                                    CreateRoleCommand.class, command -> {
                                        command.setRoleId(roleId);
                                        command.setRoleName(StringUtils.merge(command.getRoleName(),
                                                event.getUserId()));
                                        command.setCreateId(event.getUserId());
                                        command.setCreateTime(new Date());
                                        return command;
                                    }).thenReturn(roleId));

            BiFunction<Long, Long, Mono<Long>> createRoleItem =
                    (baseRoleId, roleId) -> roleRootQueryRepository
                            .findRoleItemByRoleId(
                                    RoleItemEntity.builder().roleId(baseRoleId).build())
                            .flatMap(roleItem -> {
                                roleItem.setRoleId(roleId);
                                return commandDispatcher.executeCommand(Mono.just(roleItem),
                                        CreateRoleItemCommand.class, Void.class);
                            }).then(Mono.just(roleId));

            Function<Long, Mono<Void>> createUserRole =
                    roleId -> userRoleRootService.generateUserRoleId().flatMap(id -> {
                        CreateUserRoleCommand command =
                                CreateUserRoleCommand.builder().userRoleId(id)
                                        .userId(event.getUserId()).roleId(roleId).groupId(-1L)
                                        .createId(event.getUserId()).createTime(new Date()).build();
                        return commandDispatcher.executeCommand(Mono.just(command),
                                CreateUserRoleCommand.class, Void.class);
                    });

            Function<Throwable, Mono<Void>> onError = e -> {
                log.warn("CorpUserCreateEvent事件处理失败,userId:{}", event.getUserId(), e);
                return Mono.empty();
            };

            return queryUserAuth.get()
                    .flatMap(baseRoleId -> queryBaseRole.apply(baseRoleId).flatMap(createRole)
                            .flatMap(roleId -> createRoleItem.apply(baseRoleId, roleId))
                            .flatMap(createUserRole))
                    .onErrorResume(onError);
        });
    }
}
