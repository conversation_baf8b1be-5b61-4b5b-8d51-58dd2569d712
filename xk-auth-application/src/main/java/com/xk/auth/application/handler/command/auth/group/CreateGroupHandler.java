package com.xk.auth.application.handler.command.auth.group;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.group.CreateGroupCommand;
import com.xk.domain.model.auth.group.GroupEntity;
import com.xk.domain.model.auth.group.GroupRoot;
import com.xk.domain.repository.auth.group.GroupRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class CreateGroupHandler implements IActionCommandHandler<CreateGroupCommand, Void> {

    private final Converter converter;

    private final GroupRootRepository groupRootRepository;


    @Override
    public Mono<Void> execute(Mono<CreateGroupCommand> command) {
        return this.execute(command, GroupEntity.class,
                this.converter::convert,
                (groupEntity) -> GroupRoot.builder()
                        .identifier(LongIdentifier.builder().id(groupEntity.getGroupId()).build())
                        .groupEntity(groupEntity)
                        .build(),
                groupRootRepository::save
        );
    }
}
