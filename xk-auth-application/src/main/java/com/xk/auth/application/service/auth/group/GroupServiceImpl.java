package com.xk.auth.application.service.auth.group;

import java.util.*;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.application.action.query.auth.group.GroupByIdQuery;
import com.xk.application.action.query.auth.group.RoleGroupByIdQuery;
import com.xk.application.action.query.auth.role.RoleByIdQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.action.command.auth.group.*;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.auth.interfaces.service.auth.group.GroupService;
import com.xk.enums.auth.MenuAuthStatusEnum;
import com.xk.domain.model.auth.group.GroupEntity;
import com.xk.domain.model.auth.group.RoleGroupEntity;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.service.auth.group.GroupRootService;
import com.xk.domain.service.auth.userrole.UserRoleRootService;
import com.xk.interfaces.dto.req.auth.group.*;
import com.xk.interfaces.dto.rsp.auth.group.GroupRspDto;
import com.xk.interfaces.dto.rsp.auth.group.RoleGroupRspDto;
import com.xk.interfaces.dto.rsp.auth.role.RoleRspDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @Author: liucaihong
 * @CreateTime: 2024-07-15
 * @Description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GroupServiceImpl implements GroupService {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final IdentifierGenerateService identifierGenerateService;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;

    private final GroupRootService groupRootService;

    private final EventRootService eventRootService;

    private final UserRoleRootService userRoleRootService;


    /**
     * 添加分组
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> save(Mono<SaveGroupReqDto> dto) {

        Mono<Void> checkMono = dto.flatMap(saveGroupReqDto -> {
            if (saveGroupReqDto.getParentId() == null) {
                return Mono.empty();
            }
            return actionQueryDispatcher.executeQuery(
                            Mono.just(GroupByIdQuery.builder().groupId(saveGroupReqDto.getParentId()).build()),
                            GroupByIdQuery.class, GroupRspDto.class)
                    .switchIfEmpty(Mono.error(new XkApplicationException(XkApplicationErrorEnum.PARENT_ID_NOT_EXIST)))
                    .flatMap(groupRspDto -> {
                        if (!Objects.equals(groupRspDto.getPlatformType(),
                                saveGroupReqDto.getPlatformType()) || !Objects.equals(groupRspDto.getBusinessType(),
                                saveGroupReqDto.getBusinessType())) {
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                    new RuntimeException("父级分组平台类型或业务类型错误")));
                        }
                        return Mono.empty();
                    });
        });

        Mono<Void> saveMono = ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> groupRootService.generateGroupId().flatMap(groupId -> {
                    return commandDispatcher.executeCommand(dto, CreateGroupCommand.class, command -> {
                        command.setGroupId(groupId);
                        command.setCreateId(userId);
                        command.setCreateTime(new Date());
                        command.setStatus(MenuAuthStatusEnum.NORMAL.getCode());
                        if (command.getParentId() == null) {
                            command.setParentId(0L);
                        }
                        return command;
                    });
                }));
        return checkMono.then(saveMono);
    }

    /**
     * 添加角色分组关系
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> saveRoleGroup(Mono<SaveRoleGroupReqDto> dto) {
        return dto.flatMap(saveRoleGroupReqDto -> {
            return actionQueryDispatcher.executeQuery(
                            Mono.just(GroupByIdQuery.builder().groupId(saveRoleGroupReqDto.getGroupId()).build()),
                            GroupByIdQuery.class, GroupRspDto.class)
                    .flatMap(groupRspDto -> {
                        return Flux.fromIterable(saveRoleGroupReqDto.getRoleIds())
                                .flatMap(roleId -> {
                                    return actionQueryDispatcher.executeQuery(
                                                    Mono.just(RoleByIdQuery.builder().roleId(roleId).build()),
                                                    RoleByIdQuery.class, RoleRspDto.class)
                                            .flatMap(roleRspDto -> {
                                                if (!Objects.equals(groupRspDto.getPlatformType(),
                                                        roleRspDto.getPlatformType()) || !Objects.equals(
                                                        groupRspDto.getBusinessType(), roleRspDto.getBusinessType())) {
                                                    return Mono.error(new XkApplicationException(
                                                            XkApplicationErrorEnum.PLATFORM_TYPE_ERROR,
                                                            new RuntimeException("角色平台类型或业务类型错误")));
                                                }
                                                //查询
                                                RoleGroupByIdQuery byIdQuery = RoleGroupByIdQuery.builder()
                                                        .groupId(saveRoleGroupReqDto.getGroupId())
                                                        .roleId(roleId)
                                                        .build();
                                                return actionQueryDispatcher.executeQuery(Mono.just(byIdQuery),
                                                                RoleGroupByIdQuery.class, RoleGroupRspDto.class)
                                                        .hasElement()
                                                        .flatMap(hasElement -> {
                                                            if (hasElement) {
                                                                return Mono.empty();
                                                            }
                                                            return commandDispatcher.executeCommand(
                                                                    Mono.just(CreateRoleGroupCommand.builder()
                                                                            .groupId(saveRoleGroupReqDto.getGroupId())
                                                                            .roleId(roleId)
                                                                            .build()), CreateRoleGroupCommand.class);
                                                        });
                                            });
                                }).then();
                    });
        });
    }

    /**
     * 删除角色分组关系
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> deleteRoleGroup(Mono<RoleGroupReqDto> dto) {
        return dto.flatMap(groupIdReqDto -> {
            Set<Long> groupIds = new HashSet<>(); // 用于收集所有的 groupId
            // 初始化队列，存储待处理的分组 ID
            Queue<Long> groupQueue = new LinkedList<>();
            groupQueue.add(groupIdReqDto.getGroupId());

            return Flux.generate(() -> groupQueue, (queue, sink) -> {
                        Long currentGroupId = queue.poll();
                        if (currentGroupId == null) {
                            sink.complete(); // 队列为空，终止流
                        } else {
                            sink.next(currentGroupId); // 发出当前分组 ID
                        }
                        return queue;
                    })
                    .flatMap(groupId -> {
                        groupIds.add((Long) groupId); // 收集当前分组 ID

                        // 查找子分组并加入队列
                        return groupRootService.findByParentId(GroupEntity.builder().parentId((Long) groupId).build())
                                .doOnNext(subGroup -> {
                                    if (!groupIds.contains(subGroup.getGroupId())) { // 防止重复添加
                                        groupQueue.add(subGroup.getGroupId()); // 子分组加入队列
                                    }
                                })
                                .then();
                    }, 1) // 确保顺序执行
                    .then(Mono.defer(() -> {
                        // 在所有分组 ID 收集完成后执行删除
                        return Flux.fromIterable(groupIds)
                                .flatMap(groupId -> {
                                    return actionQueryDispatcher.executeQuery(Mono.just(RoleGroupByIdQuery.builder()
                                                    .groupId(groupId)
                                                    .roleId(groupIdReqDto.getRoleId())
                                                    .build()), RoleGroupByIdQuery.class, RoleGroupRspDto.class)
                                            .flatMap(roleGroupRspDto -> {
                                                DeletedRoleGroupCommand command = DeletedRoleGroupCommand.builder()
                                                        .groupId(groupId)
                                                        .roleId(roleGroupRspDto.getRoleId())
                                                        .build();
                                                return commandDispatcher.executeCommand(Mono.just(command),
                                                        DeletedRoleGroupCommand.class);
                                            });
                                }) // 顺序删除每个分组
                                .then();
                    }));
        });
    }

    /**
     * 修改分组
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> update(Mono<UpdateGroupReqDto> dto) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            return commandDispatcher.executeCommand(dto, UpdateGroupCommand.class, command -> {
                command.setUpdateId(userId);
                command.setUpdateTime(new Date());
                return command;
            });
        });
    }

    /**
     * 删除分组
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> delete(Mono<GroupIdReqDto> dto) {
        return dto.flatMap(groupIdReqDto -> {
            Set<Long> groupIds = new HashSet<>(); // 用于收集所有的 groupId

            // 初始化队列，存储待处理的分组 ID
            Queue<Long> groupQueue = new LinkedList<>();
            groupQueue.add(groupIdReqDto.getGroupId());

            return Flux.generate(() -> groupQueue, (queue, sink) -> {
                        Long currentGroupId = queue.poll();
                        if (currentGroupId == null) {
                            sink.complete(); // 队列为空，终止流
                        } else {
                            sink.next(currentGroupId); // 发出当前分组 ID
                        }
                        return queue;
                    })
                    .flatMap(groupId -> {
                        groupIds.add((Long) groupId); // 收集当前分组 ID

                        // 查找子分组并加入队列
                        return groupRootService.findByParentId(GroupEntity.builder().parentId((Long) groupId).build())
                                .doOnNext(subGroup -> {
                                    if (!groupIds.contains(subGroup.getGroupId())) { // 防止重复添加
                                        groupQueue.add(subGroup.getGroupId()); // 子分组加入队列
                                    }
                                })
                                .then();
                    }, 1) // 确保顺序执行
                    .then(Mono.defer(() -> {
                        // 在所有分组 ID 收集完成后执行删除
                        return Flux.fromIterable(groupIds)
                                .flatMap(this::deleteSingleGroupAndRoles) // 顺序删除每个分组
                                .then();
                    }));
        });
//        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId->{
//            return commandDispatcher.executeCommand(dto, DeletedGroupCommand.class)
//                    .then(Mono.defer(()->{
//                        return dto.flatMap(groupIdReqDto->{
//                           //删除角色关联
//                           return groupRootService.findRoleGroupByGroupId(
//                                   RoleGroupEntity.builder().groupId(groupIdReqDto.getGroupId()).build()
//                           ).flatMap(roleGroupEntity->{
//                               return commandDispatcher.executeCommand(
//                                       Mono.just(DeletedRoleGroupCommand.builder()
//                                               .groupId(roleGroupEntity.getGroupId())
//                                               .roleId(roleGroupEntity.getRoleId())
//                                               .build()),DeletedRoleGroupCommand.class);
//                           }).then(Mono.just(groupIdReqDto.getGroupId()));
//                        });
//                    }))
//                    .flatMap(groupId->{
//                        //删除与用户关联关系
//                        return userRoleRootService.selectByGroupId(UserRoleEntity.builder().groupId(groupId).build())
//                                .flatMap(userRoleEntity->{
//                                    return commandDispatcher.executeCommand(Mono.just(UpdateUserRoleCommand.builder()
//                                            .userRoleId(userRoleEntity.getUserRoleId())
//                                                    .groupId(0L)
//                                            .build()), UpdateUserRoleCommand.class);
//                                }).then(Mono.just(groupId));
//                    })
//                    .flatMap(groupId->{
//                        DeleteGroupEvent event = DeleteGroupEvent.builder().identifier(EventRoot.getCommonsDomainEventIdentifier(DeleteGroupEvent.class))
//                                .groupId(groupId)
//                                .build();
//                        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).isTry(true).build();
//                        return eventRootService.publisheByMono(eventRoot)
//                                .flatMap(status->{
//                                    if (status){
//                                        return Mono.empty();
//                                    }
//                                    return Mono.error(new XkApplicationException(XkApplicationErrorEnum.EVENT_SEND_ERROR));
//                                });
//                    });
//        });
    }

    private Mono<Void> deleteSingleGroupAndRoles(Long groupId) {
        Mono<Void> deletedGroup = commandDispatcher.executeCommand(
                Mono.just(DeletedGroupCommand.builder().groupId(groupId).build()), DeletedGroupCommand.class
        );
        Mono<Void> deletedRoles = groupRootService.findRoleGroupByGroupId(
                        RoleGroupEntity.builder().groupId(groupId).build()
                )
                .flatMap(roleGroupEntity -> {
                    return commandDispatcher.executeCommand(
                            Mono.just(DeletedRoleGroupCommand.builder()
                                    .groupId(roleGroupEntity.getGroupId())
                                    .roleId(roleGroupEntity.getRoleId())
                                    .build()),
                            DeletedRoleGroupCommand.class
                    );
                }).then();

        //删除与用户关联关系
        Mono<Void> deleteUserRoleMono = userRoleRootService.selectByGroupId(
                        UserRoleEntity.builder().groupId(groupId).build())
                .flatMap(userRoleEntity -> {
                    return commandDispatcher.executeCommand(Mono.just(UpdateUserRoleCommand.builder()
                            .userRoleId(userRoleEntity.getUserRoleId())
                            .groupId(0L)
                            .build()), UpdateUserRoleCommand.class);
                }).then();
        return deletedGroup.then(deletedRoles).then(deleteUserRoleMono);
    }
}
