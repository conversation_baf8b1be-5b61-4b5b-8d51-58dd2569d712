package com.xk.auth.application.action.command.auth.menu;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.menu.MenuEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.menu.MenuIdReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = MenuEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = MenuIdReqDto.class),
})
public class DeletedMenuCommand extends AbstractActionCommand {

    /**
     * 菜单ID
     */
    private Long menuId;
}
