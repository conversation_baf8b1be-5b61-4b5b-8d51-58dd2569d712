package com.xk.auth.application.handler.command.log;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.application.action.command.log.CreateOperationLogCommand;
import com.xk.domain.model.log.OperationLogEntity;
import com.xk.domain.model.log.OperationLogRoot;
import com.xk.domain.repository.log.OperationLogRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Component
@RequiredArgsConstructor
public class CreateOperationLogHandler implements IActionCommandHandler<CreateOperationLogCommand, Void> {

    private final OperationLogRootRepository operationLogRootRepository;
    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<CreateOperationLogCommand> command) {
        return this.execute(command, OperationLogEntity.class,
                this.converter::convert,
                (operationLogEntity) -> OperationLogRoot.builder()
                        .identifier(LongIdentifier.builder().id(operationLogEntity.getLogId()).build())
                        .operationLogEntity(operationLogEntity)
                        .build(),
                operationLogRootRepository::save
        );
    }
}
