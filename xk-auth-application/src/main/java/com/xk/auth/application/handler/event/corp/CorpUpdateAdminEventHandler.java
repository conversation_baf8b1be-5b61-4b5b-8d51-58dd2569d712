package com.xk.auth.application.handler.event.corp;

import java.util.Date;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.util.StringUtils;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.auth.application.action.command.auth.role.CreateRoleCommand;
import com.xk.auth.application.action.command.auth.role.CreateRoleItemCommand;
import com.xk.auth.application.action.command.auth.role.DeletedRoleCommand;
import com.xk.auth.application.action.command.auth.role.DeletedRoleItemCommand;
import com.xk.auth.application.action.command.auth.userrole.CreateUserRoleCommand;
import com.xk.auth.application.action.command.auth.userrole.DeletedUserRoleCommand;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.auth.application.commons.AuthDictEnum;
import com.xk.auth.application.commons.XkAuthApplicationErrorEnum;
import com.xk.auth.application.support.XkAuthApplicationException;
import com.xk.corp.domain.event.user.CorpUpdateAdminEvent;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.repository.auth.role.RoleRootQueryRepository;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.domain.service.auth.userrole.UserRoleRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CorpUpdateAdminEventHandler extends AbstractEventVerticle<CorpUpdateAdminEvent> {

    private final DictObjectDomainService dictObjectDomainService;
    private final RoleRootQueryRepository roleRootQueryRepository;
    private final RoleRootService roleRootService;
    private final UserRoleRootService userRoleRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<CorpUpdateAdminEvent> mono) {
        return mono.flatMap(event -> roleRootService.generateRoleId().flatMap(roleId -> {

            Mono<Long> queryBaseRoleId =
                    dictObjectDomainService.getSystemConfigToLong(AuthDictEnum.BASE_CORP_USER_AUTH)
                            .switchIfEmpty(Mono.error(new XkAuthApplicationException(
                                    XkAuthApplicationErrorEnum.CORP_USER_ROLE_NOT_CONFIG)));

            Function<Long, Mono<Long>> queryBaseRole = baseRoleId -> roleRootQueryRepository
                    .findById(LongIdentifier.builder().id(baseRoleId).build())
                    .switchIfEmpty(Mono.error(new XkAuthApplicationException(
                            XkAuthApplicationErrorEnum.CORP_USER_ROLE_NOT_CONFIG)))
                    .flatMap(roleEntity -> commandDispatcher.executeCommand(Mono.just(roleEntity),
                            CreateRoleCommand.class, command -> {
                                command.setRoleId(roleId);
                                command.setRoleName(StringUtils.merge(command.getRoleName(),
                                        event.getOldAdminId()));
                                command.setCreateId(event.getOldAdminId());
                                command.setCreateTime(new Date());
                                return command;
                            }).thenReturn(roleId));

            Function<Long, Mono<Long>> createRoleItem = baseRoleId -> roleRootQueryRepository
                    .findRoleItemByRoleId(RoleItemEntity.builder().roleId(baseRoleId).build())
                    .flatMap(roleItem -> {
                        roleItem.setRoleId(roleId);
                        return commandDispatcher.executeCommand(Mono.just(roleItem),
                                CreateRoleItemCommand.class, Void.class);
                    }).then(Mono.just(roleId));

            Function<Long, Mono<Void>> createUserRole =
                    newRoleId -> userRoleRootService.generateUserRoleId().flatMap(id -> {
                        CreateUserRoleCommand command = CreateUserRoleCommand.builder()
                                .userRoleId(id).userId(event.getOldAdminId()).roleId(newRoleId)
                                .groupId(-1L).createId(event.getOldAdminId()).createTime(new Date())
                                .build();
                        return commandDispatcher.executeCommand(Mono.just(command),
                                CreateUserRoleCommand.class, Void.class);
                    });

            Mono<Void> removeNewAdminRole = userRoleRootService
                    .selectByUserId(UserRoleEntity.builder().userId(event.getNewAdminId()).build())
                    .flatMap(entity -> commandDispatcher
                            .executeCommand(Mono.just(entity), DeletedUserRoleCommand.class)
                            .thenReturn(entity.getRoleId()))
                    .flatMap(existRoleId -> commandDispatcher
                            .executeCommand(Mono.just(new DeletedRoleCommand()),
                                    DeletedRoleCommand.class, command -> {
                                        command.setRoleId(existRoleId);
                                        return command;
                                    })
                            .thenReturn(existRoleId))
                    .flatMapMany(existRoleId -> roleRootService.findRoleItemByRoleId(
                            RoleItemEntity.builder().roleId(existRoleId).build()))
                    .flatMap(roleItem -> commandDispatcher.executeCommand(Mono.just(roleItem),
                            DeletedRoleItemCommand.class))
                    .then();

            Mono<Void> updateOldAdminRole = userRoleRootService
                    .selectByUserId(UserRoleEntity.builder().userId(event.getOldAdminId()).build())
                    .flatMap(entity -> commandDispatcher.executeCommand(Mono.just(entity),
                            UpdateUserRoleCommand.class, command -> {
                                command.setUserId(event.getNewAdminId());
                                command.setUpdateId(event.getOldAdminId());
                                command.setUpdateTime(new Date());
                                return command;
                            }));

            Mono<Void> createOldAdminRole = queryBaseRoleId.flatMap(queryBaseRole)
                    .flatMap(createRoleItem).flatMap(createUserRole);

            return removeNewAdminRole.then(updateOldAdminRole).then(createOldAdminRole)
                    .onErrorResume(e -> {
                        log.warn("CorpUpdateAdminEvent事件处理失败,userId:{}", event.getOldAdminId(), e);
                        return Mono.empty();
                    });
        }));
    }
}
