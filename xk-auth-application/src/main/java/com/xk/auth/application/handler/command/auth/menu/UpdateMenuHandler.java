package com.xk.auth.application.handler.command.auth.menu;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.menu.UpdateMenuCommand;
import com.xk.domain.model.auth.menu.MenuEntity;
import com.xk.domain.model.auth.menu.MenuRoot;
import com.xk.domain.repository.auth.menu.MenuRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class UpdateMenuHandler implements IActionCommandHandler<UpdateMenuCommand, Void> {

    private final MenuRootRepository menuRootRepository;

    private final Converter converter;


    @Override
    public Mono<Void> execute(Mono<UpdateMenuCommand> command) {
        return this.execute(command, MenuEntity.class,
                this.converter::convert,
                (entity) -> MenuRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getMenuId()).build())
                        .menuEntity(entity)
                        .build(),
                menuRootRepository::update
        );
    }
}
