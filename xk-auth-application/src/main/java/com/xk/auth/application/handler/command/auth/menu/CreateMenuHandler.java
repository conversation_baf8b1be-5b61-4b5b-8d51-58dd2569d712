package com.xk.auth.application.handler.command.auth.menu;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.menu.CreateMenuCommand;
import com.xk.domain.model.auth.menu.MenuEntity;
import com.xk.domain.model.auth.menu.MenuRoot;
import com.xk.domain.repository.auth.menu.MenuRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class CreateMenuHandler implements IActionCommandHandler<CreateMenuCommand, Void> {

    private final Converter converter;

    private final MenuRootRepository menuRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateMenuCommand> command) {
        return this.execute(command, MenuEntity.class,
                this.converter::convert,
                (entity) -> MenuRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getMenuId()).build())
                        .menuEntity(entity)
                        .build(),
                menuRootRepository::save
        );
    }
}
