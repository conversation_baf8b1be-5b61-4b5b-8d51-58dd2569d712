package com.xk.auth.application.handler.command.auth.userrole;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.auth.application.action.command.auth.userrole.UpdateUserRoleCommand;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import com.xk.domain.model.auth.userrole.UserRoleRoot;
import com.xk.domain.repository.auth.userrole.UserRoleRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * @Author: liucaihong
 */
@Component
@RequiredArgsConstructor
public class UpdateUserRoleHandler implements IActionCommandHandler<UpdateUserRoleCommand, Void> {

    private final UserRoleRootRepository userRoleRootRepository;

    private final Converter converter;


    @Override
    public Mono<Void> execute(Mono<UpdateUserRoleCommand> command) {
        return this.execute(command, UserRoleEntity.class,
                this.converter::convert,
                (entity) -> UserRoleRoot.builder()
                        .identifier(LongIdentifier.builder().id(entity.getUserRoleId()).build())
                        .userRoleEntity(entity)
                        .build(),
                userRoleRootRepository::update
        );
    }
}
