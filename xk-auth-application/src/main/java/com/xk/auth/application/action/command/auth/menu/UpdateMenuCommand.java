package com.xk.auth.application.action.command.auth.menu;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.menu.MenuEntity;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.interfaces.dto.req.auth.menu.UpdateMenuReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

import java.util.Date;

/**
 * @Author: liucaihong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = MenuEntity.class, uses = {PlatformTypeEnumConvertor.class, BusinessTypeEnumConvertor.class}),
        @AutoMapper(target = UpdateMenuReqDto.class),
})
public class UpdateMenuCommand extends AbstractActionCommand {

    /**
     * 菜单ID
     */
    private Long menuId;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 菜单名
     */
    private String menuName;

    /**
     * 菜单英文名
     */
    private String menuNameEn;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 菜单等级
     */
    private Integer menuLevel;

    /**
     * 菜单url
     */
    private String menuUrl;

    /**
     * 菜单约定显示位置
     */
    private String menuPosition;

    /**
     * 菜单图标名称（根据图标名称加载本地图片文件）
     */
    private String iconName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateId;

    /**
     * 菜单描述
     */
    private String menuDesc;

    private Integer status;
}
