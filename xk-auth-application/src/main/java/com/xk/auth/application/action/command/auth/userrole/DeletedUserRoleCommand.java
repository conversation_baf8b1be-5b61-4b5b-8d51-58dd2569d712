package com.xk.auth.application.action.command.auth.userrole;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.domain.model.auth.userrole.UserRoleEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @Author: liu<PERSON><PERSON>ong
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = UserRoleEntity.class)
})
public class DeletedUserRoleCommand extends AbstractActionCommand {

    /**
     * 用户角色id
     */
    private Long userRoleId;
}
