package com.xk.auth.application.service.auth.item;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.application.action.query.auth.item.ItemByIdQuery;
import com.xk.application.commons.XkApplicationErrorEnum;
import com.xk.application.support.XkApplicationException;
import com.xk.auth.application.action.command.auth.item.CreateItemCommand;
import com.xk.auth.application.action.command.auth.item.DeletedItemCommand;
import com.xk.auth.application.action.command.auth.item.UpdateItemCommand;
import com.xk.auth.application.action.command.auth.menu.DeletedMenuItemCommand;
import com.xk.auth.application.action.command.auth.role.DeletedRoleItemCommand;
import com.xk.auth.interfaces.service.auth.item.ItemService;
import com.xk.domain.model.auth.item.ItemRoot;
import com.xk.domain.model.auth.menu.MenuItemEntity;
import com.xk.domain.model.auth.role.RoleItemEntity;
import com.xk.domain.service.auth.item.ItemRootService;
import com.xk.domain.service.auth.menu.MenuRootService;
import com.xk.domain.service.auth.role.RoleRootService;
import com.xk.interfaces.dto.req.auth.item.ItemIdReqDto;
import com.xk.interfaces.dto.req.auth.item.SaveItemReqDto;
import com.xk.interfaces.dto.req.auth.item.UpdateItemReqDto;
import com.xk.interfaces.dto.rsp.auth.item.ItemRspDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * @Author: liucaihong
 * @CreateTime: 2024-07-15
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class ItemServiceImpl implements ItemService {


    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ItemRootService itemRootService;
    private final RoleRootService roleRootService;
    private final MenuRootService menuRootService;


    /**
     * 添加权限
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> save(Mono<SaveItemReqDto> dto) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            return commandDispatcher.executeCommand(dto, CreateItemCommand.class, command -> {
                command.setItemId(ItemRoot.generateItemId(command.getPlatformType(), command.getUri()));
                command.setCreateId(userId);
                command.setCreateTime(new Date());
                return command;
            });
        });
    }

    /**
     * 修改权限
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> update(Mono<UpdateItemReqDto> dto) {
        return dto.flatMap(req -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
            return actionQueryDispatcher.executeQuery(dto, ItemByIdQuery.class, ItemRspDto.class)
                    .flatMap(itemRspDto -> {
                        if (!req.getUri().equals(itemRspDto.getUri())) {
                            return Mono.error(new XkApplicationException(XkApplicationErrorEnum.ITEM_URL_NOT_EDIT));
                        }
                        return commandDispatcher.executeCommand(dto, UpdateItemCommand.class, command -> {
                            command.setUpdateId(userId);
                            command.setUpdateTime(new Date());
                            return command;
                        });
                    });
        }));
    }

    /**
     * 删除权限
     *
     * @param dto
     * @return
     */
    @Override
    @BusiCode
    public Mono<Void> delete(Mono<ItemIdReqDto> dto) {
        //删除权限
        return commandDispatcher.executeCommand(dto, DeletedItemCommand.class)
                .then(Mono.defer(() -> {
                    return dto.flatMap(req -> {
                        return roleRootService.findRoleItemByItemId(
                                        RoleItemEntity.builder().itemId(req.getItemId()).build())
                                .flatMap(roleItemEntity -> {
                                    return commandDispatcher.executeCommand(Mono.just(DeletedRoleItemCommand.builder()
                                            .itemId(roleItemEntity.getItemId())
                                            .roleId(roleItemEntity.getRoleId())
                                            .build()), DeletedRoleItemCommand.class);
                                }).then(Mono.just(req.getItemId()));
                    });
                }))
                .flatMap(itemId -> {
                    //删除菜单权限关联
                    return menuRootService.findMenuItemByItemId(MenuItemEntity.builder().itemId(itemId).build())
                            .flatMap(menuItemEntity -> {
                                return commandDispatcher.executeCommand(Mono.just(DeletedMenuItemCommand.builder()
                                                .itemId(menuItemEntity.getItemId()).menuId(menuItemEntity.getMenuId()).build()),
                                        DeletedMenuItemCommand.class);
                            }).then();
                });
    }
}
