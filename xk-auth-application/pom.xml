<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.auth</groupId>
        <artifactId>xk-auth</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-auth-application</artifactId>
    <packaging>jar</packaging>
    <name>xk-auth-application</name>
    <description>xk-auth-application</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.auth.application.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.auth.application.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.auth.application.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkAuthApplicationConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.auth</groupId>
            <artifactId>xk-auth-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.auth</groupId>
            <artifactId>xk-auth-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.auth</groupId>
            <artifactId>xk-auth-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.auth</groupId>
            <artifactId>xk-auth-infrastructure</artifactId>
        </dependency>
    </dependencies>
</project>
