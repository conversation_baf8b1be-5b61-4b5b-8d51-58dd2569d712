package com.xk.order.application.handler.event.filetask;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.model.filetask.FileTaskEntity;
import com.xk.domain.model.filetask.FileTaskIdentifier;
import com.xk.domain.model.filetask.FileTaskRoot;
import com.xk.domain.repository.filetask.FileTaskRootRepository;
import com.xk.ewd.domain.event.filetask.TaskOrderUpdateEvent;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskOrderUpdateEventHandler extends AbstractEventVerticle<TaskOrderUpdateEvent> {

    private final Converter converter;

    private final FileTaskRootRepository fileTaskRootRepository;

    @Override
    public Mono<Void> handle(Mono<TaskOrderUpdateEvent> event) {
        Function<TaskOrderUpdateEvent, FileTaskRoot> getFileTaskRoot = taskOrderCreateEvent -> FileTaskRoot.builder()
                .identifier(FileTaskIdentifier.builder().fileTaskId(taskOrderCreateEvent.getFileTaskId()).build())
                .fileTaskEntity(FileTaskEntity.builder()
                        .fileTaskId(taskOrderCreateEvent.getFileTaskId())
                        .fileTaskStatus(taskOrderCreateEvent.getFileTaskStatus())
                        .fileTaskBizStatus(taskOrderCreateEvent.getFileTaskBizStatusEnum())
                        .totalCount(taskOrderCreateEvent.getTotalCount())
                        .successCount(taskOrderCreateEvent.getSuccessCount())
                        .successTime(taskOrderCreateEvent.getSuccessTime())
                        .build())
                .build();

        return event.flatMap(taskOrderCreateEvent -> fileTaskRootRepository.update(getFileTaskRoot.apply(taskOrderCreateEvent)));

    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
