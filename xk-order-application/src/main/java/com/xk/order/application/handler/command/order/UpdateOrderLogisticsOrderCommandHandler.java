package com.xk.order.application.handler.command.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.order.UpdateOrderLogisticsOrderCommand;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateOrderLogisticsOrderCommandHandler
        implements IActionCommandHandler<UpdateOrderLogisticsOrderCommand, Void> {

    private final OrderRootService orderRootService;

    @Override
    public Mono<Void> execute(Mono<UpdateOrderLogisticsOrderCommand> mono) {
        return mono.flatMap(command -> {
            // 根据订单号查询现有的订单根聚合
            OrderIdentifier orderIdentifier =
                    OrderIdentifier.builder().orderNo(command.getOrderNo()).build();

            return orderRootService.getRoot(orderIdentifier)
                    .flatMap(v -> orderRootService.updateRoot(command.buildRoot(v)));
        });
    }
}
