package com.xk.order.application.convertor.sendgoods;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.order.application.action.command.sendgoods.CreateSendGoodsCommand;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.model.sendGoods.valobj.SendOrderAddrValueObject;
import com.xk.order.enums.sendGoods.LogisticsStatusEnum;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class CreateSendGoodsCommandToSendGoodsRootMapper
        implements BaseMapper<CreateSendGoodsCommand, SendGoodsRoot> {

    @Override
    public SendGoodsRoot convert(CreateSendGoodsCommand source) {
        return convert(source, SendGoodsRoot.builder()
                .identifier(SendGoodsIdentifier.builder().sendGoodsId(-1L).build()).build());
    }

    @Override
    public SendGoodsRoot convert(CreateSendGoodsCommand source, SendGoodsRoot target) {
        if (source == null) {
            return null;
        }

        return SendGoodsRoot.builder()
                .identifier(
                        SendGoodsIdentifier.builder().sendGoodsId(source.getSendGoodsId()).build())
                .logisticsOrderIdentifierList(source.getLogisticsOrderIdList().stream()
                        .map(id -> LogisticsOrderIdentifier.builder().logisticsOrderId(id).build())
                        .toList())
                .sendGoodsEntity(SendGoodsEntity.builder().sendGoodsId(source.getSendGoodsId())
                        .logisticsCorpName(source.getLogisticsCorpName())
                        .logisticsNo(source.getLogisticsNo())
                        .sendOrderAddr(SendOrderAddrValueObject.builder()
                                .addressId(source.getReceivingAddressId())
                                .mobile(source.getReceivingMobile())
                                .province(source.getReceivingProvince())
                                .city(source.getReceivingCity())
                                .district(source.getReceivingDistrict())
                                .addressSite(source.getReceivingAddressSite())
                                .addressDetail(source.getReceivingAddressDetail())
                                .name(source.getReceivingName()).build())
                        .logisticsStatus(LogisticsStatusEnum.getByCode(source.getLogisticsStatus()))
                        .price(source.getPrice()).sendGoodsTime(source.getSendGoodsTime())
                        .createId(source.getCreateId()).createTime(source.getCreateTime()).build())
                .build();

    }
}
