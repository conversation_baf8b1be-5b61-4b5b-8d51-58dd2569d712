package com.xk.order.application.action.command.logistics;


import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;

import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CreateLogisticsSendGoodsCommand extends AbstractActionCommand {

    /**
     * 物流订单IDS
     */
    private List<Long> logisticsOrderIdList;
    /**
     * 发货id
     */
    private Long sendGoodsId;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}
