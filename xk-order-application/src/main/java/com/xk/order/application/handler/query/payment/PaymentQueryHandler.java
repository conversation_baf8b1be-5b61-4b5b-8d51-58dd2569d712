package com.xk.order.application.handler.query.payment;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.order.application.action.query.payment.PaymentQuery;
import com.xk.order.domain.repository.payment.PaymentRootQueryRepository;
import com.xk.order.interfaces.dto.rsp.payment.PaymentRsp;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class PaymentQueryHandler implements IActionQueryHandler<PaymentQuery, PaymentRsp> {

    private final Converter converter;
    private final PaymentRootQueryRepository paymentRootQueryRepository;

    @Override
    public Mono<PaymentRsp> execute(Mono<PaymentQuery> query) {
        return this.execute(query, PaymentQuery::getOrderNo,
                paymentRootQueryRepository::findByOrderNo,
                (data) -> PaymentRsp.builder()
                        .paymentId(data.getPaymentId())
                        .orderNo(data.getOrderNo())
                        .payNo(data.getPayNo())
                        .userId(data.getUserId())
                        .payStatus(data.getPayStatus())
                        .platformType(data.getPlatformType())
                        .payType(data.getPayType())
                        .payTime(data.getPayTime())
                        .amount(data.getAmount())
                        .remark(data.getRemark())
                        .createTime(data.getCreateTime())
                        .updateTime(data.getUpdateTime())
                        .remark(data.getRemark())
                        .deleted(data.getDeleted())
                        .build());
    }
}
