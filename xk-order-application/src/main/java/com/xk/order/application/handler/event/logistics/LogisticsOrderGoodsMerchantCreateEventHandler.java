package com.xk.order.application.handler.event.logistics;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.logistics.LogisticsOrderCreateEvent;
import com.xk.order.domain.event.logistics.LogisticsOrderGoodsMerchantCreateEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.order.domain.service.logistics.LogisticsOrderRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsOrderGoodsMerchantCreateEventHandler
        extends AbstractEventVerticle<LogisticsOrderGoodsMerchantCreateEvent> {

    private final Converter converter;

    private final LogisticsOrderRootService logisticsOrderRootService;

    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


    /**
     * 商家商品创建物流订单
     * 
     * @param event event
     * @return
     */
    @Override
    public Mono<Void> handle(Mono<LogisticsOrderGoodsMerchantCreateEvent> event) {
        log.debug("商家商品创建物流订单");
        Function<LogisticsOrderRoot, Mono<Void>> publishEvent = logisticsOrderRoot -> {
            LogisticsOrderCreateEvent createEvent = LogisticsOrderCreateEvent
                    .builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(LogisticsOrderCreateEvent.class))
                    .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity()
                            .getOrderValueObject().getLogisticsOrderType())
                    .orderNo(logisticsOrderRoot.getLogisticsOrderEntity().getOrderValueObject()
                            .getOrderNo())
                    .logisticsOrderId(
                            logisticsOrderRoot.getLogisticsOrderEntity().getLogisticsOrderId())
                    .createTime(logisticsOrderRoot.getLogisticsOrderEntity().getCreateTime())
                    .build();
            log.debug("商家商品创建物流订单-发布消息：{}", logisticsOrderRoot.getLogisticsOrderEntity()
                    .getOrderValueObject().getOrderNo());
            EventRoot eventRoot =
                    EventRoot.builder().domainEvent(createEvent).isQueue(true).build();
            return eventRootService.publisheByMono(eventRoot).then();
        };

        /*
         * 创建物流订单
         */
        Function<LogisticsOrderGoodsMerchantCreateEvent, Mono<LogisticsOrderRoot>> logisticsOrderGoodsMerchantCreateEventMonoFunction =
                logisticsOrderGoodsMerchantCreateEvent -> logisticsOrderRootService.generateId()
                        .flatMap(id -> {
                            log.debug("商家商品创建物流订单-生成物流订单id：{}", id);
                            Date date = new Date();
                            return Mono.just(LogisticsOrderRoot.builder()
                                    .identifier(LogisticsOrderIdentifier.builder()
                                            .logisticsOrderId(id).build())
                                    .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                            .logisticsOrderId(id)
                                            .orderValueObject(OrderValueObject.builder()
                                                    .orderNo(logisticsOrderGoodsMerchantCreateEvent
                                                            .getOrderNo())
                                                    .logisticsOrderType(
                                                            LogisticsOrderTypeEnum.MERCHANT)
                                                    .build())
                                            .logisticsOrderStatus(
                                                    LogisticsOrderStatusEnum.TO_BE_SHIPPED)
                                            .createId(-1L).createTime(date).orderStatusTime(date)
                                            .build())
                                    .build());
                        });


        return event.flatMap(
                logisticsOrderGoodsMerchantCreateEvent -> logisticsOrderGoodsMerchantCreateEventMonoFunction
                        .apply(logisticsOrderGoodsMerchantCreateEvent)
                        .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                                .save(logisticsOrderRoot).thenReturn(logisticsOrderRoot))
                        .flatMap(publishEvent));
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
