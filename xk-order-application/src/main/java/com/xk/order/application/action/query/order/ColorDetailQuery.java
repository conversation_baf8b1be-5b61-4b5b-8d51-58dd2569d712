package com.xk.order.application.action.query.order;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderNoRequireReq.class)})
public class ColorDetailQuery implements IActionQueryMany {

    /**
     * 订单编号
     */
    private String orderNo;
}
