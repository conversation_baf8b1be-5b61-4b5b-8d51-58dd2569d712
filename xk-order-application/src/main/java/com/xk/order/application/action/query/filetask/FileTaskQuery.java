package com.xk.order.application.action.query.filetask;

import java.util.Date;
import java.util.List;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.dto.req.filetask.FileTaskReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = FileTaskReqDto.class, convertGenerate = false)})
public class FileTaskQuery extends PagerQuery implements IActionQuery {

    /**
     * 业务类型 1-物料订单导出 2-物料订单导入 3-物料生成发货单 4-商品订单导出 5-商品订单导入 6-商品生成发货单 7-商城订单导出 8-商城订单导入 9-商城生成发货单
     */
    private List<Integer> fileTaskBizType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 任务状态 1 待处理 2 处理中 3 部分成功 4 处理完成 5 处理异常 6 处理失败
     */
    private Integer fileTaskStatus;
}
