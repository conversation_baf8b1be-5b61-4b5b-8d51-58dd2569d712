package com.xk.order.application.handler.event.order;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderCancelEvent;
import com.xk.order.domain.event.order.OrderCancelSuccessEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.entity.OrderPriceEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelTypeEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.promotion.domain.enums.coupon.UsedStatusEnum;
import com.xk.promotion.interfaces.dto.req.UpdateUsedStatusReqDto;
import com.xk.promotion.interfaces.service.coupon.CouponInnerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 订单取消事件处理器 负责处理订单取消事件，包括更新订单状态和恢复库存
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelEventHandler extends AbstractEventVerticle<OrderCancelEvent> {

    private final LockRootService lockRootService;
    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final StockRootService stockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final CouponInnerService couponInnerService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    /**
     * 处理订单取消事件 流程：获取锁 -> 获取订单 -> 检查订单状态 -> 更新订单状态 -> 恢复库存
     * 
     * @param mono 包含订单取消事件的Mono
     * @return 处理结果的Mono
     */
    @Override
    public Mono<Void> handle(Mono<OrderCancelEvent> mono) {

        // 定义获取锁的函数
        Function<OrderCancelEvent, Mono<Boolean>> getLock =
                event -> lockRootService.acquireTransactionObjectLockMono(
                        ZookeeperLockObject.LOCKS_PAY_CALLBACK, event.getOrderNo());

        return mono.flatMap(event -> getLock.apply(event).flatMap(lock -> {
            // 如果获取锁失败，记录错误并返回异常
            if (Boolean.FALSE.equals(lock)) {
                log.error("获取LOCKS_PAY_CALLBACK锁失败,订单编号{}", event.getOrderNo());
                return Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR));
            }
            log.info("获取LOCKS_PAY_CALLBACK锁成功,订单编号{}", event.getOrderNo());

            // 根据订单号获取订单信息
            Mono<OrderRoot> getRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(event.getOrderNo()).build());

            // 检查订单状态是否为待支付，只有待支付的订单才能取消
            Function<OrderRoot, Mono<OrderRoot>> checkOrderStatus = root -> {
                if (!root.getOrderEntity().getOrderStatus().equals(OrderStatusEnum.WAIT_PAID)) {
                    log.info("订单编号{}不是待支付状态,不进行取消操作", event.getOrderNo());
                    return Mono.empty();
                }
                return Mono.just(root);
            };

            // 更新订单状态为已取消
            Function<OrderRoot, Mono<OrderRoot>> updateOrder = root -> {
                UpdateOrderCommand updateOrderCommand =
                        UpdateOrderCommand.builder().orderNo(root.getOrderEntity().getOrderNo())
                                .orderStatus(OrderStatusEnum.CANCEL.getCode())
                                .cancelType(event.getOrderCancelType().getCode())
                                .orderStatusTime(new Date()).build();
                return this.commandDispatcher
                        .executeCommand(Mono.just(updateOrderCommand), UpdateOrderCommand.class)
                        .thenReturn(root);
            };

            // 根据订单类型恢复相应的库存
            Function<OrderRoot, Mono<OrderRoot>> addStock = root -> {
                OrderEntity orderEntity = root.getOrderEntity();
                if (OrderTypeEnum.MATERIAL_PRODUCT.equals(orderEntity.getOrderType())) {
                    // 物料订单处理
                    return doMaterialStock(root).thenReturn(root);
                }
                // 其他类型订单处理
                return doOtherStock(root, event).thenReturn(root);
            };

            Function<OrderRoot, Mono<OrderRoot>> addToCancelList = root -> {
                if (!OrderTypeEnum.MERCHANT_PRODUCT.equals(root.getOrderEntity().getOrderType())) {
                    return Mono.empty();
                }
                if (!OrderCancelTypeEnum.TIME_OUT_CANCEL.equals(event.getOrderCancelType())) {
                    return Mono.empty();
                }
                // 超时取消的商家商品加到超时取消队列中
                return orderRootService.addCancelDelayQueue(root).thenReturn(root);
            };

            Function<OrderRoot, Mono<Void>> publishEvent = root -> {
                OrderCancelSuccessEvent successEvent = OrderCancelSuccessEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(OrderCancelSuccessEvent.class))
                        .orderNo(event.getOrderNo()).orderCancelType(event.getOrderCancelType())
                        .updateId(event.getUpdateId()).build();
                return eventRootService
                        .publisheByMono(
                                EventRoot.builder().domainEvent(successEvent).isQueue(true).build())
                        .then();
            };

            Function<Throwable, Mono<Void>> onError = e -> {
                if (event.getRetryCount() > 10) {
                    log.error("订单{}取消尝试超过最大次数", event.getOrderNo());
                    return Mono.error(new XkOrderApplicationException(
                            XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR));
                }
                log.error("订单取消处理失败: {}", event.getOrderNo(), e);
                Integer retryCount = event.getRetryCount() + 1;
                OrderCancelEvent orderCancelEvent = OrderCancelEvent.builder()
                        .identifier(
                                EventRoot.getCommonsDomainEventIdentifier(OrderCancelEvent.class))
                        .retryCount(retryCount).orderNo(event.getOrderNo()).build();
                return eventRootService.publisheByMono(
                        EventRoot.builder().domainEvent(orderCancelEvent).isQueue(true).build())
                        .then(Mono.error(e));
            };

            // 执行整个处理流程：获取订单 -> 检查状态 -> 更新状态 -> 恢复库存
            return getRoot.flatMap(checkOrderStatus).flatMap(updateOrder).flatMap(addStock)
                    .flatMap(addToCancelList).flatMap(publishEvent).onErrorResume(onError);
        }));
    }

    /**
     * 处理物料订单的库存恢复 对于物料订单，需要恢复公司的免费额度
     * 
     * @param root 订单根对象
     * @return 处理结果的Mono
     */
    private @NotNull Mono<Void> doMaterialStock(OrderRoot root) {
        OrderEntity orderEntity = root.getOrderEntity();
        OrderPriceEntity orderPriceEntity = root.getOrderPriceEntity();

        // 如果免费额度折扣状态为禁用，则不需要恢复库存
        if (Objects.equals(orderPriceEntity.getFreeQuotaDiscountStatus(),
                CommonStatusEnum.DISABLE)) {
            return Mono.empty();
        }

        // 计算需要恢复的免费额度（使用负数表示增加库存）
        int stockAdjustment = -Math.toIntExact(orderPriceEntity.getFreeQuotaDiscountAmount());

        // 调用库存服务恢复公司的免费额度
        return stockRootService
                .deductionStock(
                        StringIdentifier.builder().id(orderEntity.getCorpId().toString()).build(),
                        stockAdjustment, StockBusinessTypeEnum.CORP_FREE_QUOTA)
                .filter(Boolean::booleanValue) // 过滤成功的结果
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR))) // 如果失败则抛出异常
                .then(); // 转换为Mono<Void>
    }

    /**
     * 处理其他类型订单的库存恢复 对于非物料订单，需要恢复商品规格的库存
     *
     * @param root 订单根对象
     * @param event 订单取消事件
     * @return 处理结果的Mono
     */
    private @NotNull Mono<Void> doOtherStock(OrderRoot root, OrderCancelEvent event) {
        // 获取订单的所有订单项
        Flux<OrderItemEntity> orderItemFlux = orderItemRootService
                .searchEntityByOrderNo(root.getIdentifier()).cache(Duration.ofSeconds(10));

        // 定义恢复库存的函数
        Function<OrderItemEntity, Mono<Boolean>> addStock = entity -> {
            // 使用负数调用deductionStock方法来增加库存
            // 负数表示库存增加（取消订单时恢复库存）
            int stockAdjustment = -entity.getBuyCount();

            // 调用库存服务恢复商品规格的库存
            return stockRootService.deductionStock(
                    StringIdentifier.builder().id(entity.getSpecificationId().toString()).build(),
                    stockAdjustment, StockBusinessTypeEnum.SPECIFICATION);
        };

        // 商家商品移除缓存
        Function<OrderItemEntity, Mono<OrderItemEntity>> removeCache = entity -> {
            if (!OrderTypeEnum.MERCHANT_PRODUCT.equals(root.getOrderEntity().getOrderType())) {
                return Mono.empty();
            }
            // 超时取消的商家商品加到定时取消队列中
            if (OrderCancelTypeEnum.TIME_OUT_CANCEL.equals(event.getOrderCancelType())) {
                return Mono.empty();
            }
            return orderItemRootService.removeOrderBuyCache(OrderCacheValObj.builder()
                    .orderNo(root.getIdentifier().getOrderNo()).goodsId(entity.getGoodsId())
                    .userId(root.getOrderEntity().getUserId()).buyCount(entity.getBuyCount())
                    .amount(entity.getItemPayAmount()).build()).thenReturn(entity);
        };

        // 商家商品归还优惠券
        Function<OrderItemEntity, Mono<Void>> unusedCoupon = entity -> {
            if (!OrderTypeEnum.MERCHANT_PRODUCT.equals(root.getOrderEntity().getOrderType())) {
                return Mono.empty();
            }
            // 超时取消的商家商品加到定时取消队列中
            if (OrderCancelTypeEnum.TIME_OUT_CANCEL.equals(event.getOrderCancelType())) {
                return Mono.empty();
            }
            if (root.getOrderPriceEntity().getCouponUserId() == null) {
                return Mono.empty();
            }
            UpdateUsedStatusReqDto reqDto = UpdateUsedStatusReqDto.builder()
                    .couponUserId(root.getOrderPriceEntity().getCouponUserId())
                    .usedStatus(UsedStatusEnum.NOT_USED.getCode()).build();
            reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
            return couponInnerService.updateUsedStatus(Mono.just(reqDto)).then();
        };

        // 对每个订单项执行库存恢复操作
        return orderItemFlux.flatMap(addStock).filter(Boolean::booleanValue) // 过滤成功的结果
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR))) // 如果失败则抛出异常
                .thenMany(orderItemFlux).flatMap(removeCache).flatMap(unusedCoupon).then(); // 转换为Mono<Void>
    }
}
