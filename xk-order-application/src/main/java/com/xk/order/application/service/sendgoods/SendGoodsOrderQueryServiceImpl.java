package com.xk.order.application.service.sendgoods;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.service.sendgoods.SendGoodsOrderQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendGoodsOrderQueryServiceImpl implements SendGoodsOrderQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;


}
