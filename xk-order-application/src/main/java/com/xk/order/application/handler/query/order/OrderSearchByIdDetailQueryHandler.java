package com.xk.order.application.handler.query.order;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.goods.CardGoodsValueObject;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.application.action.query.order.OrderSearchByIdDetailQuery;
import com.xk.order.application.dto.order.OrderGiftAppDto;
import com.xk.order.application.dto.order.OrderGoodsAppDto;
import com.xk.order.application.dto.order.OrderLogisticsOrderAppDto;
import com.xk.order.application.dto.order.OrderSearchDetailAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchByIdDetailRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class OrderSearchByIdDetailQueryHandler
        implements IActionQueryHandler<OrderSearchByIdDetailQuery, OrderSearchByIdDetailRsp> {

    private final OrderRootService orderRootService;
    private final Converter converter;
    private final SelectorRootService selectorRootService;
    private final OrderItemRootService orderItemRootService;

    @Override
    public Mono<OrderSearchByIdDetailRsp> execute(Mono<OrderSearchByIdDetailQuery> mono) {
        return mono.flatMap(query -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderSearchDetailAppDto>> getDetail = root -> {
                OrderSearchDetailAppDto appDto =
                        converter.convert(root.getOrderEntity(), OrderSearchDetailAppDto.class);
                converter.convert(root.getOrderPriceEntity(), appDto);
                converter.convert(root.getOrderAddressEntity(), appDto);
                converter.convert(root.getOrderPayEntity(), appDto);
                converter.convert(root.getOrderRefundEntity(), appDto);
                if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                    List<OrderLogisticsOrderAppDto> appDtoList =
                            converter.convert(root.getOrderLogisticsOrderEntityList(),
                                    OrderLogisticsOrderAppDto.class);
                    appDto.setLogisticsInfoDtoList(appDtoList);
                }
                // 设置收货人手机号
                appDto.setReceivingMobile(root.getOrderAddressEntity().getMobile());
                return Mono.just(appDto);
            };

            Map<Long, GoodsObjectRoot> goodsCacheMap = new ConcurrentHashMap<>();
            Function<OrderSearchDetailAppDto, Mono<OrderSearchDetailAppDto>> getGoodsInfo =
                    appDto -> orderItemRootService
                            .searchRootByOrderNo(OrderIdentifierConvertor.map(appDto.getOrderNo()))
                            .flatMap(root -> {
                                Long goodsId = root.getOrderItemEntity().getGoodsId();
                                return Mono.justOrEmpty(goodsCacheMap.get(goodsId))
                                        .switchIfEmpty(selectorRootService.getGoodsObject(goodsId))
                                        .flatMap(goodsObjectRoot -> {
                                            goodsCacheMap.putIfAbsent(goodsId, goodsObjectRoot);
                                            OrderGoodsAppDto goodsAppDto =
                                                    converter.convert(root.getOrderItemEntity(),
                                                            OrderGoodsAppDto.class);
                                            // 如果是物料订单，则发货数量为购买数量
                                            if (Objects.equals(
                                                    root.getOrderItemEntity().getOrderType(),
                                                    OrderTypeEnum.MATERIAL_PRODUCT)) {
                                                goodsAppDto.setShipCount(
                                                        root.getOrderItemEntity().getBuyCount());
                                            } else {
                                                goodsAppDto
                                                        .setShipCount(appDto.getShipTotalCount());
                                            }
                                            goodsAppDto.setGoodsImages(goodsObjectRoot.getResList()
                                                    .stream()
                                                    .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE
                                                            .name().equals(v.getResType()))
                                                    .findFirst()
                                                    .map(GoodsResValueObject::getResAddr)
                                                    .orElse(null));
                                            goodsAppDto.setUnitPrice(
                                                    root.getOrderItemEntity().getUnitPrice());
                                            if (CollectionUtils.isNotEmpty(
                                                    goodsObjectRoot.getSpecificationList())) {
                                                goodsAppDto.setUnitType(
                                                        goodsObjectRoot.getSpecificationList()
                                                                .getFirst().getUnitType());
                                            }
                                            if (Objects.equals(
                                                    goodsObjectRoot.getGoodsInfo().getGoodsType(),
                                                    GoodsTypeEnum.MERCHANT_PRODUCT.name())) {
                                                CardGoodsValueObject cardGoods = goodsObjectRoot
                                                        .getGoodsInfo().getCardGoods();
                                                goodsAppDto.setProductType(ProductTypeEnum
                                                        .valueOf(cardGoods.getCardGoodsType())
                                                        .getCode());
                                                goodsAppDto.setCollectibleCardName(
                                                        cardGoods.getCollectionCardName());
                                            }
                                            List<OrderGiftAppDto> orderGiftAppDtos =
                                                    converter.convert(root.getOrderGiftEntityList(),
                                                            OrderGiftAppDto.class);
                                            List<OrderGoodsAppDto> goodsInfoDtoList =
                                                    Objects.requireNonNullElse(
                                                            appDto.getGoodsInfoDtoList(),
                                                            new ArrayList<>());
                                            goodsInfoDtoList.add(goodsAppDto);
                                            List<OrderGiftAppDto> orderGiftAppDtoList = Objects
                                                    .requireNonNullElse(appDto.getGiftInfoDtoList(),
                                                            new ArrayList<>());
                                            orderGiftAppDtoList.addAll(orderGiftAppDtos);
                                            appDto.setGoodsInfoDtoList(goodsInfoDtoList);
                                            appDto.setGiftInfoDtoList(orderGiftAppDtoList);
                                            return Mono.empty();
                                        });
                            }).collectList().thenReturn(appDto);

            Function<OrderSearchDetailAppDto, Mono<OrderSearchDetailAppDto>> getUserInfo =
                    appDto -> selectorRootService.getUserObject(appDto.getUserId())
                            .doOnSuccess(v -> {
                                appDto.setMobile(v.getUserDataObjectEntity().getMobile());
                                appDto.setUserNick(v.getUserDataObjectEntity().getNickname());
                                appDto.setPicId(v.getUserDataObjectEntity().getPicId());
                            }).thenReturn(appDto);

            Function<OrderSearchDetailAppDto, Mono<OrderSearchDetailAppDto>> getCorpInfo =
                    appDto -> selectorRootService.getCorpObject(appDto.getCorpId())
                            .doOnSuccess(v -> {
                                if (v == null) {
                                    return;
                                }
                                appDto.setCorpName(v.getCorpInfoObjectEntity().getCorpName());
                                appDto.setCorpLogo(v.getCorpInfoObjectEntity().getCorpLogo());
                            }).thenReturn(appDto);

            Function<OrderSearchDetailAppDto, Mono<OrderSearchDetailAppDto>> setGoodsInfo =
                    orderSearchDetailAppDto -> {
                        if (Objects.equals(orderSearchDetailAppDto.getOrderType(),
                                OrderTypeEnum.MATERIAL_PRODUCT.getCode())) {
                            return Mono.just(orderSearchDetailAppDto);
                        }

                        int sum = orderSearchDetailAppDto.getGoodsInfoDtoList().stream()
                                .mapToInt(OrderGoodsAppDto::getShipCount).sum();
                        OrderGoodsAppDto first =
                                orderSearchDetailAppDto.getGoodsInfoDtoList().getFirst();
                        first.setShipCount(sum);
                        orderSearchDetailAppDto.setGoodsInfoDtoList(List.of(first));
                        return Mono.just(orderSearchDetailAppDto);
                    };

            return getOrderRoot.flatMap(getDetail).flatMap(getGoodsInfo).flatMap(setGoodsInfo)
                    .flatMap(getUserInfo).flatMap(getCorpInfo)
                    .map(v -> converter.convert(v, OrderSearchByIdDetailRsp.class));
        });
    }
}
