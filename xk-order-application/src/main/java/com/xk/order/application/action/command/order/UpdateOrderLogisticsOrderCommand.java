package com.xk.order.application.action.command.order;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.domain.event.sendgoods.SendGoodsConfirmEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderLogisticsOrderEntity;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderTypeEnumConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;
import reactor.core.publisher.Mono;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = SendGoodsConfirmEvent.class, convertGenerate = false,
                uses = {LogisticsOrderStatusEnumConvertor.class,
                        LogisticsOrderTypeEnumConvertor.class}),
        @AutoMapper(target = SendGoodsCreateEvent.class, convertGenerate = false, uses = {
                LogisticsOrderStatusEnumConvertor.class, LogisticsOrderTypeEnumConvertor.class}),})
public class UpdateOrderLogisticsOrderCommand extends AbstractActionCommand {

    /**
     * 物流订单id
     */
    private Long logisticsOrderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 物流订单状态 1、待发货2、待收货3、已完成
     */
    private Integer logisticsOrderStatus;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 物流公司名
     */
    private String logisticsCorpName;

    /**
     * 构建订单根聚合
     */
    public Mono<OrderRoot> buildRoot(OrderRoot existingRoot) {
        OrderLogisticsOrderEntity newEntity = OrderLogisticsOrderEntity.builder()
                .logisticsOrderId(this.logisticsOrderId).orderNo(this.orderNo)
                .logisticsOrderType(this.logisticsOrderType != null
                        ? LogisticsOrderTypeEnum.getByCode(this.logisticsOrderType)
                        : null)
                .logisticsOrderStatus(this.logisticsOrderStatus != null
                        ? LogisticsOrderStatusEnum.getByCode(this.logisticsOrderStatus)
                        : null)
                .logisticsCorpName(this.logisticsCorpName).logisticsNo(this.logisticsNo)
                .updateTime(this.updateTime).build();

        // 更新或添加物流订单实体到列表中
        List<OrderLogisticsOrderEntity> updatedList = new ArrayList<>();
        if (existingRoot.getOrderLogisticsOrderEntityList() != null) {
            updatedList.addAll(existingRoot.getOrderLogisticsOrderEntityList());
        }

        // 查找是否已存在相同的物流订单ID，如果存在则更新，否则添加
        boolean found = false;
        for (int i = 0; i < updatedList.size(); i++) {
            if (updatedList.get(i).getLogisticsOrderId().equals(this.logisticsOrderId)) {
                updatedList.set(i, newEntity);
                found = true;
                break;
            }
        }
        if (!found) {
            updatedList.add(newEntity);
        }
        existingRoot.getOrderEntity()
                .setOrderOldStatus(existingRoot.getOrderEntity().getOrderStatus());
        return Mono.just(OrderRoot.builder().identifier(existingRoot.getIdentifier())
                .orderEntity(existingRoot.getOrderEntity())
                .orderPriceEntity(existingRoot.getOrderPriceEntity())
                .orderAddressEntity(existingRoot.getOrderAddressEntity())
                .orderPayEntity(existingRoot.getOrderPayEntity())
                .orderRefundEntity(existingRoot.getOrderRefundEntity())
                .orderLogisticsOrderEntityList(updatedList).build());
    }
}
