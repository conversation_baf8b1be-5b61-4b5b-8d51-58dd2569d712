package com.xk.order.application.handler.event.merchant;

import java.util.Date;
import java.util.List;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.merchant.MerchantProductGroupedEvent;
import com.xk.infrastructure.cache.dao.merchant.MerchantOrderDao;
import com.xk.infrastructure.cache.key.merchant.MerchantOrderKey;
import com.xk.infrastructure.cache.po.merchant.MerchantOrderPo;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.domain.event.logistics.LogisticsOrderGoodsMerchantCreateEvent;
import com.xk.order.enums.order.OrderStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantProductGroupedEventHandler
        extends AbstractEventVerticle<MerchantProductGroupedEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final MerchantOrderDao merchantOrderDao;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    /**
     * 查询所有商品的订单数据并发送创建物流单id
     * 
     * @param event event
     */
    @Override
    public Mono<Void> handle(Mono<MerchantProductGroupedEvent> event) {
        Function<MerchantOrderPo, Mono<Void>> publishEvent = merchantOrderPo -> {
            EventRoot eventRoot = EventRoot.builder()
                    .domainEvent(LogisticsOrderGoodsMerchantCreateEvent.builder()
                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                    LogisticsOrderGoodsMerchantCreateEvent.class))
                            .orderNo(merchantOrderPo.getOrderNo()).build())
                    .isQueue(true).build();
            return eventRootService.publisheByMono(eventRoot).then();
        };

        Function<MerchantOrderPo, Mono<MerchantOrderPo>> updateOrder =
                merchantOrderPo -> commandDispatcher.executeCommand(
                        Mono.just(new UpdateOrderCommand()), UpdateOrderCommand.class, command -> {
                            command.setOrderNo(merchantOrderPo.getOrderNo());
                            command.setOrderStatus(OrderStatusEnum.WAIT_PUBLICITY.getCode());
                            Date date = new Date();
                            command.setOrderStatusTime(date);
                            command.setUpdateId(-1L);
                            command.setUpdateTime(date);
                            return command;
                        }).thenReturn(merchantOrderPo);

        return event.flatMap(merchantProductGroupedEvent -> {
            List<MerchantOrderPo> list = merchantOrderDao.getAllValue(MerchantOrderKey.builder()
                    .goodsId(merchantProductGroupedEvent.getGoodsId()).build());
            return Flux.fromIterable(list).flatMap(updateOrder).flatMap(publishEvent).then();
        });
    }
}
