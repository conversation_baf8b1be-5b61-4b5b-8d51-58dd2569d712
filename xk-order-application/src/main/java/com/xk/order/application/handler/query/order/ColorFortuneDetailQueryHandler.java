package com.xk.order.application.handler.query.order;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.object.corp.CorpObjectRoot;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.order.application.action.query.order.ColorFortuneDetailQuery;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderAppColorFortuneDataRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ColorFortuneDetailQueryHandler implements
        IActionQueryManyHandler<ColorFortuneDetailQuery, MerchantOrderAppColorFortuneDataRsp> {

    private final OrderItemRootQueryRepository orderItemRootQueryRepository;
    private final SelectorRootService selectorRootService;

    @Override
    public Flux<MerchantOrderAppColorFortuneDataRsp> execute(Mono<ColorFortuneDetailQuery> mono) {
        return mono.flatMapMany(this::processColorDetailQuery);
    }

    /**
     * 处理主要逻辑
     */
    private Flux<MerchantOrderAppColorFortuneDataRsp> processColorDetailQuery(
            ColorFortuneDetailQuery query) {
        Map<Long, CorpObjectRoot> corpCache = new HashMap<>();
        Flux<OrderGiftEntity> giftListMono = orderItemRootQueryRepository
                .searchGiftEntityByOrderNo(OrderIdentifierConvertor.map(query.getOrderNo()));
        return giftListMono.collectList().flatMapMany(list -> {
            Map<Long, List<OrderGiftEntity>> specIdMap = list.stream()
                    .collect(Collectors.groupingBy(OrderGiftEntity::getSpecificationId));
            return Flux.fromIterable(specIdMap.values()).flatMap(giftList -> {
                OrderGiftEntity entity = giftList.getFirst();
                return getCorpObjectRoot(entity.getCorpId(), corpCache)
                        .flatMap(corpObject -> Mono.just(MerchantOrderAppColorFortuneDataRsp
                                .builder().giftBusinessName(entity.getGiftBusinessName())
                                .cardBackAddr(
                                        corpObject.getBusinessConfigValueObject().getCardBack())
                                // 如果只有一个展示头像否则展示横图
                                .giftShowAddrList(giftList.size() == 1
                                        ? Collections
                                                .singletonList(giftList.getFirst().getGiftAddr())
                                        : giftList.stream().map(OrderGiftEntity::getGiftShowAddr)
                                                .toList())
                                .build()));
            });
        });
    }

    /**
     * 获取公司对象根，使用缓存优化
     */
    private Mono<CorpObjectRoot> getCorpObjectRoot(Long corpId,
            Map<Long, CorpObjectRoot> corpCache) {
        return Mono.justOrEmpty(corpCache.get(corpId))
                .switchIfEmpty(selectorRootService.getCorpObject(corpId)
                        .doOnNext(corp -> corpCache.put(corp.getIdentifier().corpId(), corp)));
    }
}
