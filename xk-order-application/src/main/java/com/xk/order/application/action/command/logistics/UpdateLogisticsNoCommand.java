package com.xk.order.application.action.command.logistics;


import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.interfaces.dto.req.logistics.LogisticsEditReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsEditReqDto.class)})
public class UpdateLogisticsNoCommand extends AbstractActionCommand {

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    /**
     * 物流订单类型
     */
    private Integer logisticsOrderType;

    /**
     * 物流公司名称
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

}
