package com.xk.order.application.action.command.sendgoods;


import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;

import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CreateSendGoodsCommand extends AbstractActionCommand {

    /**
     * 物流订单IDS
     */
    List<Long> logisticsOrderIdList;
    /**
     * 发货id
     */
    private Long sendGoodsId;
    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 赠品ID
     */
    private Long giftBusinessId;

    /**
     * 物流公司名
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收货地址id
     */
    private Long receivingAddressId;

    /**
     * 收货手机号
     */
    private String receivingMobile;

    /**
     * 收货省
     */
    private String receivingProvince;

    /**
     * 收货市
     */
    private String receivingCity;

    /**
     * 收货区
     */
    private String receivingDistrict;

    /**
     * 收货详细地址
     */
    private String receivingAddressDetail;

    /**
     * 收货人姓名
     */
    private String receivingName;

    /**
     * 收货省市区中文
     */
    private String receivingAddressSite;

    /**
     * 物流状态 0、待发货1、待揽收 2、已发货3、已完成4、物流异常
     */
    private Integer logisticsStatus;

    /**
     * 运费价格
     */
    private Long price;

    /**
     * 发货时间
     */
    private Date sendGoodsTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}
