package com.xk.order.application.handler.event.sendgoods;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.sendgoods.SendGoodsConfirmEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsConfirmEventHandler extends AbstractEventVerticle<SendGoodsConfirmEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public Mono<Void> handle(Mono<SendGoodsConfirmEvent> mono) {
        // return mono.flatMap(event -> {
        // log.info("处理确认收货事件: logisticsOrderId={}, orderNo={}, status={}",
        // event.getLogisticsOrderId(), event.getOrderNo(),
        // event.getLogisticsOrderStatus());
        //
        // // 构建更新物流订单命令
        // UpdateOrderLogisticsOrderCommand command = UpdateOrderLogisticsOrderCommand.builder()
        // .logisticsOrderId(event.getLogisticsOrderId()).orderNo(event.getOrderNo())
        // .logisticsOrderType(event.getLogisticsOrderType() != null
        // ? event.getLogisticsOrderType().getCode()
        // : null)
        // .logisticsNo(event.getOrderNo())
        // .logisticsOrderStatus(LogisticsOrderStatusEnum.RECEIVED.getCode())
        // .updateTime(new Date()).build();
        //
        // return commandDispatcher.executeCommand(Mono.just(command),
        // UpdateOrderLogisticsOrderCommand.class);
        // }).doOnError(error -> log.error("处理确认收货事件失败", error));
        return Mono.empty();
    }
}
