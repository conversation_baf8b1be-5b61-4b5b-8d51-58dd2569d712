package com.xk.order.application.service.sendgoods;

import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.service.filetask.FileTaskRootDomainService;
import com.xk.order.interfaces.service.sendgoods.SendGoodsOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SendGoodsOrderServiceImpl implements SendGoodsOrderService {

    private final FileTaskRootDomainService fileTaskRootDomainService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


}
