package com.xk.order.application.handler.query.order;

import static com.xk.order.interfaces.dto.rsp.order.MaterialOrderAppDetailRsp.OrderMaterialGoodsDto;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.order.application.action.query.order.MaterialOrderAppDetailQuery;
import com.xk.order.application.dto.order.OrderDetailAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.rsp.order.MaterialOrderAppDetailRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MaterialOrderAppDetailQueryHandler
        implements IActionQueryHandler<MaterialOrderAppDetailQuery, MaterialOrderAppDetailRsp> {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<MaterialOrderAppDetailRsp> execute(Mono<MaterialOrderAppDetailQuery> mono) {
        return mono.flatMap(query -> {

            Map<Long, GoodsObjectRoot> goodsCache = new ConcurrentHashMap<>();

            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderDetailAppDto>> getDetail =
                    root -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
                        if (!Objects.equals(root.getOrderEntity().getUserId(), userId)) {
                            return Mono.error(new SystemWrapperThrowable(
                                    SystemErrorEnum.UNSUPPORTED_OPERATION));
                        }
                        OrderDetailAppDto appDto =
                                converter.convert(root.getOrderEntity(), OrderDetailAppDto.class);
                        converter.convert(root.getOrderPriceEntity(), appDto);
                        converter.convert(root.getOrderAddressEntity(), appDto);
                        converter.convert(root.getOrderPayEntity(), appDto);
                        converter.convert(root.getOrderRefundEntity(), appDto);
                        if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                            converter.convert(root.getOrderLogisticsOrderEntityList().getFirst(),
                                    appDto);
                        }
                        appDto.setCreateTime(
                                root.getOrderEntity().getCreateValObj().getCreateTime());

                        return Mono.just(appDto);
                    });

            Function<OrderDetailAppDto, Mono<MaterialOrderAppDetailRsp>> convert =
                    appDto -> Mono.just(converter.convert(appDto, MaterialOrderAppDetailRsp.class));

            Function<OrderMaterialGoodsDto, Mono<OrderMaterialGoodsDto>> getGoodsImage =
                    goodsDto -> Mono.justOrEmpty(goodsCache.get(goodsDto.getGoodsId()))
                            .switchIfEmpty(
                                    selectorRootService.getGoodsObject(goodsDto.getGoodsId()))
                            .flatMap(goodsObjectRoot -> {
                                goodsCache.putIfAbsent(goodsDto.getGoodsId(), goodsObjectRoot);
                                goodsDto.setGoodsImages(goodsObjectRoot.getResList().stream()
                                        .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE.name()
                                                .equals(v.getResType()))
                                        .findFirst().map(GoodsResValueObject::getResAddr)
                                        .orElse(null));
                                if (CollectionUtils
                                        .isNotEmpty(goodsObjectRoot.getSpecificationList())) {
                                    goodsDto.setUnitType(goodsObjectRoot.getSpecificationList()
                                            .stream()
                                            .filter(v -> v.getSpecificationId()
                                                    .equals(goodsDto.getSpecificationId()))
                                            .findFirst().map(SpecificationValueObject::getUnitType)
                                            .orElse(null));
                                }
                                goodsDto.setGoodsDescribe(
                                        goodsObjectRoot.getGoodsInfo().getDescription());
                                return Mono.just(goodsDto);
                            });

            Function<MaterialOrderAppDetailRsp, Mono<MaterialOrderAppDetailRsp>> getGoodsInfo =
                    detailRsp -> orderItemRootService.searchEntityByOrderNo(
                            OrderIdentifier.builder().orderNo(detailRsp.getOrderNo()).build())
                            .map(entity -> {
                                OrderMaterialGoodsDto orderMaterialGoodsDto =
                                        new OrderMaterialGoodsDto();
                                orderMaterialGoodsDto.setOrderTotalBuyCount(entity.getBuyCount());
                                orderMaterialGoodsDto
                                        .setSpecificationId(entity.getSpecificationId());
                                orderMaterialGoodsDto.setGoodsId(entity.getGoodsId());
                                orderMaterialGoodsDto.setGoodsName(entity.getGoodsName());
                                orderMaterialGoodsDto.setUnitPrice(entity.getUnitPrice());
                                return orderMaterialGoodsDto;
                            }).flatMap(getGoodsImage).collectList().map(v -> {
                                detailRsp.setOrderMaterialGoodsDtoList(v);
                                return detailRsp;
                            });

            return getOrderRoot.flatMap(getDetail).flatMap(convert).flatMap(getGoodsInfo);
        });
    }
}
