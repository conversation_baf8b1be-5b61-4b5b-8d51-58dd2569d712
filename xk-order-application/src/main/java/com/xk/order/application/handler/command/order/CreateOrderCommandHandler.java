package com.xk.order.application.handler.command.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.order.CreateOrderCommand;
import com.xk.order.domain.service.order.OrderRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateOrderCommandHandler implements IActionCommandHandler<CreateOrderCommand, Void> {

    private final Converter converter;
    private final OrderRootService orderRootService;

    @Override
    public Mono<Void> execute(Mono<CreateOrderCommand> mono) {
        return mono.flatMap(command -> orderRootService.saveRoot(command.buildRoot(converter)));
    }
}
