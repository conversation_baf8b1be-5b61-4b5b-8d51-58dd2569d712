package com.xk.order.application.handler.command.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.logistics.UpdateLogisticsOrderCommand;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateLogisticsSendGoodsCommandHandler
        implements IActionCommandHandler<UpdateLogisticsOrderCommand, Void> {

    private final Converter converter;
    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    @Override
    public Mono<Void> execute(Mono<UpdateLogisticsOrderCommand> command) {
        return command
                .flatMap(updateLogisticsOrderCommand -> Flux
                                .fromIterable(updateLogisticsOrderCommand.getLogisticsOrderIdList())
                                .flatMap(logisticsOrderId -> {
                                    LogisticsOrderRoot root = LogisticsOrderRoot.builder()
                                            .identifier(LogisticsOrderIdentifier.builder()
                                                    .logisticsOrderId(logisticsOrderId).build())
                                            .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                    .logisticsOrderId(logisticsOrderId)
                                                    .logisticsOrderStatus(
                                                            updateLogisticsOrderCommand
                                                                    .getLogisticsOrderStatus())
                                            .updateId(updateLogisticsOrderCommand.getUserId())
                                                    .updateTime(updateLogisticsOrderCommand
                                                            .getUpdateTime())
                                                    .orderStatusTime(updateLogisticsOrderCommand
                                                            .getUpdateTime())
                                                    .build())
                                            .build();
                                    return logisticsOrderRootRepository.update(root);
                        }).then());
    }
}
