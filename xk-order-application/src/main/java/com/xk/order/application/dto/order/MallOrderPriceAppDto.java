package com.xk.order.application.dto.order;

import com.xk.goods.interfaces.dto.res.goods.SpecDetailResDto;
import com.xk.order.application.action.command.order.CreateOrderItemCommand;
import com.xk.order.interfaces.dto.rsp.order.MallOrderPriceRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CreateOrderItemCommand.class, reverseConvertGenerate = false),
        @AutoMapper(target = MallOrderPriceRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = SpecDetailResDto.class, convertGenerate = false)})
public class MallOrderPriceAppDto {

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 合计金额
     */
    private Long payAmount;

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsImage;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 价格id
     */
    private Long priceId;
}
