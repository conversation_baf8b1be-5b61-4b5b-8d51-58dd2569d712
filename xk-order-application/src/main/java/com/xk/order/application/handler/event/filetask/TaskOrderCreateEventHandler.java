package com.xk.order.application.handler.event.filetask;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.model.filetask.FileTaskEntity;
import com.xk.domain.model.filetask.FileTaskIdentifier;
import com.xk.domain.model.filetask.FileTaskRoot;
import com.xk.domain.repository.filetask.FileTaskRootRepository;
import com.xk.order.domain.event.task.TaskOrderCreateEvent;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskOrderCreateEventHandler extends AbstractEventVerticle<TaskOrderCreateEvent> {

    private final Converter converter;

    private final FileTaskRootRepository fileTaskRootRepository;

    @Override
    public Mono<Void> handle(Mono<TaskOrderCreateEvent> event) {
        Function<TaskOrderCreateEvent, FileTaskRoot> getFileTaskRoot = taskOrderCreateEvent -> FileTaskRoot.builder()
                .identifier(FileTaskIdentifier.builder().fileTaskId(taskOrderCreateEvent.getFileTaskId()).build())
                .fileTaskEntity(FileTaskEntity.builder()
                        .fileTaskId(taskOrderCreateEvent.getFileTaskId())
                        .fileTaskName(taskOrderCreateEvent.getFileTaskName())
                        .fileTaskType(taskOrderCreateEvent.getFileTaskType())
                        .fileTaskStatus(taskOrderCreateEvent.getFileTaskStatus())
                        .fileTaskDescription(taskOrderCreateEvent.getFileTaskDescription())
                        .fileName(taskOrderCreateEvent.getFileName())
                        .createId(taskOrderCreateEvent.getCreateId())
                        .fileTaskBizStatus(taskOrderCreateEvent.getFileTaskBizStatusEnum())
                        .fileTaskBizType(taskOrderCreateEvent.getFileTaskBizType())
                        .commitTime(taskOrderCreateEvent.getCommitTime())
                        .createTime(new Date())
                        .ossPath(taskOrderCreateEvent.getOssPath())
                        .build())
                .build();

        return event.flatMap(taskOrderCreateEvent -> fileTaskRootRepository.save(getFileTaskRoot.apply(taskOrderCreateEvent)));

    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
