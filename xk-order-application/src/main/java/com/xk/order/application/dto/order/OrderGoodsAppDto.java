package com.xk.order.application.dto.order;

import com.xk.order.domain.model.order.entity.OrderItemEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderItemEntity.class, convertGenerate = false)})
public class OrderGoodsAppDto {

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 收藏卡名称
     */
    private String collectibleCardName;

    /**
     * 商品主图
     */
    private String goodsImages;

    /**
     * 购买单价
     */
    private Long unitPrice;

    /**
     * 发货数量
     */
    private Integer shipCount;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 产品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;
}
