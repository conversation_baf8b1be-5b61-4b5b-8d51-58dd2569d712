package com.xk.order.application.handler.command.sendgoods;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.sendgoods.CreateSendGoodsCommand;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.repository.sendgoods.SendGoodsRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateSendGoodsCommandHandler
        implements IActionCommandHandler<CreateSendGoodsCommand, Void> {

    private final Converter converter;
    private final SendGoodsRootRepository sendGoodsRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateSendGoodsCommand> command) {
        return command.flatMap(createSendGoodsCommand -> {
            SendGoodsRoot sendGoodsRoot =
                    converter.convert(createSendGoodsCommand, SendGoodsRoot.class);
            return sendGoodsRootRepository.createSendGoods(sendGoodsRoot);
        });
    }
}
