package com.xk.order.application.handler.query.order;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.order.application.action.query.order.MaterialOrderDetailQuery;
import com.xk.order.application.dto.order.OrderDetailAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.rsp.order.MaterialOrderDetailRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderGoodsDetailDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MaterialOrderDetailQueryHandler
        implements IActionQueryHandler<MaterialOrderDetailQuery, MaterialOrderDetailRsp> {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<MaterialOrderDetailRsp> execute(Mono<MaterialOrderDetailQuery> mono) {
        return mono.flatMap(query -> {

            Map<Long, GoodsObjectRoot> goodsCache = new ConcurrentHashMap<>();

            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderDetailAppDto>> getDetail = root -> {
                OrderDetailAppDto appDto =
                        converter.convert(root.getOrderEntity(), OrderDetailAppDto.class);
                converter.convert(root.getOrderPriceEntity(), appDto);
                converter.convert(root.getOrderAddressEntity(), appDto);
                converter.convert(root.getOrderPayEntity(), appDto);
                converter.convert(root.getOrderRefundEntity(), appDto);
                if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                    converter.convert(root.getOrderLogisticsOrderEntityList().getFirst(), appDto);
                }
                appDto.setCreateTime(root.getOrderEntity().getCreateValObj().getCreateTime());

                return Mono.just(appDto);
            };

            Function<OrderDetailAppDto, Mono<MaterialOrderDetailRsp>> convert =
                    appDto -> Mono.just(converter.convert(appDto, MaterialOrderDetailRsp.class));

            Function<OrderGoodsDetailDto, Mono<OrderGoodsDetailDto>> getGoodsImage =
                    goodsDto -> Mono.justOrEmpty(goodsCache.get(goodsDto.getGoodsId()))
                            .switchIfEmpty(
                                    selectorRootService.getGoodsObject(goodsDto.getGoodsId()))
                            .flatMap(goodsObjectRoot -> {
                                goodsCache.putIfAbsent(goodsDto.getGoodsId(), goodsObjectRoot);
                                goodsDto.setGoodsImages(goodsObjectRoot.getResList().stream()
                                        .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE.name()
                                                .equals(v.getResType()))
                                        .findFirst().map(GoodsResValueObject::getResAddr)
                                        .orElse(null));
                                if (CollectionUtils
                                        .isNotEmpty(goodsObjectRoot.getSpecificationList())) {
                                    goodsDto.setUnitType(goodsObjectRoot.getSpecificationList()
                                            .stream()
                                            .filter(v -> v.getSpecificationId()
                                                    .equals(goodsDto.getSpecificationId()))
                                            .findFirst().map(SpecificationValueObject::getUnitType)
                                            .orElse(null));
                                }
                                return Mono.just(goodsDto);
                            });

            Function<MaterialOrderDetailRsp, Mono<MaterialOrderDetailRsp>> getGoodsInfo =
                    detailRsp -> orderItemRootService.searchEntityByOrderNo(
                            OrderIdentifier.builder().orderNo(detailRsp.getOrderNo()).build())
                            .map(entity -> {
                                OrderGoodsDetailDto orderGoodsDetailDto = new OrderGoodsDetailDto();
                                orderGoodsDetailDto.setOrderTotalBuyCount(entity.getBuyCount());
                                orderGoodsDetailDto.setSpecificationId(entity.getSpecificationId());
                                orderGoodsDetailDto.setGoodsId(entity.getGoodsId());
                                orderGoodsDetailDto.setGoodsName(entity.getGoodsName());
                                orderGoodsDetailDto.setUnitPrice(entity.getUnitPrice());
                                return orderGoodsDetailDto;
                            }).flatMap(getGoodsImage).collectList().map(v -> {
                                detailRsp.setOrderGoodsDtoList(v);
                                return detailRsp;
                            });

            Function<MaterialOrderDetailRsp, Mono<MaterialOrderDetailRsp>> getCorpInfo =
                    rsp -> selectorRootService.getCorpObject(rsp.getCorpId()).doOnSuccess(v -> {
                        rsp.setCorpName(v.getCorpInfoObjectEntity().getCorpName());
                        rsp.setCorpLogo(v.getCorpInfoObjectEntity().getCorpLogo());
                    }).thenReturn(rsp);

            return getOrderRoot.flatMap(getDetail).flatMap(convert).flatMap(getGoodsInfo)
                    .flatMap(getCorpInfo);
        });
    }
}
