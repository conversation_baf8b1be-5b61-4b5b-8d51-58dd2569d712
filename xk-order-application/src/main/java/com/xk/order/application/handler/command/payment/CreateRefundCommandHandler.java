package com.xk.order.application.handler.command.payment;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.payment.CreateRefundCommand;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.domain.repository.payment.PaymentRootRepository;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.support.OrderSequenceEnum;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateRefundCommandHandler implements IActionCommandHandler<CreateRefundCommand,Void> {

    private final Converter converter;
    private final PaymentRootRepository paymentRootRepository;
    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> execute(Mono<CreateRefundCommand> command) {
        return paymentRootService.generateId(OrderSequenceEnum.O_REFUND).flatMap(id ->{
            return this.execute(command, RefundEntity.class,converter::convert,refund->{
                refund.setPaymentId(id);
                return refund;
            },paymentRootRepository::insertRefund);
        });
    }
}
