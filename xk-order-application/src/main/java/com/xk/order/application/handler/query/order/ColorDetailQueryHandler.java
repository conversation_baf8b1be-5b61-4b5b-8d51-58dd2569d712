package com.xk.order.application.handler.query.order;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.myco.mydata.domain.model.object.corp.CorpObjectRoot;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.interfaces.dto.res.color.SerialItemColorInnerResDto;
import com.xk.goods.interfaces.query.color.SerialItemColorQueryService;
import com.xk.order.application.action.query.order.ColorDetailQuery;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.enums.order.OrderGiftBusinessTypeEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderAppColorDataRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class ColorDetailQueryHandler
        implements IActionQueryManyHandler<ColorDetailQuery, MerchantOrderAppColorDataRsp> {

    private final OrderItemRootQueryRepository orderItemRootQueryRepository;
    private final SelectorRootService selectorRootService;
    private final SerialItemColorQueryService serialItemColorQueryService;

    @Override
    public Flux<MerchantOrderAppColorDataRsp> execute(Mono<ColorDetailQuery> mono) {
        return mono.flatMapMany(this::processColorDetailQuery);
    }

    /**
     * 处理颜色详情查询的主要逻辑
     */
    private Flux<MerchantOrderAppColorDataRsp> processColorDetailQuery(ColorDetailQuery query) {
        // 1. 并行获取颜色数据和订单礼品数据
        Mono<Map<String, SerialItemColorInnerResDto>> colorMapMono = getColorDataMap().cache();
        Mono<List<OrderGiftEntity>> giftListMono = getOrderGiftEntities(query).cache();

        // 2. 组合数据并处理
        return Mono.zip(colorMapMono, giftListMono)
                .flatMapMany(tuple -> processGiftEntities(tuple.getT1(), tuple.getT2()));
    }

    /**
     * 获取颜色数据映射
     */
    private Mono<Map<String, SerialItemColorInnerResDto>> getColorDataMap() {
        AbstractSession session = createInternalSession();
        return serialItemColorQueryService.innerSearch(Mono.just(session))
                .map(colorList -> colorList.stream()
                        .collect(Collectors.toMap(SerialItemColorInnerResDto::getColorName,
                                Function.identity(), (existing, replacement) -> existing)));
    }

    /**
     * 获取订单礼品实体列表
     */
    private Mono<List<OrderGiftEntity>> getOrderGiftEntities(ColorDetailQuery query) {
        OrderIdentifier orderIdentifier = OrderIdentifierConvertor.map(query.getOrderNo());
        return orderItemRootQueryRepository.searchGiftEntityByOrderNo(orderIdentifier)
                .collectList();
    }

    /**
     * 处理礼品实体列表，转换为响应DTO
     */
    private Flux<MerchantOrderAppColorDataRsp> processGiftEntities(
            Map<String, SerialItemColorInnerResDto> colorMap, List<OrderGiftEntity> giftEntities) {

        Map<Long, CorpObjectRoot> corpCache = new HashMap<>();
        return Flux.fromIterable(giftEntities)
                .flatMap(giftEntity -> buildColorDetailResponse(giftEntity, colorMap, corpCache));
    }

    /**
     * 构建单个颜色详情响应
     */
    private Mono<MerchantOrderAppColorDataRsp> buildColorDetailResponse(OrderGiftEntity giftEntity,
            Map<String, SerialItemColorInnerResDto> colorMap, Map<Long, CorpObjectRoot> corpCache) {
        return getCorpObjectRoot(giftEntity.getCorpId(), corpCache).flatMap(corpObject -> {
            SerialItemColorInnerResDto colorData = colorMap.get(giftEntity.getColor());
            return Mono.justOrEmpty(createResponseDto(giftEntity, colorData, corpObject));
        });
    }

    /**
     * 获取公司对象根，使用缓存优化
     */
    private Mono<CorpObjectRoot> getCorpObjectRoot(Long corpId,
            Map<Long, CorpObjectRoot> corpCache) {
        return Mono.justOrEmpty(corpCache.get(corpId))
                .switchIfEmpty(selectorRootService.getCorpObject(corpId)
                        .doOnNext(corp -> corpCache.put(corp.getIdentifier().corpId(), corp)));
    }

    /**
     * 创建响应DTO
     */
    private MerchantOrderAppColorDataRsp createResponseDto(OrderGiftEntity giftEntity,
            SerialItemColorInnerResDto colorData, CorpObjectRoot corpObject) {

        String cardBackAddr = getCardBackAddr(colorData, corpObject);
        Integer glowStatus =
                colorData != null && colorData.getGlowStatus() != null ? colorData.getGlowStatus()
                        : 0;
        String backGroundAddr = colorData != null ? colorData.getBackgroundAddr() : null;

        OrderGiftBusinessTypeEnum giftBusinessType = giftEntity.getGiftBusinessType();
        return MerchantOrderAppColorDataRsp.builder().categoryName(giftEntity.getCategoryName())
                .giftShowAddr(giftEntity.getGiftShowAddr())
                .giftBusinessName(giftEntity.getGiftBusinessName()).color(giftEntity.getColor())
                .glowStatus(glowStatus).cardBackAddr(cardBackAddr).backGroundAddr(backGroundAddr)
                .goodsId(giftEntity.getGoodsId())
                .giftBusinessType(giftBusinessType == null ? null : giftBusinessType.getCode())
                .build();
    }

    /**
     * 获取卡背地址，优先使用颜色数据，否则使用公司默认配置
     */
    private String getCardBackAddr(SerialItemColorInnerResDto colorData,
            CorpObjectRoot corpObject) {
        if (colorData != null && colorData.getCardBackAddr() != null) {
            return colorData.getCardBackAddr();
        }
        return corpObject.getBusinessConfigValueObject().getCardBack();
    }

    /**
     * 创建内部会话
     */
    private AbstractSession createInternalSession() {
        AbstractSession session = new AbstractSession();
        session.setSessionId(SessionRoot.getInternalDefaultSessionId());
        return session;
    }
}
