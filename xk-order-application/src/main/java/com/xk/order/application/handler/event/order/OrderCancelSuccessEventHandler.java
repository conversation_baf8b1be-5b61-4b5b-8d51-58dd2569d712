package com.xk.order.application.handler.event.order;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionIdReq;
import com.xk.goods.interfaces.service.random.RandomDistributionService;
import com.xk.order.domain.event.order.OrderCancelSuccessEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.entity.OrderLogisticsOrderEntity;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelSuccessEventHandler extends AbstractEventVerticle<OrderCancelSuccessEvent> {

    private final OrderItemRootService orderItemRootService;
    private final RandomDistributionService randomDistributionService;
    private final LogisticsOrderRootRepository logisticsOrderRootRepository;
    private final OrderRootService orderRootService;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderCancelSuccessEvent> mono) {
        return mono.flatMap(event -> orderItemRootService
                .searchRootByOrderNo(OrderIdentifierConvertor.map(event.getOrderNo())).collectList()
                .flatMap(list -> {
                    if (CollectionUtils.isEmpty(list)) {
                        return Mono.empty();
                    }
                    OrderItemEntity entity = list.getFirst().getOrderItemEntity();
                    if (ProductTypeEnum.FORTUNE_BOX.getCode().equals(entity.getProductType())) {
                        return doFortuneCallBack(list);
                    }
                    if (CommonStatusEnum.ENABLE.getCode().equals(entity.getRemainRandomStatus())) {
                        return doRemainRandomCallBack(list);
                    }
                    return Mono.empty();
                }).thenReturn(event)).flatMap(event -> {
                    // 更新物流订单撤单
                    return logisticsOrderRootRepository.updateByOrderNo(LogisticsOrderRoot.builder()
                            .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(-1L)
                                    .build())
                            .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                    .orderValueObject(OrderValueObject.builder()
                                            .orderNo(event.getOrderNo()).build())
                                    .logisticsOrderStatus(LogisticsOrderStatusEnum.CANCEL).build())
                            .build()).thenReturn(event);
                }).flatMap(event -> orderRootService
                        .getRoot(OrderIdentifierConvertor.map(event.getOrderNo())).flatMap(root -> {
                            List<OrderLogisticsOrderEntity> list =
                                    root.getOrderLogisticsOrderEntityList();
                            if (CollectionUtils.isEmpty(list)) {
                                return Mono.empty();
                            }
                            list.forEach(v -> {
                                v.setUpdateTime(new Date());
                                v.setLogisticsOrderStatus(LogisticsOrderStatusEnum.CANCEL);
                            });
                            return orderRootService.updateRoot(Mono.just(root));
                        }));
    }

    private Mono<Void> doFortuneCallBack(List<OrderItemRoot> rootList) {
        OrderItemEntity entity = rootList.getFirst().getOrderItemEntity();
        List<Long> list = rootList.stream().map(OrderItemRoot::getOrderItemEntity)
                .map(OrderItemEntity::getRandomItemId).toList();
        return randomDistributionService
                .updateCallbackDistributionItem(Mono.just(RandomDistributionIdReq.builder()
                        .distributionId(entity.getDistributionId()).itemIdList(list).build()))
                .thenMany(Flux.fromIterable(rootList)).flatMap(itemRoot -> {
                    OrderItemEntity orderItemEntity = itemRoot.getOrderItemEntity();
                    return orderItemRootService.removeSpecificationLocked(
                            OrderItemLockValObj.builder().goodsId(orderItemEntity.getGoodsId())
                                    .position(orderItemEntity.getItemPosition())
                                    .userId(orderItemEntity.getUserId()).build());
                }).then();
    }

    private Mono<Void> doRemainRandomCallBack(List<OrderItemRoot> rootList) {
        OrderItemEntity entity = rootList.getFirst().getOrderItemEntity();
        List<Long> list = rootList.stream().map(OrderItemRoot::getOrderItemEntity)
                .flatMap(orderItem -> Collections
                        .nCopies(orderItem.getBuyCount(), orderItem.getRandomItemId()).stream())
                .toList();
        return randomDistributionService
                .updateCallbackDistributionItem(Mono.just(RandomDistributionIdReq.builder()
                        .distributionId(entity.getDistributionId()).itemIdList(list).build()))
                .then();
    }
}
