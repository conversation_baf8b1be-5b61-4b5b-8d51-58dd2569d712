package com.xk.order.application.action.command.order;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.infrastructure.convertor.order.OrderGiftPrizeStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.infrastructure.convertor.order.OrderItemIdentifierConvertor;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderGiftEntity.class, uses = {OrderIdentifierConvertor.class,
        OrderItemIdentifierConvertor.class, OrderGiftPrizeStatusEnumConvertor.class})})
public class UpdateOrderGiftCommand extends AbstractActionCommand {

    /**
     * 订单条目id
     */
    private Long orderItemId;

    /**
     * 订单赠品ID
     */
    private Long orderGiftId;

    /**
     * 获奖状态 1-待公布 2-已完成 3-获赠卡
     */
    private Integer giftPrizeStatus;
}
