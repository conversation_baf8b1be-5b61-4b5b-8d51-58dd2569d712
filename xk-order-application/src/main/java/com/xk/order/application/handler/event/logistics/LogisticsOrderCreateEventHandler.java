package com.xk.order.application.handler.event.logistics;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.order.CreateOrderLogisticsOrderCommand;
import com.xk.order.domain.event.logistics.LogisticsOrderCreateEvent;
import com.xk.order.domain.event.order.OrderUpdateEvent;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsOrderCreateEventHandler
        extends AbstractEventVerticle<LogisticsOrderCreateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final OrderRootService orderRootService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<LogisticsOrderCreateEvent> mono) {
        return commandDispatcher.executeCommand(mono, CreateOrderLogisticsOrderCommand.class)
                .then(mono.flatMap(event -> orderRootService
                        .getRootNoLogistics(
                                OrderIdentifier.builder().orderNo(event.getOrderNo()).build())
                        .flatMap(root -> {
                            // 事件发布函数
                            String orderNo = root.getIdentifier().getOrderNo();
                            EventRoot eventRoot = EventRoot.builder().domainEvent(OrderUpdateEvent
                                    .builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderUpdateEvent.class))
                                    .orderNo(orderNo)
                                    // 旧订单状态等于新订单状态
                                    .oldOrderStatus(
                                            root.getOrderEntity().getOrderStatus().getCode())
                                    .orderStatus(root.getOrderEntity().getOrderStatus().getCode())
                                    .orderType(root.getOrderEntity().getOrderType().getCode())
                                    .build()).isQueue(true).build();
                            return eventRootService.publisheByMono(eventRoot).then();
                        })));
    }
}
