package com.xk.order.application.dto.order;

import com.xk.order.interfaces.dto.rsp.order.MaterialOrderPriceDetailRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = MaterialOrderPriceDetailRsp.class, reverseConvertGenerate = false)})
public class MaterialOrderPriceDetailAppDto {

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格总金额
     */
    private Long specTotalAmount;

    /**
     * 规格免费额度减免
     */
    private Long specFreeQuota;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 规格合计金额
     */
    private Long specPayAmount;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsImage;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 价格id
     */
    private Long priceId;
}
