package com.xk.order.application.handler.event.order;

import java.util.Date;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.logistics.LogisticsOrderCreateEvent;
import com.xk.order.domain.event.order.OrderItemCreatePrizedEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.order.domain.service.logistics.LogisticsOrderRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPrizedEventHandler extends AbstractEventVerticle<OrderItemCreatePrizedEvent> {

    private final Converter converter;

    private final LogisticsOrderRootService logisticsOrderRootService;

    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


    /**
     * 商家赠品创建物流订单
     * 
     * @param event event
     * @return
     */
    @Override
    public Mono<Void> handle(Mono<OrderItemCreatePrizedEvent> event) {

        Date createTime = new Date();

        Function<LogisticsOrderRoot, Mono<Void>> publishEvent =
                logisticsOrderRoot -> event.flatMap(orderItemCreatePrizedEvent -> {
                    EventRoot eventRoot = EventRoot.builder().domainEvent(LogisticsOrderCreateEvent
                            .builder()
                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                    LogisticsOrderCreateEvent.class))
                            .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getOrderValueObject().getLogisticsOrderType())
                            .orderNo(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getOrderValueObject().getOrderNo())
                            .logisticsOrderId(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getLogisticsOrderId())
                            .createTime(createTime)
                            .giftAddr(orderItemCreatePrizedEvent.getGiftAddr())
                            .giftName(orderItemCreatePrizedEvent.getGiftName())
                            .giftReportId(orderItemCreatePrizedEvent.getGiftReportId()).build())
                            .isQueue(true).build();
                    return eventRootService.publisheByMono(eventRoot).then();
                });

        /*
         * 创建物流订单
         */
        Function<OrderItemCreatePrizedEvent, Mono<LogisticsOrderRoot>> logisticsOrderRootConverter =
                orderPaidEvent -> logisticsOrderRootService.generateId()
                        .flatMap(id -> Mono.just(LogisticsOrderRoot.builder()
                                .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(id)
                                        .build())
                                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                        .logisticsOrderId(id)
                                        .orderValueObject(OrderValueObject.builder()
                                                .orderNo(orderPaidEvent.getOrderNo())
                                                .logisticsOrderType(LogisticsOrderTypeEnum.GIFT)
                                                .orderItemId(orderPaidEvent.getOrderItemId())
                                                .build())
                                        .logisticsOrderStatus(
                                                LogisticsOrderStatusEnum.TO_BE_SHIPPED)
                                        .createId(-1L).createTime(createTime).build())
                                .build()));

        return event
                .flatMap(orderItemCreatePrizedEvent -> logisticsOrderRootConverter
                        .apply(orderItemCreatePrizedEvent)
                        .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository
                                .save(logisticsOrderRoot).thenReturn(logisticsOrderRoot)))
                .flatMap(publishEvent);

    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
