package com.xk.order.application.handler.event.order;

import java.util.function.BiFunction;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.logistics.LogisticsOrderDeleteEvent;
import com.xk.order.domain.event.order.OrderItemDeletePrizedEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.infrastructure.repository.logistics.LogisticsOrderRootQueryRepositoryImpl;
import com.xk.order.infrastructure.repository.logistics.LogisticsOrderRootRepositoryImpl;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderItemDeletePrizedEventHandler
        extends AbstractEventVerticle<OrderItemDeletePrizedEvent> {

    private final Converter converter;

    private final LogisticsOrderRootQueryRepositoryImpl logisticsOrderRootQueryRepository;

    private final LogisticsOrderRootRepositoryImpl logisticsOrderRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


    /**
     * 商家赠品删除物流订单
     * 
     * @param event event
     * @return
     */
    @Override
    public Mono<Void> handle(Mono<OrderItemDeletePrizedEvent> event) {
        BiFunction<LogisticsOrderEntity, OrderItemDeletePrizedEvent, Mono<Void>> publishEvent =
                (logisticsOrderEntity, deletePrizedEvent) -> {
                    EventRoot eventRoot = EventRoot.builder()
                            .domainEvent(LogisticsOrderDeleteEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            LogisticsOrderDeleteEvent.class))
                                    .logisticsOrderId(logisticsOrderEntity.getLogisticsOrderId())
                                    .logisticsOrderType(LogisticsOrderTypeEnum.GIFT.getCode())
                                    .orderNo(deletePrizedEvent.getOrderNo())
                                    .createTime(logisticsOrderEntity.getCreateTime()).userId(-1L)
                                    .giftReportId(deletePrizedEvent.getGiftReportId())
                                    .build())
                            .isQueue(true).build();
                    return eventRootService.publisheByMono(eventRoot).then();
                };



        return event
                .flatMap(
                        orderItemCreatePrizedEvent -> logisticsOrderRootQueryRepository
                                .selectList(LogisticsOrderRoot.builder()
                                        .identifier(LogisticsOrderIdentifier.builder()
                                                .logisticsOrderId(-1L).build())
                                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                .orderValueObject(OrderValueObject.builder()
                                                        .orderNo(orderItemCreatePrizedEvent
                                                                .getOrderNo())
                                                        .orderItemId(orderItemCreatePrizedEvent
                                                                .getOrderItemId())
                                                        .build())
                                                .build())
                                        .build())
                                .flatMap(logisticsOrderEntity -> {
                                    if (!logisticsOrderEntity.getLogisticsOrderStatus().getCode()
                                            .equals(LogisticsOrderStatusEnum.TO_BE_SHIPPED
                                                    .getCode())) {
                                        return Mono.error(new XkOrderApplicationException(
                                                XkOrderApplicationErrorEnum.GIFT_SEND_GOODS_ERROR));
                                    }
                                    return Mono.just(logisticsOrderEntity);
                                })
                                .flatMap(logisticsOrderEntity -> publishEvent
                                        .apply(logisticsOrderEntity, orderItemCreatePrizedEvent)
                                        .thenReturn(logisticsOrderEntity))
                                .flatMap(logisticsOrderEntity -> logisticsOrderRootRepository
                                        .remove(LogisticsOrderRoot.builder()
                                                .identifier(LogisticsOrderIdentifier.builder()
                                                        .logisticsOrderId(
                                                                logisticsOrderEntity.getIdentifier()
                                                                        .getLogisticsOrderId())
                                                        .build())
                                                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                        .logisticsOrderId(
                                                                logisticsOrderEntity.getIdentifier()
                                                                        .getLogisticsOrderId())
                                                        .build())
                                                .build()))
                                .then());

    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
