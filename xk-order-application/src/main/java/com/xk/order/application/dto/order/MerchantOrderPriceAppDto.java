package com.xk.order.application.dto.order;

import java.util.List;

import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.enums.merchant.ProductRandomTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.order.application.action.command.order.CreateOrderCommand;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderPriceRsp;
import com.xk.order.interfaces.dto.rsp.order.MerchantRandomOrderRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MerchantOrderPriceRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MerchantRandomOrderRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = CreateOrderCommand.class, reverseConvertGenerate = false)})
public class MerchantOrderPriceAppDto {

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 商品应付金额
     */
    private Long needAmount;

    /**
     * 商品其他优惠金额
     */
    private Long otherDiscountAmount;

    /**
     * 商家优惠金额
     */
    private Long corpDiscountAmount;

    /**
     * 首购优惠金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 优惠券减免金额
     */
    private Long couponAmount;

    /**
     * 满减减免金额
     */
    private Long discountAmount;

    /**
     * 合计金额
     */
    private Long payAmount;

    /**
     * 板块类型
     */
    private Integer blockType;

    /**
     * 产品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private ProductTypeEnum productType;

    /**
     * 随机模式 1-选队/随机球员 2-选队/随机卡种 10-非选队/随机卡种(不带编) 11-非选队/随机卡种(带编) 12-非选队/随机球队 13-非选队/随机球员
     */
    private ProductRandomTypeEnum randomType;

    /**
     * 条目位置
     */
    private Integer itemPosition;

    /**
     * 剩余随机状态
     */
    private CommonStatusEnum remainRandomStatus;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 发货数量,用于下方的数量展示
     */
    private Integer shipTotalCount;

    /**
     * 剩余随机分发id
     */
    private Long remainRandomDistributionId;

    /**
     * 商家商品订单价格详情
     */
    private List<MerchantOrderPriceDetailAppDto> detailDtoList;
}
