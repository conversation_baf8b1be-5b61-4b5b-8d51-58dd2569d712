package com.xk.order.application.query.order;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.order.application.action.query.order.MaterialOrderAppDetailQuery;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.rsp.order.MaterialOrderAppDetailRsp;
import com.xk.order.interfaces.query.order.MaterialOrderAppQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialOrderAppQueryServiceImpl implements MaterialOrderAppQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @BusiCode
    @Override
    public Mono<MaterialOrderAppDetailRsp> detail(Mono<OrderNoRequireReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, MaterialOrderAppDetailQuery.class,
                MaterialOrderAppDetailRsp.class);
    }
}
