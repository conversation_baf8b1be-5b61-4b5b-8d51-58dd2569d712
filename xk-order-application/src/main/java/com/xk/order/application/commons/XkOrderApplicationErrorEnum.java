package com.xk.order.application.commons;

import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * application错误码
 * 13000-13999
 */
@Getter
@AllArgsConstructor
public enum XkOrderApplicationErrorEnum implements ExceptionIdentifier {

    APPLICATION_ERROR(13000, "application错误"),

    STOCK_NOT_ENOUGH(13001,"商品剩余库存不足"),
    UPDATING_FREE_QUOTA(13002,"正在更新免费额度，请稍后再试"),
    MATERIAL_AMOUNT_NOT_MATCH(13003,"免费额度发生变化,请刷新页面后重新下单"),
    BUY_TIME_OUT(13004,"提交订单失败,请稍后再试"),
    DATE_CONVERT_ERROR(13005, "日期转换错误"),
    ORDER_CANCEL_ERROR(13006, "订单取消错误,请稍后再试"),
    SPECIFICATION_NOT_EXIST(13007,"所选商品规格不存在"),
    GOODS_STATUS_ERROR(13008, "商品状态异常,请刷新后重试"),
    ORDER_ADDR_CHANGE(13009, "当前发货的订单有新地址变动，无法发货，请重新选择发货的订单。"),
    ORDER_ALREADY_CANCEL_ERROR(13011, "订单已取消"),
    ORDER_STATUS_ERROR(13012,"订单状态异常"),
    ORDER_STOCK_ERROR(13013,"订单库存异常"),
    ORDER_EMPTY(13014, "订单不存在，发货失败"),
    ORDER_CANCEL_STATUS_ERROR(13015,"订单状态错误,无法取消"),
    ORDER_PAY_TIME_OUT(13016,"订单已超时,无法支付"),
    TRADING_PASSWORD_ERROR(13017,"支付密码错误"),
    ORDER_CANCEL_LOGISTICS_ERROR(13018,"订单存在非待发货物流订单,无法取消"),
    /**
    * 客户端特殊展示部分
    */
    GOODS_NOT_LISTING(15001,"商品已下架,请重新选购"),
    GOODS_PRICE_UPDATE(15002,"价格已更新,请确认后下单"),
    COUPON_TIME_OUT(15003,"优惠券已失效,请重新选择"),
    LIMIT_COUNT_OVER(15004,"超过限购数量,最多可买[%s]件"),
    LIMIT_TIME_OVER(15005,"购买失败,[%s]小时内最多可买[%s]件"),
    MANAGER_BUY_BLOCK(15006,"账户状态异常,请联系客服"),
    PAY_LOCK_TIME_OUT(15007,"支付响应超时,请确认是否扣款"),
    PAY_TIME_OUT(15008,"订单已过期,请重新下单"),
    PAY_ALREADY_PAID(15009,"订单已支付,请勿重复操作"),
    PAY_MONEY_ERROR(15010,"订单金额错误,请重新下单"),
    FACE_NOT_PASS(15011,"未实名认证用户无法购买商品"),
    CORP_NEED_FOLLOW(15012,"购买商品需要先关注商家"),
    MALL_AMOUNT_NOT_MATCH(15013, "价格已更新,请确认后下单"),
    ORDER_CONFIRM_ERROR(15014, "订单确认失败,需要发货中才能确认收货"),
    GIFT_SEND_GOODS_ERROR(15015, "赠品已发货，无法撤回"),
    LOGISTICS_ORDER_ADDR_CHANGE_ERROR(15016, "商品已发货，无法修改地址"),
    CORP_BUY_BLOCK(15017,"账户状态异常,请联系客服"),
    FIRST_BUY_AND_COUPON(15018,"单个购买商品优惠和优惠券无法叠加使用"),
    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
