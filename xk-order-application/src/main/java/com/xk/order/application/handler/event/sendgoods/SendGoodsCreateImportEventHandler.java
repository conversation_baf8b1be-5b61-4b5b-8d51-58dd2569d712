package com.xk.order.application.handler.event.sendgoods;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.logistics.UpdateLogisticsOrderCommand;
import com.xk.order.application.action.command.order.UpdateOrderLogisticsOrderCommand;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateDetailEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateImportEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.interfaces.service.logistics.LogisticsOrderQueryService;
import com.xk.order.interfaces.service.logistics.LogisticsOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.List;

/**
 * 发货创建事件处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsCreateImportEventHandler extends AbstractEventVerticle<SendGoodsCreateImportEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;
    private EventRootService eventRootService;


    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<SendGoodsCreateImportEvent> mono) {
        return mono.flatMap(sendGoodsCreateImportEvent -> {
            return logisticsOrderRootQueryRepository.selectList(LogisticsOrderRoot.builder()
                            .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                            .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                    .orderValueObject(OrderValueObject.builder()
                                            .orderNo(sendGoodsCreateImportEvent.getOrderNo())
                                            .build())
                                    .build())
                    .build()).collectList().flatMap(logisticsOrderEntities -> {
                        if (logisticsOrderEntities == null ||  logisticsOrderEntities.isEmpty()) {
                            return publishErrOrder(sendGoodsCreateImportEvent,"订单号不存在");
                        }

                        if (!logisticsOrderEntities.getFirst().getLogisticsOrderStatus().equals(LogisticsOrderStatusEnum.TO_BE_SHIPPED)) {
                            return publishErrOrder(sendGoodsCreateImportEvent,"订单不是待发货状态，发货失败");
                        }

                        SendGoodsCreateEvent sendGoodsCreateEvent = SendGoodsCreateEvent.builder()
                                .identifier(EventRoot
                                        .getCommonsDomainEventIdentifier(SendGoodsCreateEvent.class))
                                .logisticsOrderId(sendGoodsCreateImportEvent.getLogisticsOrderId())
                                .orderNo(sendGoodsCreateImportEvent.getOrderNo())
                                .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED)
                                .logisticsNo(sendGoodsCreateImportEvent.getLogisticsNo())
                                .logisticsCorpName(sendGoodsCreateImportEvent.getLogisticsCorpName())
                                .addressSite(sendGoodsCreateImportEvent.getAddressSite())
                                .addressDetail(sendGoodsCreateImportEvent.getAddressDetail())
                                .fileTaskId(sendGoodsCreateImportEvent.getFileTaskId())
                                .goodsName(sendGoodsCreateImportEvent.getGoodsName())
                                .goodsCount(sendGoodsCreateImportEvent.getGoodsCount())
                                .sendGoodsTime(sendGoodsCreateImportEvent.getCreateTime())
                                .hasImport(true).build();
                        EventRoot eventRoot =
                                EventRoot.builder().domainEvent(sendGoodsCreateEvent).isQueue(true).build();
                        return eventRootService.publisheByMono(eventRoot);

            });
        }).then();
    }

    private Mono<Boolean> publishErrOrder(SendGoodsCreateImportEvent sendGoodsCreateImportEvent, String message) {
        SendGoodsCreateDetailEvent detailEvent = SendGoodsCreateDetailEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(SendGoodsCreateDetailEvent.class))
                        .logisticsOrderId(sendGoodsCreateImportEvent.getLogisticsOrderId()).orderNo(sendGoodsCreateImportEvent.getOrderNo())
                        .goodsName(sendGoodsCreateImportEvent.getGoodsName()).goodsCount(sendGoodsCreateImportEvent.getGoodsCount())
                        .logisticsCorpName(sendGoodsCreateImportEvent.getLogisticsCorpName())
                        .logisticsNo(sendGoodsCreateImportEvent.getLogisticsNo())
                        .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_SHIPPED)
                        .fileTaskId(sendGoodsCreateImportEvent.getFileTaskId())
                        .errRemark(message)
                        .build();

        EventRoot eventRoot = EventRoot.builder().domainEvent(detailEvent).isQueue(true).build();
        return eventRootService.publisheByMono(eventRoot);
    }
}
