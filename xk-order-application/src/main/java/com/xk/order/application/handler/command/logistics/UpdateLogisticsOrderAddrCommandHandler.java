package com.xk.order.application.handler.command.logistics;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.application.action.command.logistics.UpdateLogisticsOrderAddrCommand;
import com.xk.order.domain.event.logistics.LogisticsOrderUpdateAddressEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderAddrValueObject;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateLogisticsOrderAddrCommandHandler
        implements IActionCommandHandler<UpdateLogisticsOrderAddrCommand, Void> {

    private final Converter converter;
    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<UpdateLogisticsOrderAddrCommand> command) {
        Function<UpdateLogisticsOrderAddrCommand, Mono<Void>> publishEvent =
                root -> eventRootService
                        .publisheByMono(EventRoot.builder()
                                .domainEvent(LogisticsOrderUpdateAddressEvent.builder()
                                        .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                LogisticsOrderUpdateAddressEvent.class))
                                        .logisticsOrderId(root.getLogisticsOrderId())
                                        .userAddressId(root.getUserAddressId())
                                        .receivingMobile(root.getReceivingMobile())
                                        .consigneeName(root.getConsigneeName())
                                        .addressSite(root.getAddressSite())
                                        .addressDetail(root.getAddressDetail())
                                        .provinceCode(root.getProvinceCode())
                                        .cityCode(root.getCityCode())
                                        .districtCode(root.getDistrictCode())
                                        .logisticsOrderType(root.getLogisticsOrderType()).build())
                                .isQueue(true).build())
                        .doOnSuccess(v -> log.info("LogisticsOrderUpdateAddressEvent事件发布完成: {}",
                                root.getLogisticsOrderId()))
                        .then();

        return command
                .flatMap(updateLogisticsOrderAddrCommand -> ReadSynchronizationUtils.getUserIdMono()
                        .flatMap(userId -> logisticsOrderRootRepository
                                .update(LogisticsOrderRoot.builder()
                                        .identifier(LogisticsOrderIdentifier
                                                .builder()
                                                .logisticsOrderId(updateLogisticsOrderAddrCommand
                                                        .getLogisticsOrderId())
                                                .build())
                                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                .orderAddrValueObject(OrderAddrValueObject.builder()
                                                        .userAddressId(
                                                                updateLogisticsOrderAddrCommand
                                                                        .getUserAddressId())
                                                        .mobile(updateLogisticsOrderAddrCommand
                                                                .getReceivingMobile())
                                                        .consigneeName(
                                                                updateLogisticsOrderAddrCommand
                                                                        .getConsigneeName())
                                                        .addressSite(updateLogisticsOrderAddrCommand
                                                                .getAddressSite())
                                                        .addressDetail(
                                                                updateLogisticsOrderAddrCommand
                                                                        .getAddressDetail())
                                                        .provinceCode(
                                                                updateLogisticsOrderAddrCommand
                                                                        .getProvinceCode())
                                                        .cityCode(updateLogisticsOrderAddrCommand
                                                                .getCityCode())
                                                        .districtCode(
                                                                updateLogisticsOrderAddrCommand
                                                                        .getDistrictCode())
                                                        .build())
                                                .build())
                                        .build())
                                .thenReturn(updateLogisticsOrderAddrCommand))
                        .flatMap(publishEvent));

    }
}
