package com.xk.order.application.action.command.payment;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.enums.payment.PayDetailStatusEnum;
import com.xk.order.enums.payment.PayDirectionEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = PaymentDetailEntity.class)
})
public class CreatePaymentDetailCommand extends AbstractActionCommand {
    /**
     * 收付单号
     */
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 收付方向
     */
    private PayDirectionEnum payDirection;

    /**
     * 流水支付状态
     */
    private PayDetailStatusEnum status;

    /**
     * 收付金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private CommonStatusEnum deleted;

    /**
     * 创建人
     */
    private Long createId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
