package com.xk.order.application.handler.query.order;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.order.application.action.query.order.MerchantOrderAppGiftQuery;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderAppGiftSearchRsp;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MerchantOrderAppGiftQueryHandler
        implements IActionQueryHandler<MerchantOrderAppGiftQuery, Pagination> {

    private final OrderItemRootQueryRepository orderItemRootQueryRepository;

    @Override
    public Mono<Pagination> execute(Mono<MerchantOrderAppGiftQuery> mono) {
        return mono.flatMap(query -> {
            // 创建分页参数
            Pagination pagination = new Pagination();
            pagination.setLimit(query.getLimit());
            pagination.setOffset(query.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(query));

            return orderItemRootQueryRepository.searchGiftByPage(pagination).map(entity -> {
                MerchantOrderAppGiftSearchRsp rsp = new MerchantOrderAppGiftSearchRsp();
                rsp.setGiftBusinessName(entity.getGiftBusinessName());
                rsp.setOrderNo(entity.getOrderNo().getOrderNo());
                rsp.setGiftPrizeStatus(entity.getGiftPrizeStatus().getCode());
                rsp.setMemberAvatarAddr(entity.getGiftAddr());
                return rsp;
            }).collectList().map(list -> {
                pagination.setRecords(list);
                return pagination;
            });
        });
    }
}
