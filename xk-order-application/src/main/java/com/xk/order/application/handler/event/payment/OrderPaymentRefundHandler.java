package com.xk.order.application.handler.event.payment;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.service.payment.PaymentRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaymentRefundHandler extends AbstractEventVerticle<OrderPaymentRefundEvent> {

    private final LockRootService lockRootService;
    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> handle(Mono<OrderPaymentRefundEvent> event) {
        return event.flatMap(orderPaymentRefund -> {
            try {
                lockRootService.acquireTransactionObjectLock(ZookeeperLockObject.LOCKS_PAY_CALLBACK,
                        orderPaymentRefund.getOrderNo() + "_" + orderPaymentRefund.getPayNo());
            } catch (Throwable e) {
                return Mono.error(e);
            }
            if (orderPaymentRefund.isAutoRefund()) {
                log.info("OrderPaymentRefundHandler 不进行自动退款 {}", orderPaymentRefund.getOrderNo());
                return Mono.empty();
            }
            log.info("OrderPaymentRefundHandler 开始进行退款 {}", JSON.toJSONString(orderPaymentRefund));
            return paymentRootService
                    .refund(Mono
                            .just(PaymentRoot.builder()
                                    .paymentEntity(PaymentEntity.builder()
                                            .orderNo(orderPaymentRefund.getOrderNo())
                                            .payNo(orderPaymentRefund.getPayNo()).build())
                                    .build()))
                    .doOnSuccess(result -> log.info("OrderPaymentRefundHandler 退款结果 {}", result))
                    .then();
        });
    }
}
