package com.xk.order.application.handler.query.logistics;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.xk.order.application.action.query.logistics.LogisticsOrderDetailQuery;
import com.xk.order.application.action.query.order.OrderDetailQuery;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.rsp.logistics.LogisticsOrderDetailRspDto;
import com.xk.order.interfaces.dto.rsp.order.MerchantOrderDetailRsp;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class LogisticsOrderDetailQueryHandler
        implements IActionQueryHandler<LogisticsOrderDetailQuery, LogisticsOrderDetailRspDto> {

    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private final OrderRootService orderRootService;
    private final Converter converter;

    @Override
    public Mono<LogisticsOrderDetailRspDto> execute(Mono<LogisticsOrderDetailQuery> query) {
//        return query.flatMap(
//                logisticsOrderDetailQuery -> logisticsOrderRootRepository
//                        .selectById(
//                                LogisticsOrderRoot.builder()
//                                        .identifier(LogisticsOrderIdentifier.builder()
//                                                .logisticsOrderId(logisticsOrderDetailQuery.getLogisticsOrderId()).build())
//                                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
//                                                .logisticsOrderId(logisticsOrderDetailQuery.getLogisticsOrderId()).build())
//                                        .build())
//                        .map(logisticsOrderEntity -> {
//                            LogisticsOrderDetailRspDto.builder()
//                                    .build();
//
//
//                            return null;
//                        })
//        );
//
//
//        return mono.flatMap(query -> {
//            Mono<OrderRoot> getOrderRoot = orderRootService
//                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
//                    .switchIfEmpty(Mono.error(
//                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
//
//            Function<OrderRoot, Mono<SendGoodsRoot>> getDetail = root -> {
//                SendGoodsEntity sendGoodsEntity = converter.convert(root, SendGoodsEntity.class);
//                return Mono.just(SendGoodsRoot.builder()
//                        .identifier(SendGoodsIdentifier.builder().sendGoodsId(-1L).build())
//                        .sendGoodsEntity(sendGoodsEntity)
//                        .logisticsOrderIdentifierList(new ArrayList<>())
//                        .logisticsValueObjectList(new ArrayList<>()).build());
//            };
//
//            return getOrderRoot.flatMap(getDetail);
//        });
        return Mono.empty();
    }
}
