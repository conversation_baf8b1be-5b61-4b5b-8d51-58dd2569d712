package com.xk.order.application.action.command.logistics;


import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.interfaces.dto.req.logistics.LogisticsOrderAddressModifyReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsOrderAddressModifyReq.class)})
public class UpdateLogisticsOrderAddrCommand extends AbstractActionCommand {

    private Long logisticsOrderId;
    private Integer logisticsOrderType;
    private Long userAddressId;
    private String receivingMobile;
    private String consigneeName;
    private String addressSite;
    private String addressDetail;
    private String provinceCode;
    private String cityCode;
    private String districtCode;


}
