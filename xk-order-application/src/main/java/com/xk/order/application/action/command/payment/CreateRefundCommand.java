package com.xk.order.application.action.command.payment;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.domain.model.payment.entity.RefundEntity;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.interfaces.dto.rsp.payment.PaymentRsp;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = PaymentRsp.class),
        @AutoMapper(target = RefundEntity.class)
})
public class CreateRefundCommand extends AbstractActionCommand {

    /**
     * 收付单号
     */
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 退款状态
     */
    private RefundStatusEnum refundStatus;

    /**
     * 发起平台
     */
    private PlatformTypeEnum platformType;

    /**
     * 支付类型
     */
    private PaymentPayTypeEnum payType;

    /**
     * 收款完成时间
     */
    private Date payTime;

    /**
     * 收款金额
     */
    private String amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private CommonStatusEnum deleted;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
