package com.xk.order.application.handler.query.order;

import java.util.List;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.xk.order.application.action.query.order.OrderSearchByIdQuery;
import com.xk.order.application.dto.order.OrderDetailAppDto;
import com.xk.order.application.dto.order.OrderLogisticsOrderAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.rsp.order.LogisticsInfoRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchByIdRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class OrderSearchByIdQueryHandler
        implements IActionQueryHandler<OrderSearchByIdQuery, OrderSearchByIdRsp> {

    private final OrderRootService orderRootService;
    private final Converter converter;

    @Override
    public Mono<OrderSearchByIdRsp> execute(Mono<OrderSearchByIdQuery> mono) {
        return mono.flatMap(query -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderDetailAppDto>> getDetail = root -> {
                OrderDetailAppDto appDto =
                        converter.convert(root.getOrderEntity(), OrderDetailAppDto.class);
                converter.convert(root.getOrderPriceEntity(), appDto);
                converter.convert(root.getOrderAddressEntity(), appDto);
                converter.convert(root.getOrderPayEntity(), appDto);
                converter.convert(root.getOrderRefundEntity(), appDto);
                if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                    List<OrderLogisticsOrderAppDto> appDtoList =
                            converter.convert(root.getOrderLogisticsOrderEntityList(),
                                    OrderLogisticsOrderAppDto.class);
                    appDto.setLogisticsInfoDtoList(appDtoList);
                }

                return Mono.just(appDto);
            };

            return getOrderRoot.flatMap(getDetail).map(v -> {
                OrderSearchByIdRsp convert = converter.convert(v, OrderSearchByIdRsp.class);
                List<LogisticsInfoRsp> logisticsDetailRspList =
                        converter.convert(v.getLogisticsInfoDtoList(), LogisticsInfoRsp.class);
                convert.setLogisticsInfoRspList(logisticsDetailRspList);
                return convert;
            });
        });
    }
}
