package com.xk.order.application.handler.command.payment;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.application.action.command.payment.CreatePaymentCommand;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.repository.payment.PaymentRootRepository;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.support.OrderSequenceEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePaymentCommandHandler
        implements IActionCommandHandler<CreatePaymentCommand, Void> {

    private final Converter converter;
    private final PaymentRootRepository paymentRootRepository;
    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> execute(Mono<CreatePaymentCommand> command) {
        return paymentRootService.generateId(OrderSequenceEnum.O_PAYMENT).flatMap(id -> this
                .execute(command, PaymentEntity.class, converter::convert, paymentEntity -> {
                    paymentEntity.setPaymentId(id);
                    paymentEntity.setDeleted(CommonStatusEnum.DISABLE);
                    return paymentEntity;
                }, paymentRootRepository::insertPayment));
    }
}
