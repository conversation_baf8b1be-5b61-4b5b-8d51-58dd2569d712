package com.xk.order.application.handler.command.logistics;

import java.util.Date;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.application.action.command.logistics.UpdateLogisticsNoCommand;
import com.xk.order.domain.event.sendgoods.SendGoodsEditEvent;
import com.xk.order.domain.model.logisticsSendGoods.LogisticsSendGoodsOrderRoot;
import com.xk.order.domain.model.logisticsSendGoods.entity.LogisticsSendGoodsEntity;
import com.xk.order.domain.model.logisticsSendGoods.id.LogisticsSendGoodsIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.repository.logisticsSendGoods.LogisticsSendGoodsRootQueryRepository;
import com.xk.order.domain.repository.sendgoods.SendGoodsRootQueryRepository;
import com.xk.order.domain.repository.sendgoods.SendGoodsRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateLogisticsNoCommandHandler
        implements IActionCommandHandler<UpdateLogisticsNoCommand, Void> {

    private final Converter converter;
    private final LogisticsSendGoodsRootQueryRepository logisticsSendGoodsRootQueryRepository;
    private final SendGoodsRootQueryRepository sendGoodsRootQueryRepository;
    private final SendGoodsRootRepository sendGoodsRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<UpdateLogisticsNoCommand> command) {
        return command
                .flatMap(updateLogisticsOrderCommand -> ReadSynchronizationUtils.getUserIdMono()
                        .flatMap(userId -> logisticsSendGoodsRootQueryRepository
                                .selectById(LogisticsSendGoodsOrderRoot.builder()
                                        .identifier(LogisticsSendGoodsIdentifier.builder().id(-1L)
                                                .build())
                                        .logisticsSendGoodsEntity(LogisticsSendGoodsEntity.builder()
                                                .logisticsOrderId(updateLogisticsOrderCommand
                                                        .getLogisticsOrderId())
                                                .build())
                                        .build())
                                .collectList().flatMap(list -> {
                                    if (CollectionUtils.isNotEmpty(list)) {
                                        return sendGoodsRootQueryRepository
                                                .selectById(SendGoodsRoot.builder()
                                                        .identifier(SendGoodsIdentifier.builder()
                                                                .sendGoodsId(list.getFirst()
                                                                        .getSendGoodsId())
                                                                .build())
                                                        .sendGoodsEntity(SendGoodsEntity.builder()
                                                                .sendGoodsId(list.getFirst()
                                                                        .getSendGoodsId())
                                                                .build())
                                                        .build());
                                    }
                                    return Mono.empty();
                                })
                                .flatMap(sendGoodsEntity -> sendGoodsRootRepository
                                        .update(SendGoodsRoot.builder()
                                                .identifier(SendGoodsIdentifier.builder()
                                                        .sendGoodsId(
                                                                sendGoodsEntity.getSendGoodsId())
                                                        .build())
                                                .sendGoodsEntity(SendGoodsEntity.builder()
                                                        .sendGoodsId(
                                                                sendGoodsEntity.getSendGoodsId())
                                                        .logisticsNo(updateLogisticsOrderCommand
                                                                .getLogisticsNo())
                                                        .logisticsCorpName(
                                                                updateLogisticsOrderCommand
                                                                        .getLogisticsCorpName())
                                                        .updateId(userId).updateTime(new Date())
                                                        .build())
                                                .build())
                                        .then(Mono.defer(() -> {
                                            EventRoot eventRoot = EventRoot.builder()
                                                    .domainEvent(SendGoodsEditEvent.builder()
                                                            .identifier(EventRoot
                                                                    .getCommonsDomainEventIdentifier(
                                                                            SendGoodsEditEvent.class))
                                                            .logisticsOrderId(
                                                                    updateLogisticsOrderCommand
                                                                            .getLogisticsOrderId())
                                                            .sendGoodsId(sendGoodsEntity
                                                                    .getSendGoodsId())
                                                            .logisticsCorpName(
                                                                    updateLogisticsOrderCommand
                                                                            .getLogisticsCorpName())
                                                            .logisticsNo(updateLogisticsOrderCommand
                                                                    .getLogisticsNo())
                                                            .build())
                                                    .isQueue(true).build();
                                            return eventRootService.publisheByMono(eventRoot)
                                                    .then();
                                        })))));

    }
}
