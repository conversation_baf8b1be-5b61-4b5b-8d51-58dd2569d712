package com.xk.order.application.handler.event.gift;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.IntegerIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.model.res.SysResourceEntity;
import com.xk.domain.repository.res.SysResourceRootQueryRepository;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.domain.event.gift.GiftReportUpdateEvent;
import com.xk.goods.interfaces.dto.req.gift.GiftReportInnerSearchReq;
import com.xk.goods.interfaces.dto.res.gift.GiftSearchRep;
import com.xk.goods.interfaces.query.gift.GiftReportQueryService;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.action.command.order.UpdateOrderGiftCommand;
import com.xk.order.domain.event.order.OrderItemUpdatePrizedEvent;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.enums.order.OrderGiftPrizeStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class GiftReportUpdateEventHandler extends AbstractEventVerticle<GiftReportUpdateEvent> {

    private final OrderItemRootService orderItemRootService;
    private final GiftReportQueryService giftReportQueryService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final SysResourceRootQueryRepository sysResourceRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<GiftReportUpdateEvent> mono) {
        return mono.flatMap(event -> {
            Mono<GiftSearchRep> getGiftRep = giftReportQueryService
                    .innerSearch(
                            Mono.just(GiftReportInnerSearchReq.builder().goodsId(event.getGoodsId())
                                    .giftReportId(event.getGiftReportId()).build()))
                    .filter(CollectionUtils::isNotEmpty).map(List::getFirst);

            BiFunction<OrderGiftEntity, GiftSearchRep, Mono<Void>> publishEvent =
                    (entity, rep) -> Flux.fromIterable(event.getBaseBusinessResList())
                            .map(BaseBusinessRes::getResId)
                            .flatMap(resId -> sysResourceRootQueryRepository
                                    .findById(IntegerIdentifier.builder().id(resId).build()))
                            .map(SysResourceEntity::getAddr).collectList()
                            .defaultIfEmpty(new ArrayList<>())
                            .flatMap(giftAddr -> eventRootService.publisheByMono(EventRoot.builder()
                                    .domainEvent(OrderItemUpdatePrizedEvent.builder()
                                            .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                                    OrderItemUpdatePrizedEvent.class))
                                            .giftReportId(event.getGiftReportId())
                                            .orderNo(entity.getOrderNo().getOrderNo())
                                            .orderItemId(entity.getOrderItemId().getOrderItemId())
                                            .giftName(rep.getSerialItemName()).giftAddr(giftAddr)
                                            .build())
                                    .isQueue(true).build()))
                            .then();

            Function<GiftSearchRep, Mono<OrderIdentifier>> updateOrderGift =
                    rep -> orderItemRootService
                            .findGiftEntityByGoodsIdAndBusinessId(
                                    OrderGiftEntity.builder().giftBusinessId(rep.getSerialItemId())
                                            .goodsId(event.getGoodsId()).build())
                            .flatMap(giftEntity -> {
                                giftEntity.setGiftPrizeStatus(OrderGiftPrizeStatusEnum.PRIZED);
                                return commandDispatcher
                                        .executeCommand(Mono.just(giftEntity),
                                                UpdateOrderGiftCommand.class)
                                        .then(publishEvent.apply(giftEntity, rep))
                                        .thenReturn(giftEntity.getOrderNo());
                            });

            Function<OrderIdentifier, Mono<Void>> updateOrder =
                    orderNo -> commandDispatcher.executeCommand(Mono.just(new UpdateOrderCommand()),
                            UpdateOrderCommand.class, command -> {
                                command.setOrderNo(orderNo.getOrderNo());
                                command.setPrizeStatus(CommonStatusEnum.ENABLE.getCode());
                                return command;
                            });

            return getGiftRep.flatMap(updateOrderGift).flatMap(updateOrder).then();
        });
    }
}
