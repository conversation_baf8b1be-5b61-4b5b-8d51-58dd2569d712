package com.xk.order.application.handler.query.order;

import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.object.goods.SpecificationValueObject;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.order.application.action.query.order.MallOrderAppDetailQuery;
import com.xk.order.application.dto.order.OrderDetailAppDto;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.rsp.order.MallOrderAppDetailRsp;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MallOrderAppDetailQueryHandler
        implements IActionQueryHandler<MallOrderAppDetailQuery, MallOrderAppDetailRsp> {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final SelectorRootService selectorRootService;
    private final Converter converter;

    @Override
    public Mono<MallOrderAppDetailRsp> execute(Mono<MallOrderAppDetailQuery> mono) {
        return mono.flatMap(query -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderDetailAppDto>> getDetail =
                    root -> ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> {
                        if (!Objects.equals(root.getOrderEntity().getUserId(), userId)) {
                            return Mono.error(new SystemWrapperThrowable(
                                    SystemErrorEnum.UNSUPPORTED_OPERATION));
                        }
                        OrderDetailAppDto appDto =
                                converter.convert(root.getOrderEntity(), OrderDetailAppDto.class);
                        converter.convert(root.getOrderPriceEntity(), appDto);
                        converter.convert(root.getOrderAddressEntity(), appDto);
                        converter.convert(root.getOrderPayEntity(), appDto);
                        converter.convert(root.getOrderRefundEntity(), appDto);
                        if (CollectionUtils.isNotEmpty(root.getOrderLogisticsOrderEntityList())) {
                            converter.convert(root.getOrderLogisticsOrderEntityList().getFirst(),
                                    appDto);
                        }
                        appDto.setCreateTime(
                                root.getOrderEntity().getCreateValObj().getCreateTime());

                        return Mono.just(appDto);
                    });

            Function<OrderDetailAppDto, Mono<OrderDetailAppDto>> getGoodsInfo =
                    appDto -> orderItemRootService
                            .searchEntityByOrderNo(
                                    OrderIdentifier.builder().orderNo(appDto.getOrderNo()).build())
                            .collectList().map(v -> {
                                OrderItemEntity first = v.getFirst();
                                appDto.setGoodsId(first.getGoodsId());
                                appDto.setGoodsName(first.getGoodsName());
                                appDto.setUnitPrice(first.getUnitPrice());
                                return appDto;
                            });

            Function<OrderDetailAppDto, Mono<OrderDetailAppDto>> getGoodsImage =
                    appDto -> selectorRootService.getGoodsObject(appDto.getGoodsId())
                            .flatMap(goodsObjectRoot -> {
                                appDto.setGoodsImages(goodsObjectRoot.getResList().stream()
                                        .filter(v -> BusinessResTypeEnum.PRODUCT_PICTURE.name()
                                                .equals(v.getResType()))
                                        .findFirst().map(GoodsResValueObject::getResAddr)
                                        .orElse(null));
                                if (CollectionUtils
                                        .isNotEmpty(goodsObjectRoot.getSpecificationList())) {
                                    appDto.setUnitType(goodsObjectRoot.getSpecificationList()
                                            .stream().findFirst()
                                            .map(SpecificationValueObject::getUnitType)
                                            .orElse(null));
                                }
                                return Mono.just(appDto);
                            });

            return getOrderRoot.flatMap(getDetail).flatMap(getGoodsInfo).flatMap(getGoodsImage)
                    .map(v -> converter.convert(v, MallOrderAppDetailRsp.class));
        });
    }
}
