package com.xk.order.application.action.command.order;

import java.util.Collections;
import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.domain.event.logistics.LogisticsOrderCreateEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderLogisticsOrderEntity;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = OrderLogisticsOrderEntity.class, reverseConvertGenerate = false,
                uses = {LogisticsOrderTypeEnumConvertor.class}),
        @AutoMapper(target = LogisticsOrderCreateEvent.class, convertGenerate = false,
                uses = {LogisticsOrderTypeEnumConvertor.class})})
public class CreateOrderLogisticsOrderCommand extends AbstractActionCommand {

    /**
     * 物流订单id
     */
    private Long logisticsOrderId;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 订单id
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    public OrderRoot buildRoot(Converter converter) {
        OrderLogisticsOrderEntity entity = converter.convert(this, OrderLogisticsOrderEntity.class);
        entity.setLogisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_SHIPPED);
        return OrderRoot.builder().identifier(OrderIdentifierConvertor.map(orderNo))
                .orderLogisticsOrderEntityList(Collections.singletonList(entity)).build();
    }
}
