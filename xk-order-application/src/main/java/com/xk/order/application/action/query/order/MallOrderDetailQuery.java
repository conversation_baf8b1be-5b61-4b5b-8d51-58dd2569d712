package com.xk.order.application.action.query.order;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = OrderNoRequireReq.class, convertGenerate = false)})
public class MallOrderDetailQuery implements IActionQuery {

    /**
     * 订单编号
     */
    private String orderNo;
}
