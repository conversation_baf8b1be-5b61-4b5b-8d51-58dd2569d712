package com.xk.order.application.query.order;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.order.application.action.query.order.MallOrderAppDetailQuery;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.rsp.order.MallOrderAppDetailRsp;
import com.xk.order.interfaces.query.order.MallOrderAppQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MallOrderAppQueryServiceImpl implements MallOrderAppQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @BusiCode
    @Override
    public Mono<MallOrderAppDetailRsp> detail(Mono<OrderNoRequireReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, MallOrderAppDetailQuery.class,
                MallOrderAppDetailRsp.class);
    }
}
