package com.xk.order.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@ComponentScan({"com.xk.order.application.convertor"
        , "com.xk.order.application.query"
        , "com.xk.order.application.service"
        , "com.xk.order.application.handler"
        , "com.xk.order.application.task"})
public class XkOrderApplicationConfig {
}
