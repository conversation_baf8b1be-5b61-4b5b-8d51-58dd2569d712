package com.xk.order.application.handler.command.payment;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.payment.CreatePaymentDetailCommand;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.repository.payment.PaymentRootRepository;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.domain.support.OrderSequenceEnum;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePaymentDetailCommandHandler implements IActionCommandHandler<CreatePaymentDetailCommand,Void> {

    private final Converter converter;
    private final PaymentRootRepository paymentRootRepository;
    private final PaymentRootService paymentRootService;


    @Override
    public Mono<Void> execute(Mono<CreatePaymentDetailCommand> command) {
        return paymentRootService.generateId(OrderSequenceEnum.O_PAYMENT_DETAIL).flatMap(id ->{
           return this.execute(command, PaymentDetailEntity.class,converter::convert, paymentEntity -> {
               paymentEntity.setPaymentId(id);
               return paymentEntity;
           },paymentRootRepository::insertPaymentDetail);
        });
    }
}
