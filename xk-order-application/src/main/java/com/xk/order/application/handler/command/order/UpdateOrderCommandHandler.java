package com.xk.order.application.handler.command.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateOrderCommandHandler implements IActionCommandHandler<UpdateOrderCommand, Void> {

    private final Converter converter;
    private final OrderRootService orderRootService;

    @Override
    public Mono<Void> execute(Mono<UpdateOrderCommand> mono) {
        return mono.flatMap(command -> orderRootService
                .getRoot(OrderIdentifier.builder().orderNo(command.getOrderNo()).build())
                .switchIfEmpty(Mono
                        .error(new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .map(root -> command.getRoot(root, converter))
                .flatMap(root -> orderRootService.updateRoot(Mono.just(root))));
    }
}
