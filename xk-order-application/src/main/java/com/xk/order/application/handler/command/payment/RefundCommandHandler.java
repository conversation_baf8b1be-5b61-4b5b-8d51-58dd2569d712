package com.xk.order.application.handler.command.payment;

import java.util.Date;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.application.action.command.payment.RefundCommand;
import com.xk.order.domain.model.payment.PaymentRoot;
import com.xk.order.domain.model.payment.entity.PaymentDetailEntity;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.service.payment.PaymentRootService;
import com.xk.order.enums.payment.PayDetailStatusEnum;
import com.xk.order.enums.payment.PayDirectionEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefundCommandHandler implements IActionCommandHandler<RefundCommand, Void> {

    private final Converter converter;
    private final PaymentRootService paymentRootService;

    @Override
    public Mono<Void> execute(Mono<RefundCommand> command) {
        return command.flatMap(refundCommand -> {
            PaymentDetailEntity paymentDetailEntity =
                    PaymentDetailEntity.builder().orderNo(refundCommand.getOrderNo())
                            .payNo(refundCommand.getPayNo()).payDirection(PayDirectionEnum.REFUND)
                            .status(PayDetailStatusEnum.SUCCEED).amount(refundCommand.getAmount())
                            .remark(refundCommand.getRemark()).deleted(CommonStatusEnum.DISABLE)
                            .createId(refundCommand.getUserId()).createTime(new Date()).build();
            PaymentEntity paymentEntity =
                    PaymentEntity.builder().orderNo(refundCommand.getOrderNo())
                            .payNo(refundCommand.getPayNo()).payType(refundCommand.getPayType())
                            .platformType(refundCommand.getPlatformType())
                            .payTime(refundCommand.getPayTime()).amount(refundCommand.getAmount())
                            .remark(refundCommand.getRemark()).deleted(CommonStatusEnum.DISABLE)
                            .createId(refundCommand.getUserId()).createTime(new Date()).build();
            PaymentRoot paymentRoot = PaymentRoot.builder().paymentEntity(paymentEntity)
                    .paymentDetailEntity(paymentDetailEntity).build();
            return paymentRootService.refund(Mono.just(paymentRoot)).then();
        });
    }
}
