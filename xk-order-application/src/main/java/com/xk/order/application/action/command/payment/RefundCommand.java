package com.xk.order.application.action.command.payment;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.enums.payment.RefundTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class RefundCommand extends AbstractActionCommand {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 退款类型
     */
    private RefundTypeEnum refundType;

    /**
     * 退款状态
     */
    private RefundStatusEnum refundStatus;

    /**
     * 发起平台
     */
    private PlatformTypeEnum platformType;

    /**
     * 支付类型
     */
    private PaymentPayTypeEnum payType;

    /**
     * 收款完成时间
     */
    private Date payTime;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 收款金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付单号
     */
    private String payNo;


    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 商户名
     */
    private String corpName;
}
