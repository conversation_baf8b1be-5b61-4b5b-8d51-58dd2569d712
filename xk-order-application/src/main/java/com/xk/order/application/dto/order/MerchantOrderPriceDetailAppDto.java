package com.xk.order.application.dto.order;

import java.io.Serializable;
import java.util.Date;

import com.xk.order.interfaces.dto.rsp.order.MerchantOrderPriceDetailRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = MerchantOrderPriceDetailRsp.class, reverseConvertGenerate = false)})
public class MerchantOrderPriceDetailAppDto implements Serializable {

    /**
     * 规格id
     */
    private Long specificationId;

    /**
     * 条目位置id
     */
    private Integer itemPosition;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格总金额
     */
    private Long specTotalAmount;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 规格合计金额
     */
    private Long specPayAmount;

    /**
     * 规格满减减免
     */
    private Long specDiscountAmount;

    /**
     * 规格优惠券减免
     */
    private Long specCouponAmount;

    /**
     * 规格首购优惠减免
     */
    private Long specFirstBuyDiscountAmount;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsImage;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * 截止时间
     */
    private Date planDownTime;

    /**
     * 是否开启限购
     */
    private Integer limitStatus;

    /**
     * 限购数量
     */
    private Integer limitAmount;

    /**
     * 是否开启定时限购
     */
    private Integer limitTimeStatus;

    /**
     * 限购1个间隔时长(小时)
     */
    private Integer limitTimeInterval;

    /**
     * 板块类型
     */
    private Integer blockType;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 分发id
     */
    private Long distributionId;

    /**
     * 随机条目id
     */
    private Long randomItemId;
}
