package com.xk.order.application.task.order;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.domain.event.order.OrderCancelJobEvent;
import com.xk.order.domain.event.order.OrderDelayCancelJobEvent;
import com.xk.order.domain.event.task.OrderImportScanEvent;
import com.xk.order.interfaces.task.order.OrderJobService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderJobServiceImpl implements OrderJobService {

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Void> commitCancelOrderEvent(String format) {
        OrderCancelJobEvent cancelJobEvent = OrderCancelJobEvent.builder()
                .identifier(EventRoot.getCommonsDomainEventIdentifier(OrderCancelJobEvent.class))
                .timeFormat(format).build();
        EventRoot eventRoot = EventRoot.builder().domainEvent(cancelJobEvent).isTry(false).build();
        return eventRootService.publisheByMono(eventRoot).then();
    }

    @Override
    public Mono<Void> commitDelayCancelOrderEvent(String format) {
        OrderDelayCancelJobEvent cancelJobEvent = OrderDelayCancelJobEvent.builder()
                .identifier(EventRoot.getCommonsDomainEventIdentifier(OrderCancelJobEvent.class))
                .timeFormat(format).build();
        EventRoot eventRoot = EventRoot.builder().domainEvent(cancelJobEvent).isTry(false).build();
        return eventRootService.publisheByMono(eventRoot).then();
    }

    @Override
    public Mono<Void> orderImportScan() {
        OrderImportScanEvent orderImportScanEvent = OrderImportScanEvent.builder()
                .identifier(EventRoot.getCommonsDomainEventIdentifier(OrderImportScanEvent.class))
                .build();
        EventRoot eventRoot =
                EventRoot.builder().domainEvent(orderImportScanEvent).isTry(false).build();
        return eventRootService.publisheByMono(eventRoot).then();
    }
}
