package com.xk.order.application.support;

import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.wrapper.ApplicationWrapperThrowable;

/**
 * @author: killer
 **/
public class XkOrderApplicationException extends ApplicationWrapperThrowable {

    public XkOrderApplicationException(ExceptionIdentifier exceptionIdentifier,
            Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkOrderApplicationException(ExceptionIdentifier exceptionIdentifier, Object... params) {
        super(exceptionIdentifier, String.format(exceptionIdentifier.getDefaultMessage(), params));
    }

    public XkOrderApplicationException(ExceptionIdentifier exceptionIdentifier) {
        super(exceptionIdentifier);
    }

}
