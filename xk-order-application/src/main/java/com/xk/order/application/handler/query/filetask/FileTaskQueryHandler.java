package com.xk.order.application.handler.query.filetask;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.infrastructure.commons.util.CollectionHelper;
import com.xk.domain.repository.filetask.FileTaskRootQueryRepository;
import com.xk.order.application.action.query.filetask.FileTaskQuery;
import com.xk.order.interfaces.dto.rsp.filetask.FileTaskRspDto;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class FileTaskQueryHandler implements IActionQueryHandler<FileTaskQuery, Pagination> {

    private final FileTaskRootQueryRepository fileTaskRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<Pagination> execute(Mono<FileTaskQuery> query) {
        // todo 分页查询参考案例
        Function<FileTaskQuery, Mono<Pagination>> getReq = fileTaskQuery -> {
            Pagination pagination = new Pagination();
            pagination.setLimit(fileTaskQuery.getLimit());
            pagination.setOffset(fileTaskQuery.getOffset());
            pagination.setCriteria(CollectionHelper.converBeanToMap(fileTaskQuery));
            return Mono.just(pagination);
        };

        return query.flatMap(getReq)
                .flatMap(pagination -> fileTaskRootQueryRepository.selectList(pagination)
                        .flatMap(fileTaskEntity -> Mono
                                .just(converter.convert(fileTaskEntity, FileTaskRspDto.class)))
                        .collectList().flatMap(list -> {
                            pagination.setRecords(list);
                            return Mono.just(pagination);
                        }));
    }

}
