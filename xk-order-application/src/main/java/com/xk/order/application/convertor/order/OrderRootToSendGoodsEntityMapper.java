package com.xk.order.application.convertor.order;

import java.util.Date;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderAddressEntity;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.valobj.SendOrderAddrValueObject;
import com.xk.order.enums.sendGoods.LogisticsStatusEnum;

import io.github.linpeilie.BaseMapper;

/**
 * OrderRoot到SendGoodsEntity的转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OrderRootToSendGoodsEntityMapper implements BaseMapper<OrderRoot, SendGoodsEntity> {

    @Override
    public SendGoodsEntity convert(OrderRoot source) {
        return convert(source, new SendGoodsEntity());
    }

    @Override
    public SendGoodsEntity convert(OrderRoot source, SendGoodsEntity target) {
        if (source == null) {
            return target;
        }

        // 如果target为null，创建新的SendGoodsEntity对象
        if (target == null) {
            target = new SendGoodsEntity();
        }

        // 收货地址信息 - 从OrderAddressEntity转换
        if (source.getOrderAddressEntity() != null) {
            OrderAddressEntity addressEntity = source.getOrderAddressEntity();

            // 创建收货地址值对象
            SendOrderAddrValueObject sendOrderAddr = SendOrderAddrValueObject.builder()
                    .addressId(addressEntity.getUserAddressId()).mobile(addressEntity.getMobile())
                    .province(addressEntity.getProvinceCode()).city(addressEntity.getCityCode())
                    .district(addressEntity.getDistrictCode())
                    .addressSite(addressEntity.getAddressSite())
                    .addressDetail(addressEntity.getAddressDetail())
                    .name(addressEntity.getConsigneeName()).build();

            target.setSendOrderAddr(sendOrderAddr);
        } else {
            target.setSendOrderAddr(null);
        }

        // 物流状态信息
        target.setLogisticsStatus(LogisticsStatusEnum.TO_BE_COLLECTED); // 默认待揽收

        // 用户信息
        target.setUserId(source.getOrderEntity().getUserId());

        // 商家信息
        target.setCorpId(source.getOrderEntity().getCorpId());

        // 价格信息
        target.setPrice(0L); // OrderRoot中没有运费价格
        target.setSendGoodsTime(new Date());
        target.setCreateTime(new Date());

        target.setCancelDeadlineTime(source.getOrderEntity().getCancelDeadlineTime());

        return target;
    }
}
