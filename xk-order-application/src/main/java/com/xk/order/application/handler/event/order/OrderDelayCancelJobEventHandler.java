package com.xk.order.application.handler.event.order;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderDelayCancelEvent;
import com.xk.order.domain.event.order.OrderDelayCancelJobEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.service.order.OrderRootService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDelayCancelJobEventHandler
        extends AbstractEventVerticle<OrderDelayCancelJobEvent> {
    private final OrderRootService orderRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderDelayCancelJobEvent> mono) {
        return mono.flatMap(event -> processOrderCancel(event.getTimeFormat()));
    }

    private Mono<Void> processOrderCancel(String timeFormat) {
        return Mono.defer(() -> {
            SimpleDateFormat sdf = new SimpleDateFormat(OrderRoot.Constant.TIME_PATTERN);
            try {
                Date date = sdf.parse(timeFormat);
                OrderEntity orderEntity =
                        OrderEntity.builder().orderNo("-1").delayCancelDeadlineTime(date).build();
                OrderRoot orderRoot = OrderRoot.builder().identifier(orderEntity.getIdentifier())
                        .orderEntity(orderEntity).build();
                return orderRootService
                        .getOrderDelayCancelCache(orderRoot).switchIfEmpty(orderRootService
                                .deleteDelayCancelQueue(orderRoot).then(Mono.empty()))
                        .flatMap(orderIdentifier -> {
                            String orderNo = orderIdentifier.getOrderNo();
                            OrderDelayCancelEvent orderCancelEvent = OrderDelayCancelEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderDelayCancelEvent.class))
                                    .orderNo(orderNo).retryCount(0).build();
                            EventRoot eventRoot = EventRoot.builder().domainEvent(orderCancelEvent)
                                    .isTry(false).build();
                            return eventRootService.publisheByMono(eventRoot)
                                    .doOnSuccess(v -> log.info("删除延时取消订单缓存: {}", orderNo))
                                    .then(processOrderCancel(timeFormat));
                        });
            } catch (ParseException e) {
                return Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.DATE_CONVERT_ERROR));
            }
        });
    }
}
