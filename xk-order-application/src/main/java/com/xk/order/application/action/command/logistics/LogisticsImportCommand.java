package com.xk.order.application.action.command.logistics;


import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.interfaces.dto.req.logistics.LogisticsImportReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsImportReqDto.class, convertGenerate = false)})
public class LogisticsImportCommand extends AbstractActionCommand {

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;

    /**
     * 文件名
     */
    private String fileName;

    private Integer fileTaskBizType;

    /**
     * 文件流
     */
    @NotNull(message = "文件流不能为空")
    private String excelBase64;

}
