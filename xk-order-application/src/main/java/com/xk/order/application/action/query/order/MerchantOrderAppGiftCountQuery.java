package com.xk.order.application.action.query.order;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.dto.req.order.MerchantOrderAppGiftCountReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MerchantOrderAppGiftCountReq.class)})
public class MerchantOrderAppGiftCountQuery implements IActionQuery {

    /**
     * 商品ID
     */
    private Long goodsId;

}
