package com.xk.order.application.dto.order;

import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.interfaces.dto.rsp.order.OrderGiftInfoRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderGiftEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderGiftInfoRsp.class, reverseConvertGenerate = false)})
public class OrderGiftAppDto {

    /**
     * 订单赠品ID
     */
    private Long orderGiftId;

    /**
     * 赠品业务名称
     */
    private String giftBusinessName;

    /**
     * 赠品地址
     */
    private String giftAddr;
}
