package com.xk.order.application.handler.query.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.xk.order.application.action.query.order.MerchantOrderAppGiftCountQuery;
import com.xk.order.domain.model.CreateValObj;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class MerchantOrderAppGiftCountQueryHandler
        implements IActionQueryHandler<MerchantOrderAppGiftCountQuery, Long> {

    private final OrderItemRootQueryRepository orderItemRootQueryRepository;

    @Override
    public Mono<Long> execute(Mono<MerchantOrderAppGiftCountQuery> mono) {
        return mono.flatMap(query -> ReadSynchronizationUtils.getUserIdMono()
                .flatMap(userId -> orderItemRootQueryRepository.searchGiftByCount(OrderGiftEntity
                        .builder().goodsId(query.getGoodsId())
                        .createValObj(CreateValObj.builder().createId(userId).build()).build())));
    }
}
