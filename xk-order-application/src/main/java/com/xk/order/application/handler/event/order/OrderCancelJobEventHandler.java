package com.xk.order.application.handler.event.order;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderCancelEvent;
import com.xk.order.domain.event.order.OrderCancelJobEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelJobEventHandler extends AbstractEventVerticle<OrderCancelJobEvent> {

    private final OrderRootService orderRootService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderCancelJobEvent> event) {
        return event.flatMap(data -> processOrderCancel(data.getTimeFormat()));
    }

    private Mono<Void> processOrderCancel(String timeFormat) {
        return Mono.defer(() -> {
            SimpleDateFormat sdf = new SimpleDateFormat(OrderRoot.Constant.TIME_PATTERN);
            try {
                Date date = sdf.parse(timeFormat);
                OrderEntity orderEntity =
                        OrderEntity.builder().orderNo("-1").cancelDeadlineTime(date).build();
                OrderRoot orderRoot = OrderRoot.builder().identifier(orderEntity.getIdentifier())
                        .orderEntity(orderEntity).build();
                return orderRootService.getOrderCancelCache(orderRoot)
                        .switchIfEmpty(
                                orderRootService.deleteCancelQueue(orderRoot).then(Mono.empty()))
                        .flatMap(orderIdentifier -> {
                            String orderNo = orderIdentifier.getOrderNo();
                            OrderCancelEvent orderCancelEvent = OrderCancelEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderCancelEvent.class))
                                    .orderNo(orderNo).retryCount(0)
                                    .orderCancelType(OrderCancelTypeEnum.TIME_OUT_CANCEL)
                                    .updateId(-1L).build();
                            EventRoot eventRoot = EventRoot.builder().domainEvent(orderCancelEvent)
                                    .isTry(false).build();
                            return eventRootService.publisheByMono(eventRoot)
                                    .doOnSuccess(v -> log.info("删除取消订单缓存: {}", orderNo))
                                    .then(processOrderCancel(timeFormat));
                        });
            } catch (ParseException e) {
                return Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.DATE_CONVERT_ERROR));
            }
        });
    }
}
