package com.xk.order.application.service.filetask;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.order.application.action.query.filetask.FileTaskQuery;
import com.xk.order.interfaces.dto.req.filetask.FileTaskReqDto;
import com.xk.order.interfaces.service.filetask.FileTaskQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileTaskQueryServiceImpl implements FileTaskQueryService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    @BusiCode
    @Override
    public Mono<Pagination> list(Mono<FileTaskReqDto> mono) {
        return queryDispatcher.executeQuery(mono, FileTaskQuery.class, Pagination.class);
    }
}
