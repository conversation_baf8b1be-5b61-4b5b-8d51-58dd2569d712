package com.xk.order.application.service.logistics;

import java.util.List;
import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.object.corp.CorpObjectRoot;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.domain.service.filetask.FileTaskRootDomainService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.order.application.action.query.logistics.LogisticsByIdQuery;
import com.xk.order.application.action.query.logistics.LogisticsOrderDetailQuery;
import com.xk.order.application.action.query.order.OrderDetailQuery;
import com.xk.order.application.action.query.sendgoods.SendGoodsByIdQuery;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.enums.logistics.GoodsSourceTypeEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.interfaces.dto.req.logistics.*;
import com.xk.order.interfaces.dto.rsp.logistics.*;
import com.xk.order.interfaces.dto.rsp.order.OrderSearchItemByIdRsp;
import com.xk.order.interfaces.service.logistics.LogisticsOrderQueryService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple4;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsOrderQueryServiceImpl implements LogisticsOrderQueryService {

    private final FileTaskRootDomainService fileTaskRootDomainService;

    private final OrderItemRootService orderItemRootService;

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    private final SelectorRootService selectorRootService;

    private final Converter converter;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Pagination> log(Mono<LogisticsLogReqDto> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(10);
        pagination.setRecords(List.of(new LogisticsLogRspDto()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<Pagination> orderViewCorp(Mono<LogisticsOrderViewReqDto> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(10);
        pagination.setRecords(List.of(new LogisticsOrderViewRspDto()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<Pagination> userViewCorp(Mono<LogisticsUserViewReqDto> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(10);
        pagination.setRecords(List.of(new LogisticsUserViewRspDto()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<LogisticsOrderDetailRspDto> orderViewDetail(Mono<LogisticsOrderDetailReqDto> mono) {
        return queryDispatcher.executeQuery(mono, LogisticsOrderDetailQuery.class, LogisticsOrderDetailRspDto.class);
    }

    @BusiCode
    @Override
    public Mono<LogisticsDetailRspDto> detail(Mono<LogisticsDetailReqDto> mono) {
        return Mono.just(LogisticsDetailRspDto.builder().build());
    }

    @BusiCode
    @Override
    public Mono<Pagination> pager(Mono<LogisticsOrderReqDto> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(10);
        pagination.setRecords(List.of(new LogisticsOrderRspDto()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<LogisticsQueryByIdRspDto> queryById(Mono<LogisticsQueryByIdReqDto> mono) {
        Function<Tuple4<LogisticsOrderRoot, SendGoodsRoot, List<OrderSearchItemByIdRsp>, UserObjectRoot>, Mono<LogisticsQueryByIdRspDto>> getRsp =
                tuple -> {
                    LogisticsOrderRoot t1 = tuple.getT1();
                    SendGoodsRoot t2 = tuple.getT2();
                    List<OrderSearchItemByIdRsp> t3 = tuple.getT3();
                    Mono<List<GoodsSearchRspDto>> listMono = mono.flatMap(reqDto -> {
                        if (LogisticsOrderTypeEnum.GIFT.equals(reqDto.getLogisticsOrderType())) {
                            return Flux
                                    .just(GoodsSearchRspDto.builder()
                                            .goodsNum(1)
                                            .createTime(reqDto.getCreateTime())
                                            .goodsImages(reqDto.getGiftAddr())
                                            .goodsName(reqDto.getGiftName())
                                            .goodsSource(GoodsSourceTypeEnum.GIFT.getMsg()).build())
                                    .collectList();
                        } else {
                            return Flux.fromIterable(t3)
                                    .flatMap(orderSearchItemByIdRsp -> selectorRootService
                                            .getGoodsObject(orderSearchItemByIdRsp.getGoodsId())
                                            .flatMapMany(goodsObjectRoot -> {
                                                List<String> goodsImages = goodsObjectRoot
                                                        .getResList().stream()
                                                        .filter(goodsResValueObject -> goodsResValueObject
                                                                .getResType()
                                                                .equals(BusinessResTypeEnum.PRODUCT_PICTURE
                                                                        .name()))
                                                        .map(GoodsResValueObject::getResAddr)
                                                        .toList();
                                                return Flux.just(GoodsSearchRspDto.builder()
                                                        .goodsImages(goodsImages)
                                                        .goodsName(goodsObjectRoot.getGoodsInfo()
                                                                .getGoodsName())
                                                        .goodsNum(orderSearchItemByIdRsp
                                                                .getBuyCount())
                                                        .createTime(t1.getLogisticsOrderEntity()
                                                                .getCreateTime())
                                                        .goodsSource(
                                                                GoodsSourceTypeEnum.GOODS.getMsg())
                                                        .build());
                                            }))
                                    .collectList();
                        }
                    });

                    // 商户信息
                    Function<Long, Mono<CorpObjectRoot>> getCorpObjectRoot =
                            selectorRootService::getCorpObject;

                    return listMono.flatMap(goodsSearchDtos -> {
                        UserObjectRoot t4 = tuple.getT4();
                        LogisticsQueryByIdRspDto rspDto = LogisticsQueryByIdRspDto.builder()
                                .logisticsOrderId(
                                        t1.getLogisticsOrderEntity().getLogisticsOrderId())
                                .logisticsOrderType(t1.getLogisticsOrderEntity()
                                        .getOrderValueObject().getLogisticsOrderType())
                                .logisticsOrderStatus(
                                        t1.getLogisticsOrderEntity().getLogisticsOrderStatus())
                                .createTime(t1.getLogisticsOrderEntity().getCreateTime())
                                .orderStatusTime(t1.getLogisticsOrderEntity().getOrderStatusTime())
                                .orderNo(t1.getLogisticsOrderEntity().getOrderValueObject()
                                        .getOrderNo())
                                .logisticsCorpName(t2.getSendGoodsEntity().getLogisticsCorpName())
                                .logisticsNo(t2.getSendGoodsEntity().getLogisticsNo())
                                .addressId(
                                        t2.getSendGoodsEntity().getSendOrderAddr().getAddressId())
                                .mobile(t2.getSendGoodsEntity().getSendOrderAddr().getMobile())
                                .province(t2.getSendGoodsEntity().getSendOrderAddr().getProvince())
                                .city(t2.getSendGoodsEntity().getSendOrderAddr().getCity())
                                .district(t2.getSendGoodsEntity().getSendOrderAddr().getDistrict())
                                .addressSite(
                                        t2.getSendGoodsEntity().getSendOrderAddr().getAddressSite())
                                .addressDetail(t2.getSendGoodsEntity().getSendOrderAddr()
                                        .getAddressDetail())
                                .name(t2.getSendGoodsEntity().getSendOrderAddr().getName())
                                .goodsDetailList(goodsSearchDtos)
                                .nickname(t4.getUserDataObjectEntity().getNickname())
                                .userLogo(t4.getUserDataObjectEntity().getPicId())
                                .userMobile(t4.getUserDataObjectEntity().getMobile())
                                .userId(t4.getUserDataObjectEntity().getUserId())
                                .cancelDeadlineTime(t2.getSendGoodsEntity().getCancelDeadlineTime())
                                .corpId(t2.getSendGoodsEntity().getCorpId()).build();

                        if (t1.getLogisticsOrderEntity().getOrderValueObject()
                                .getLogisticsOrderType().equals(LogisticsOrderTypeEnum.GIFT)
                                || t1.getLogisticsOrderEntity().getOrderValueObject()
                                        .getLogisticsOrderType()
                                        .equals(LogisticsOrderTypeEnum.MERCHANT)) {
                            return getCorpObjectRoot.apply(t2.getSendGoodsEntity().getCorpId())
                                    .flatMap(corpObjectRoot -> {
                                        rspDto.setCorpName(corpObjectRoot.getCorpInfoObjectEntity()
                                                .getCorpName());
                                        rspDto.setCorpLogo(corpObjectRoot.getCorpInfoObjectEntity()
                                                .getCorpLogo());
                                        rspDto.setDeliverQrCode(corpObjectRoot
                                                .getBusinessConfigValueObject().getDeliverQrCode());
                                        return Mono.just(rspDto);
                                    });
                        }
                        return Mono.just(rspDto);
                    });
                };

        // 物流订单数据
        Mono<LogisticsOrderRoot> logisticsOrderRootMono = queryDispatcher.executeQuery(mono,
                LogisticsByIdQuery.class, LogisticsOrderRoot.class);

        // 订单数据和收货地址数据
        Mono<SendGoodsRoot> sendGoodsRootMono =
                logisticsOrderRootMono.flatMap(logisticsOrderRoot -> {
                    OrderDetailQuery query = OrderDetailQuery.builder().orderNo(logisticsOrderRoot
                            .getLogisticsOrderEntity().getOrderValueObject().getOrderNo()).build();
                    return queryDispatcher.executeQuery(Mono.just(query), OrderDetailQuery.class,
                            SendGoodsRoot.class).flatMap(sendGoodsRoot -> {
                                // 补充发货数据
                                SendGoodsByIdQuery sendGoodsByIdQuery = SendGoodsByIdQuery.builder()
                                        .logisticsOrderId(logisticsOrderRoot
                                                .getLogisticsOrderEntity().getLogisticsOrderId())
                                        .build();
                                return queryDispatcher
                                        .executeQuery(Mono.just(sendGoodsByIdQuery),
                                                SendGoodsByIdQuery.class, SendGoodsEntity.class)
                                        .switchIfEmpty(Mono.just(SendGoodsEntity.builder().build()))
                                        .flatMap(sendGoodsEntity -> {
                                            SendGoodsEntity target =
                                                    sendGoodsRoot.getSendGoodsEntity();
                                            target.setLogisticsCorpName(
                                                    sendGoodsEntity.getLogisticsCorpName());
                                            target.setLogisticsNo(sendGoodsEntity.getLogisticsNo());
                                            return Mono.just(sendGoodsRoot);
                                        });
                            });
                });

        // 商品数据
        Mono<List<OrderSearchItemByIdRsp>> itemList =
                logisticsOrderRootMono.flatMap(logisticsOrderRoot -> orderItemRootService
                        .searchEntityByOrderNo(OrderIdentifier.builder()
                                .orderNo(logisticsOrderRoot.getLogisticsOrderEntity()
                                        .getOrderValueObject().getOrderNo())
                                .build())
                        .map(entity -> OrderSearchItemByIdRsp.builder()
                                .orderItemId(entity.getOrderItemId())
                                .orderNo(logisticsOrderRoot.getLogisticsOrderEntity()
                                        .getOrderValueObject().getOrderNo())
                                .goodsId(entity.getGoodsId()).buyCount(entity.getBuyCount())
                                .build())
                        .collectList());


        // 用户信息
        Mono<UserObjectRoot> userObjectRootMono =
                sendGoodsRootMono.flatMap(sendGoodsRoot -> selectorRootService
                        .getUserObject(sendGoodsRoot.getSendGoodsEntity().getUserId()));

        return Mono.zip(logisticsOrderRootMono, sendGoodsRootMono, itemList, userObjectRootMono)
                .flatMap(getRsp);
    }
}
