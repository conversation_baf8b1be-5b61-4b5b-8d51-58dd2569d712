package com.xk.order.application.action.command.order;

import static com.xk.application.commons.CommonUtil.setIfNotNull;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.acct.interfaces.dto.rsp.user.UserAddressRspDto;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderAddressEntity;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.model.order.entity.OrderPayEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.infrastructure.convertor.common.BlockTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderCancelTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PayStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PaymentPayTypeEnumConvertor;
import com.xk.order.interfaces.dto.req.order.OrderAddressModifyReq;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.req.order.OrderRemindStatusReq;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderNoRequireReq.class, convertGenerate = false),
        @AutoMapper(target = OrderAddressModifyReq.class, convertGenerate = false),
        @AutoMapper(target = OrderRemindStatusReq.class, convertGenerate = false),
        @AutoMapper(target = OrderEntity.class,
                uses = {CommonStatusEnumConvertor.class, BlockTypeEnumConvertor.class,
                        OrderTypeEnumConvertor.class, OrderStatusEnumConvertor.class,
                        BusinessTypeEnumConvertor.class, PlatformTypeEnumConvertor.class,
                        OrderCancelTypeEnumConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = OrderAddressEntity.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrderPayEntity.class, reverseConvertGenerate = false,
                uses = {PayStatusEnumConvertor.class, PaymentPayTypeEnumConvertor.class})})
public class UpdateOrderCommand extends AbstractActionCommand {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单状态：1-待付款 2-售卖中 3-待公布 4-已完成 5-未售罄 6-已取消
     */
    private Integer orderStatus;

    /**
     * 订单状态时间
     */
    private Date orderStatusTime;

    /**
     * 收款支付标识符
     */
    private String payPaymentId;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 订单支付时间
     */
    private Date payTime;

    /**
     * 支付创建时间
     */
    private Date payCreateTime;

    /**
     * 支付状态：1-待付款 2-已支付 3-已取消 4-支付失败
     */
    private Integer payStatus;

    /**
     * 支付方式：1-银行卡 2-支付宝 3-微信支付
     */
    private Integer payType;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 用户地址ID
     */
    private Long userAddressId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 催发货状态：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;

    /**
     * 获奖状态 0-未获奖 1-已获奖
     */
    private Integer prizeStatus;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 订单取消类型 1-用户取消 2-超时取消 3-商户取消 4-运营取消
     */
    private Integer cancelType;

    /**
     * 更新人ID
     */
    @AutoMappings({@AutoMapping(targetClass = OrderEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = OrderEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;

    public void buildUpdate(Long userId) {
        this.updateId = userId;
        this.updateTime = new Date();
    }

    public void buildAddress(UserAddressRspDto rsp) {
        this.addressDetail = rsp.getAddressDetail();
        this.addressSite = rsp.getAddressSite();
        this.cityCode = rsp.getCityCode();
        this.districtCode = rsp.getDistrictCode();
        this.provinceCode = rsp.getProvinceCode();
        this.consigneeName = rsp.getConsigneeName();
        this.mobile = rsp.getMobile();
        this.userAddressId = rsp.getUserAddressId();
    }

    /**
     * 根据当前命令和已有的OrderRoot构建更新后的OrderRoot
     *
     * @param root 原始OrderRoot对象
     * @param converter 转换器
     * @return 更新后的OrderRoot对象
     */
    public OrderRoot getRoot(OrderRoot root, Converter converter) {
        // 获取原始实体
        OrderEntity originalEntity = root.getOrderEntity();
        OrderAddressEntity originalAddressEntity = root.getOrderAddressEntity();

        // 转换当前命令为实体
        OrderEntity updatedEntity = converter.convert(this, OrderEntity.class);
        OrderAddressEntity updatedAddressEntity = converter.convert(this, OrderAddressEntity.class);
        OrderPayEntity updatedPayEntity = converter.convert(this, OrderPayEntity.class);

        // 合并实体属性（保留原始实体中未在命令中指定的属性）
        OrderEntity mergedEntity = OrderEntity.builder().orderId(originalEntity.getOrderId())
                .orderNo(originalEntity.getOrderNo()).userId(originalEntity.getUserId())
                .corpId(originalEntity.getCorpId()).orderType(originalEntity.getOrderType())
                .orderOldStatus(originalEntity.getOrderStatus())
                .orderStatus(setIfNotNull(updatedEntity.getOrderStatus(),
                        originalEntity.getOrderStatus()))
                .orderStatusTime(setIfNotNull(updatedEntity.getOrderStatusTime(),
                        originalEntity.getOrderStatusTime()))
                .businessType(originalEntity.getBusinessType())
                .platformType(originalEntity.getPlatformType())
                .blockType(originalEntity.getBlockType())
                .remindShippingStatus(setIfNotNull(updatedEntity.getRemindShippingStatus(),
                        originalEntity.getRemindShippingStatus()))
                .prizeStatus(setIfNotNull(updatedEntity.getPrizeStatus(),
                        originalEntity.getPrizeStatus()))
                .cancelType(
                        setIfNotNull(updatedEntity.getCancelType(), originalEntity.getCancelType()))
                .createValObj(originalEntity.getCreateValObj())
                .updateValObj(updatedEntity.getUpdateValObj()).build();

        // 合并地址实体
        OrderAddressEntity mergedAddressEntity =
                OrderAddressEntity.builder().orderId(originalAddressEntity.getOrderId())
                        .orderNo(originalAddressEntity.getOrderNo())
                        .userAddressId(setIfNotNull(updatedAddressEntity.getUserAddressId(),
                                originalAddressEntity.getUserAddressId()))
                        .consigneeName(setIfNotNull(updatedAddressEntity.getConsigneeName(),
                                originalAddressEntity.getConsigneeName()))
                        .mobile(setIfNotNull(updatedAddressEntity.getMobile(),
                                originalAddressEntity.getMobile()))
                        .provinceCode(setIfNotNull(updatedAddressEntity.getProvinceCode(),
                                originalAddressEntity.getProvinceCode()))
                        .cityCode(setIfNotNull(updatedAddressEntity.getCityCode(),
                                originalAddressEntity.getCityCode()))
                        .districtCode(setIfNotNull(updatedAddressEntity.getDistrictCode(),
                                originalAddressEntity.getDistrictCode()))
                        .addressSite(setIfNotNull(updatedAddressEntity.getAddressSite(),
                                originalAddressEntity.getAddressSite()))
                        .addressDetail(setIfNotNull(updatedAddressEntity.getAddressDetail(),
                                originalAddressEntity.getAddressDetail()))
                        .build();

        // 合并支付实体
        OrderPayEntity originalPayEntity = root.getOrderPayEntity();
        OrderPayEntity mergedPayEntity = OrderPayEntity.builder()
                .orderId(originalPayEntity.getOrderId()).orderNo(originalPayEntity.getOrderNo())
                .payPaymentId(setIfNotNull(updatedPayEntity.getPayPaymentId(),
                        originalPayEntity.getPayPaymentId()))
                .payNo(setIfNotNull(updatedPayEntity.getPayNo(), originalPayEntity.getPayNo()))
                .payType(
                        setIfNotNull(updatedPayEntity.getPayType(), originalPayEntity.getPayType()))
                .payStatus(setIfNotNull(updatedPayEntity.getPayStatus(),
                        originalPayEntity.getPayStatus()))
                .paymentAmount(setIfNotNull(updatedPayEntity.getPaymentAmount(),
                        originalPayEntity.getPaymentAmount()))
                .payCreateTime(setIfNotNull(updatedPayEntity.getPayCreateTime(),
                        originalPayEntity.getPayCreateTime()))
                .payTime(
                        setIfNotNull(updatedPayEntity.getPayTime(), originalPayEntity.getPayTime()))
                .refundPaymentId(setIfNotNull(updatedPayEntity.getRefundPaymentId(),
                        originalPayEntity.getRefundPaymentId()))
                .refundAuditStatus(setIfNotNull(updatedPayEntity.getRefundAuditStatus(),
                        originalPayEntity.getRefundAuditStatus()))
                .refundAuditUserId(setIfNotNull(updatedPayEntity.getRefundAuditUserId(),
                        originalPayEntity.getRefundAuditUserId()))
                .refundAuditTime(setIfNotNull(updatedPayEntity.getRefundAuditTime(),
                        originalPayEntity.getRefundAuditTime()))
                .build();

        // 构建并返回更新后的OrderRoot
        return OrderRoot.builder()
                .identifier(OrderIdentifier.builder().orderNo(originalEntity.getOrderNo()).build())
                .orderEntity(mergedEntity).orderAddressEntity(mergedAddressEntity)
                .orderPayEntity(mergedPayEntity)
                // 保留原始OrderRoot中的其他实体
                .orderPriceEntity(root.getOrderPriceEntity())
                .orderRefundEntity(root.getOrderRefundEntity()).build();
    }
}
