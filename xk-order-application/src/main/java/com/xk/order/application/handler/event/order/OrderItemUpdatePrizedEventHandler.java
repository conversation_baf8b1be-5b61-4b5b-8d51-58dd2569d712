package com.xk.order.application.handler.event.order;

import java.util.function.BiFunction;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.logistics.LogisticsOrderUpdateEvent;
import com.xk.order.domain.event.order.OrderItemUpdatePrizedEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.infrastructure.repository.logistics.LogisticsOrderRootQueryRepositoryImpl;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderItemUpdatePrizedEventHandler
        extends AbstractEventVerticle<OrderItemUpdatePrizedEvent> {

    private final Converter converter;

    private final LogisticsOrderRootQueryRepositoryImpl logisticsOrderRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }


    /**
     * 商家赠品更新物流订单
     * 
     * @param event event
     * @return
     */
    @Override
    public Mono<Void> handle(Mono<OrderItemUpdatePrizedEvent> event) {
        BiFunction<LogisticsOrderEntity, OrderItemUpdatePrizedEvent, Mono<Void>> publishEvent =
                (logisticsOrderEntity, createPrizedEvent) -> {
                    EventRoot eventRoot = EventRoot.builder()
                            .domainEvent(LogisticsOrderUpdateEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            LogisticsOrderUpdateEvent.class))
                                    .logisticsOrderType(LogisticsOrderTypeEnum.GIFT)
                                    .orderNo(createPrizedEvent.getOrderNo())
                                    .logisticsOrderId(logisticsOrderEntity.getLogisticsOrderId())
                                    .giftAddr(createPrizedEvent.getGiftAddr())
                                    .giftName(createPrizedEvent.getGiftName()).build())
                            .isQueue(true).build();
                    return eventRootService.publisheByMono(eventRoot).then();
                };



        return event
                .flatMap(
                        orderItemCreatePrizedEvent -> logisticsOrderRootQueryRepository
                                .selectList(LogisticsOrderRoot.builder()
                                        .identifier(LogisticsOrderIdentifier.builder()
                                                .logisticsOrderId(-1L).build())
                                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                .orderValueObject(OrderValueObject.builder()
                                                        .orderNo(orderItemCreatePrizedEvent
                                                                .getOrderNo())
                                                        .orderItemId(orderItemCreatePrizedEvent
                                                                .getOrderItemId())
                                                        .build())
                                                .build())
                                        .build())
                                .flatMap(logisticsOrderEntity -> publishEvent
                                        .apply(logisticsOrderEntity, orderItemCreatePrizedEvent))
                                .then());

    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
