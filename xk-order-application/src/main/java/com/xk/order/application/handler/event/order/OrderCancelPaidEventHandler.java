package com.xk.order.application.handler.event.order;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.function.Function;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.action.command.payment.RefundCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderCancelPaidEvent;
import com.xk.order.domain.event.order.OrderCancelSuccessEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.entity.OrderPriceEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderRefundStatusEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.order.enums.payment.PayStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelPaidEventHandler extends AbstractEventVerticle<OrderCancelPaidEvent> {

    private final OrderRootService orderRootService;
    private final LockRootService lockRootService;
    private final OrderItemRootService orderItemRootService;
    private final StockRootService stockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    /**
     * 处理订单取消事件 流程 获取订单 -> 检查订单状态 -> 更新订单状态 -> 恢复库存
     *
     * @param mono 包含订单取消事件的Mono
     * @return 处理结果的Mono
     */
    @Override
    public Mono<Void> handle(Mono<OrderCancelPaidEvent> mono) {
        return mono.flatMap(event -> {
            // 根据订单号获取订单信息
            Mono<OrderRoot> getRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(event.getOrderNo()).build())
                    .filter(root -> !OrderTypeEnum.MERCHANT_PRODUCT
                            .equals(root.getOrderEntity().getOrderType()))
                    .switchIfEmpty(Mono.error(
                            new XkOrderApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXIST)));

            // 检查订单状态是否为已支付，只有已支付的订单才能取消
            Function<OrderRoot, Mono<OrderRoot>> checkOrderStatus =
                    root -> root.checkNoneMerchantRefundPaidStatus().thenReturn(root);

            // 更新订单状态为已取消
            Function<OrderRoot, Mono<OrderRoot>> updateOrder = root -> {
                UpdateOrderCommand updateOrderCommand =
                        UpdateOrderCommand.builder().orderNo(root.getOrderEntity().getOrderNo())
                                .orderStatus(OrderStatusEnum.CANCEL.getCode())
                                .cancelType(event.getOrderCancelType().getCode())
                                .orderStatusTime(new Date())
                                .refundStatus(OrderRefundStatusEnum.REFUNDED.getCode()).build();
                return this.commandDispatcher
                        .executeCommand(Mono.just(updateOrderCommand), UpdateOrderCommand.class)
                        .thenReturn(root);
            };

            // 根据订单类型恢复相应的库存
            Function<OrderRoot, Mono<OrderRoot>> addStock = root -> {
                OrderEntity orderEntity = root.getOrderEntity();
                if (OrderTypeEnum.MATERIAL_PRODUCT.equals(orderEntity.getOrderType())) {
                    // 物料订单处理
                    return doMaterialStock(root).thenReturn(root);
                }
                // 其他类型订单处理
                return doMallStock(root).thenReturn(root);
            };

            Function<OrderRoot, Mono<OrderRoot>> refund = root -> {
                if (!PayStatusEnum.PAID.equals(root.getOrderPayEntity().getPayStatus())) {
                    return Mono.just(root);
                }
                RefundCommand command = new RefundCommand();
                command.setOrderNo(root.getIdentifier().getOrderNo());
                command.setAmount(root.getOrderPriceEntity().getPayAmount());
                command.setPayType(root.getOrderPayEntity().getPayType());
                command.setPayTime(new Date());
                command.setPlatformType(root.getOrderEntity().getPlatformType());
                command.setRemark(event.getOrderCancelType().getMsg());
                command.setPayNo(root.getOrderPayEntity().getPayNo());
                command.setUserId(root.getOrderEntity().getUserId());
                return this.commandDispatcher
                        .executeCommand(Mono.just(command), RefundCommand.class).thenReturn(root);
            };

            Function<OrderRoot, Mono<Void>> publishEvent = root -> {
                OrderCancelSuccessEvent successEvent = OrderCancelSuccessEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(OrderCancelSuccessEvent.class))
                        .orderNo(event.getOrderNo()).orderCancelType(event.getOrderCancelType())
                        .updateId(event.getUpdateId()).build();
                return eventRootService
                        .publisheByMono(
                                EventRoot.builder().domainEvent(successEvent).isQueue(true).build())
                        .then();
            };

            // 执行整个处理流程：获取订单 -> 检查状态 -> 更新状态 -> 恢复库存 -> 退款
            return getRoot.flatMap(checkOrderStatus).flatMap(updateOrder).flatMap(addStock)
                    .flatMap(refund).flatMap(publishEvent);
        });
    }

    /**
     * 处理物料订单的库存恢复 对于物料订单，需要恢复公司的免费额度
     *
     * @param root 订单根对象
     * @return 处理结果的Mono
     */
    private @NotNull Mono<Void> doMaterialStock(OrderRoot root) {
        OrderEntity orderEntity = root.getOrderEntity();
        OrderPriceEntity orderPriceEntity = root.getOrderPriceEntity();

        // 如果免费额度折扣状态为禁用，则不需要恢复库存
        if (Objects.equals(orderPriceEntity.getFreeQuotaDiscountStatus(),
                CommonStatusEnum.DISABLE)) {
            return Mono.empty();
        }

        // 计算需要恢复的免费额度（使用负数表示增加库存）
        int stockAdjustment = -Math.toIntExact(orderPriceEntity.getFreeQuotaDiscountAmount());

        // 调用库存服务恢复公司的免费额度
        return stockRootService
                .deductionStock(
                        StringIdentifier.builder().id(orderEntity.getCorpId().toString()).build(),
                        stockAdjustment, StockBusinessTypeEnum.CORP_FREE_QUOTA)
                .filter(Boolean::booleanValue) // 过滤成功的结果
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR))) // 如果失败则抛出异常
                .then(); // 转换为Mono<Void>
    }

    /**
     * 处理其他类型订单的库存恢复 对于非物料订单，需要恢复商品规格的库存
     *
     * @param root 订单根对象
     * @return 处理结果的Mono
     */
    private @NotNull Mono<Void> doMallStock(OrderRoot root) {
        // 获取订单的所有订单项
        Flux<OrderItemEntity> orderItemFlux = orderItemRootService
                .searchEntityByOrderNo(root.getIdentifier()).cache(Duration.ofSeconds(10));

        // 定义恢复库存的函数
        Function<OrderItemEntity, Mono<Boolean>> addStock = entity -> {
            // 使用负数调用deductionStock方法来增加库存
            // 负数表示库存增加（取消订单时恢复库存）
            int stockAdjustment = -entity.getBuyCount();

            // 调用库存服务恢复商品规格的库存
            StringIdentifier build =
                    StringIdentifier.builder().id(entity.getSpecificationId().toString()).build();
            return stockRootService
                    .deductionStock(build, stockAdjustment, StockBusinessTypeEnum.SPECIFICATION)
                    .then(Mono.just(build)
                            .filter(v -> Objects.equals(
                                    root.getOrderPayEntity().getPayStatus(), PayStatusEnum.PAID))
                            .flatMap(identifier -> stockRootService.deductionPaidStock(build,
                                    stockAdjustment, StockBusinessTypeEnum.SPECIFICATION)));
        };

        // 对每个订单项执行库存恢复操作
        return orderItemFlux.flatMap(addStock).filter(Boolean::booleanValue) // 过滤成功的结果
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR))) // 如果失败则抛出异常
                .then(); // 转换为Mono<Void>
    }
}
