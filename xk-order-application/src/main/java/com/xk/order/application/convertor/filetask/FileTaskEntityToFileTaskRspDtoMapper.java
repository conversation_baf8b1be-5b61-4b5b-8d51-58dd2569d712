package com.xk.order.application.convertor.filetask;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.domain.model.filetask.FileTaskEntity;
import com.xk.order.interfaces.dto.rsp.filetask.FileTaskRspDto;

import io.github.linpeilie.BaseMapper;

/**
 * OrderRoot到SendGoodsEntity的转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class FileTaskEntityToFileTaskRspDtoMapper
        implements BaseMapper<FileTaskEntity, FileTaskRspDto> {

    @Override
    public FileTaskRspDto convert(FileTaskEntity source) {
        return convert(source, new FileTaskRspDto());
    }

    @Override
    public FileTaskRspDto convert(FileTaskEntity source, FileTaskRspDto target) {
        if (source == null) {
            return target;
        }

        return FileTaskRspDto.builder().fileName(source.getFileName())
                .fileTaskId(source.getFileTaskId()).ossPath(source.getOssPath())
                .userId(source.getCreateId()).userNick(source.getUserNick())
                .createTime(source.getCreateTime())
                .fileTaskBizType(source.getFileTaskBizType() == null ? null
                        : source.getFileTaskBizType().getCode())
                .orderType(source.getFileTaskBizType() == null ? null
                        : source.getFileTaskBizType().getOrderType())
                .fileTaskStatus(source.getFileTaskStatus() == null ? null
                        : source.getFileTaskStatus().getMsg())
                .totalCount(source.getTotalCount()).successCount(source.getSuccessCount()).build();
    }
}
