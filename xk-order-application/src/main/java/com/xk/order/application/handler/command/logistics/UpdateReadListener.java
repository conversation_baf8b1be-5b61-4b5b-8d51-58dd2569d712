package com.xk.order.application.handler.command.logistics;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson2.JSONObject;
import com.xk.order.application.dto.logistics.LogisticsImportDetail;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UpdateReadListener implements ReadListener<LogisticsImportDetail> {
    @Override
    public void invoke(LogisticsImportDetail data, AnalysisContext context) {

        log.info(JSONObject.toJSONString(data));

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info(JSONObject.toJSONString(context));
    }
}
