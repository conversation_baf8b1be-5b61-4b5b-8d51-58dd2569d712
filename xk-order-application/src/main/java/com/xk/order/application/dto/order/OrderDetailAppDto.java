package com.xk.order.application.dto.order;

import java.util.Date;
import java.util.List;

import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.order.domain.model.order.entity.*;
import com.xk.order.infrastructure.convertor.common.BlockTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderRefundStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PayStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PaymentPayTypeEnumConvertor;
import com.xk.order.interfaces.dto.rsp.order.*;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = OrderEntity.class,
                uses = {CommonStatusEnumConvertor.class, BlockTypeEnumConvertor.class,
                        OrderTypeEnumConvertor.class, OrderStatusEnumConvertor.class,
                        BusinessTypeEnumConvertor.class, PlatformTypeEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = OrderPriceEntity.class, uses = {CommonStatusEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = OrderAddressEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderPayEntity.class,
                uses = {PaymentPayTypeEnumConvertor.class, PayStatusEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = OrderRefundEntity.class,
                uses = {CommonStatusEnumConvertor.class, OrderRefundStatusEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = OrderLogisticsOrderEntity.class, convertGenerate = false,
                uses = {LogisticsOrderStatusEnumConvertor.class}),
        @AutoMapper(target = MallOrderAppDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MerchantOrderAppDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MaterialOrderAppDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MallOrderDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MerchantOrderDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = MaterialOrderDetailRsp.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrderSearchByIdRsp.class, reverseConvertGenerate = false)})
public class OrderDetailAppDto {

    /**
     * 订单状态：1-待付款 2-售卖中 3-待公布 4-已完成 5-未售罄 6-已取消
     */
    private Integer orderStatus;

    /**
     * 退款状态 1-无退款 2-退款中 3-已退款
     */
    private Integer refundStatus;

    /**
     * 用户头像
     */
    private String picId;

    /**
     * 订单完成时间
     */
    private Date successTime;

    /**
     * 物流订单ID
     */
    private Long logisticsOrderId;

    /**
     * 物流订单状态 1、待发货2、待收货3、已完成
     */
    private Integer logisticsOrderStatus;

    /**
     * 取消截止时间
     */
    private Date cancelDeadlineTime;

    /**
     * 订单状态时间
     */
    private Date orderStatusTime;

    /**
     * 用户地址ID
     */
    private Long userAddressId;

    /**
     * 收货人手机号
     */
    private String mobile;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 收货人手机号
     */
    @AutoMappings({@AutoMapping(targetClass = OrderAddressEntity.class, target = "mobile")})
    private String receivingMobile;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 购买数量
     */
    private Integer orderTotalBuyCount;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsImages;

    /**
     * 购买单价
     */
    private Long unitPrice;

    /**
     * 建议单价
     */
    private Long collectibleCardUnitPrice;

    /**
     * 收藏卡名称
     */
    private String collectibleCardName;

    /**
     * 规格单位
     */
    private String unitType;

    /**
     * 商品金额
     */
    private Long totalAmount;

    /**
     * 运费
     */
    private Long shippingFee;

    /**
     * 商品其他优惠金额
     */
    private Long otherDiscountAmount;

    /**
     * 合计/商品总额
     */
    private Long payAmount;

    /**
     * 免费额度减免金额
     */
    private Long freeQuotaDiscountAmount;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 快递公司名称
     */
    private String logisticsCorpName;

    /**
     * 快递单号
     */
    private String logisticsNo;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 创建时间
     */
    @AutoMapping(targetClass = OrderEntity.class, target = "createValObj.createTime")
    private Date createTime;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 支付方式：1-银行卡 2-支付宝 3-微信支付
     */
    private Integer payType;

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 板块类型
     */
    private Integer blockType;

    /**
     * 商户ID
     */
    private Long corpId;

    /**
     * 商户名称
     */
    private String corpName;

    /**
     * 商户logo
     */
    private String corpLogo;

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 免费额度状态 0-未使用 1-已使用
     */
    private Integer freeQuotaDiscountStatus;

    /**
     * 满减状态 0-未使用 1-已使用
     */
    private Integer discountAmountStatus;

    /**
     * 满减金额
     */
    private Long discountAmount;

    /**
     * 首购优惠状态：0-未使用 1-已使用
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠减免金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 优惠券状态 0-未使用 1-已使用
     */
    private Integer couponStatus;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券减免金额
     */
    private Long couponAmount;

    /**
     * 收款支付标识符
     */
    private String payPaymentId;

    /**
     * 支付状态：1-待付款 2-已支付 3-已取消 4-支付失败
     */
    private Integer payStatus;

    /**
     * 支付金额
     */
    private Long paymentAmount;

    /**
     * 支付创建时间
     */
    private Date payCreateTime;

    /**
     * 退款支付标识符
     */
    private String refundPaymentId;

    /**
     * 退款审核状态：0-未审核 1-审核通过 2-审核拒绝
     */
    private Integer refundAuditStatus;

    /**
     * 退款审核人ID
     */
    private Long refundAuditUserId;

    /**
     * 退款审核时间
     */
    private Date refundAuditTime;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 催发货状态：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;

    /**
     * 匿名状态：0-非匿名 1-匿名
     */
    private Integer anonymousStatus;

    /**
     * 商家代搓状态：0-未代搓 1-已代搓
     */
    private Integer corpRubbedStatus;

    /**
     * 剩余随机状态
     */
    private Integer remainRandomStatus;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private Integer deleted;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = OrderEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 更新人ID
     */
    @AutoMappings({@AutoMapping(targetClass = OrderEntity.class, target = "updateValObj.updateId")})
    private Long updateId;

    /**
     * 更新时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = OrderEntity.class, target = "updateValObj.updateTime")})
    private Date updateTime;

    /**
     * 卡密类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 随机模式 1-选队/随机球员 2-选队/随机卡种 10-非选队/随机卡种(不带编) 11-非选队/随机卡种(带编) 12-非选队/随机球队 13-非选队/随机球员
     */
    private Integer randomType;

    /**
     * 获奖状态 0-未获奖 1-已获奖
     */
    private Integer prizeStatus;

    /**
     * 商家优惠
     */
    private Long corpDiscountAmount;

    /**
     * 发货数量
     */
    private Integer shipTotalCount;

    /**
     * 物流订单数据
     */
    private List<OrderLogisticsOrderAppDto> logisticsInfoDtoList;

    /**
     * 计划上架结束时间
     */
    private Date planDownTime;
}
