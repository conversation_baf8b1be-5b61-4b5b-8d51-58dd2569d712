package com.xk.order.application.dto.order;

import java.util.Date;

import com.xk.order.domain.model.order.entity.OrderLogisticsOrderEntity;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.LogisticsOrderTypeEnumConvertor;
import com.xk.order.interfaces.dto.rsp.order.LogisticsInfoRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = OrderLogisticsOrderEntity.class, convertGenerate = false,
                uses = {LogisticsOrderStatusEnumConvertor.class,
                        LogisticsOrderTypeEnumConvertor.class}),
        @AutoMapper(target = LogisticsInfoRsp.class, reverseConvertGenerate = false)})
public class OrderLogisticsOrderAppDto {

    /**
     * 物流订单id
     */
    private Long logisticsOrderId;

    /**
     * 订单id
     */
    private String orderNo;

    /**
     * 物流订单类型 1-商城 2-物料 3-商品 4-赠品
     */
    private Integer logisticsOrderType;

    /**
     * 物流订单状态 1、待发货2、待收货3、已完成
     */
    private Integer logisticsOrderStatus;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 创建时间
     */
    private Date createTime;
}
