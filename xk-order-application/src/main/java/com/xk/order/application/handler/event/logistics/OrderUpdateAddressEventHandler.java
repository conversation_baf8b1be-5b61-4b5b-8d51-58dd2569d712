package com.xk.order.application.handler.event.logistics;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.domain.event.order.OrderUpdateAddressEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.order.interfaces.dto.req.logistics.LogisticsOrderAddressModifyReq;
import com.xk.order.interfaces.service.logistics.LogisticsOrderService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderUpdateAddressEventHandler extends AbstractEventVerticle<OrderUpdateAddressEvent> {

    private final LogisticsOrderService logisticsOrderService;
    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<OrderUpdateAddressEvent> mono) {
        // 订单地址修改地址后，获赠地址也要跟着修改
        return mono
                .flatMap(
                        orderUpdateAddressEvent -> logisticsOrderRootQueryRepository
                                .selectList(LogisticsOrderRoot.builder()
                                        .identifier(LogisticsOrderIdentifier.builder()
                                                .logisticsOrderId(-1L).build())
                                        .logisticsOrderEntity(LogisticsOrderEntity
                                                .builder()
                                                .orderValueObject(
                                                        OrderValueObject.builder()
                                                                .orderNo(orderUpdateAddressEvent
                                                                        .getOrderNo())
                                                                .build())
                                                .build())
                                        .build())
                                .flatMap(logisticsOrderEntity -> logisticsOrderService
                                        .updateAddress(Mono.just(LogisticsOrderAddressModifyReq
                                                .builder()
                                                .logisticsOrderId(
                                                        logisticsOrderEntity.getLogisticsOrderId())
                                                .userAddressId(
                                                        orderUpdateAddressEvent.getUserAddressId())
                                                .build())))
                                .then());
    }
}
