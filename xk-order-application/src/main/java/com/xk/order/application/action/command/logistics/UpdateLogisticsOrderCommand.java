package com.xk.order.application.action.command.logistics;


import java.util.Date;
import java.util.List;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class UpdateLogisticsOrderCommand extends AbstractActionCommand {

    /**
     * 物流订单IDS
     */
    private List<Long> logisticsOrderIdList;

    private LogisticsOrderStatusEnum logisticsOrderStatus;

    private Date updateTime;

    private Long userId;
}
