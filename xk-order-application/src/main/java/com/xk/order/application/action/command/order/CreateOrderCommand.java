package com.xk.order.application.action.command.order;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.xk.acct.interfaces.dto.req.user.UserAddressIdReqDto;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.BusinessTypeEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.*;
import com.xk.order.infrastructure.convertor.common.BlockTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderCancelTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.order.OrderTypeEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PayStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PaymentPayTypeEnumConvertor;
import com.xk.order.interfaces.dto.req.order.*;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;
import reactor.core.publisher.Mono;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MaterialOrderCreateReq.class, convertGenerate = false),
        @AutoMapper(target = MallOrderCreateReq.class, convertGenerate = false),
        @AutoMapper(target = MerchantOrderCreateReq.class, convertGenerate = false),
        @AutoMapper(target = MerchantFortuneCreateReq.class, convertGenerate = false),
        @AutoMapper(target = MerchantOrderRemainRandomOrderReq.class, convertGenerate = false),
        @AutoMapper(target = OrderEntity.class,
                uses = {CommonStatusEnumConvertor.class, BlockTypeEnumConvertor.class,
                        OrderTypeEnumConvertor.class, OrderStatusEnumConvertor.class,
                        BusinessTypeEnumConvertor.class, PlatformTypeEnumConvertor.class,
                        OrderCancelTypeEnumConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = OrderPriceEntity.class, uses = {CommonStatusEnumConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = OrderAddressEntity.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrderPayEntity.class,
                uses = {PaymentPayTypeEnumConvertor.class, PayStatusEnumConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = OrderRefundEntity.class, uses = {CommonStatusEnumConvertor.class},
                reverseConvertGenerate = false)})
public class CreateOrderCommand extends AbstractActionCommand {

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 板块类型
     */
    private Integer blockType;

    /**
     * 商户ID
     */
    private Long corpId;

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 订单商品总数
     */
    private Integer orderTotalBuyCount;

    /**
     * 订单发货总数
     */
    private Integer shipTotalCount;

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 商家优惠金额
     */
    private Long corpDiscountAmount;

    /**
     * 商品应付金额
     */
    private Long needAmount;

    /**
     * 商品其他优惠金额
     */
    private Long otherDiscountAmount;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 运费
     */
    private Long shippingFee;

    /**
     * 免费额度状态 0-未使用 1-已使用
     */
    private Integer freeQuotaDiscountStatus;

    /**
     * 免费额度减免金额
     */
    private Long freeQuotaDiscountAmount;

    /**
     * 满减状态 0-未使用 1-已使用
     */
    private Integer discountAmountStatus;

    /**
     * 满减金额
     */
    private Long discountAmount;

    /**
     * 首购优惠状态：0-未使用 1-已使用
     */
    private Integer firstBuyDiscountStatus;

    /**
     * 首购优惠减免金额
     */
    private Long firstBuyDiscountAmount;

    /**
     * 优惠券状态 0-未使用 1-已使用
     */
    private Integer couponStatus;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券领取id
     */
    private Long couponUserId;

    /**
     * 优惠券减免金额
     */
    private Long couponAmount;

    /**
     * 用户地址ID
     */
    private Long userAddressId;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 催发货状态：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;

    /**
     * 匿名状态：0-非匿名 1-匿名
     */
    private Integer anonymousStatus;

    /**
     * 商家代搓状态：0-未代搓 1-已代搓
     */
    private Integer corpRubbedStatus;

    /**
     * 支付方式：1-银行卡 2-支付宝 3-微信支付
     */
    private Integer payType;

    /**
     * 创建人ID
     */
    @AutoMappings({@AutoMapping(targetClass = OrderEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = OrderEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    public void buildCreate(Long createId, Date createTime) {
        this.createId = createId;
        this.createTime = createTime;
        this.businessType = BusinessTypeEnum.XING_KA.getValue();
    }

    public Mono<OrderRoot> buildRoot(Converter converter) {
        OrderEntity entity = converter.convert(this, OrderEntity.class);
        return Mono.just(OrderRoot.builder().identifier(entity.getIdentifier()).orderEntity(entity)
                .orderPriceEntity(converter.convert(this, OrderPriceEntity.class))
                .orderAddressEntity(converter.convert(this, OrderAddressEntity.class))
                .orderPayEntity(converter.convert(this, OrderPayEntity.class))
                .orderRefundEntity(converter.convert(this, OrderRefundEntity.class)).build());
    }

    public Mono<CreateOrderCommand> processAddress(Long userAddressId,
            UserQueryService userQueryService) {
        UserAddressIdReqDto reqDto = new UserAddressIdReqDto();
        reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
        reqDto.setUserAddressId(userAddressId);
        return userQueryService.getUserAddress(Mono.just(reqDto))
                .switchIfEmpty(Mono
                        .error(new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .flatMap(address -> {
                    this.setUserAddressId(userAddressId);
                    this.setConsigneeName(address.getConsigneeName());
                    this.setMobile(address.getMobile());
                    this.setProvinceCode(address.getProvinceCode());
                    this.setCityCode(address.getCityCode());
                    this.setDistrictCode(address.getDistrictCode());
                    this.setAddressSite(address.getAddressSite());
                    this.setAddressDetail(address.getAddressDetail());
                    return Mono.just(this);
                });
    }
}
