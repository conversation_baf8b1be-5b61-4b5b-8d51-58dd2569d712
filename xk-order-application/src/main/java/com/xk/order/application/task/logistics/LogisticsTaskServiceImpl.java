package com.xk.order.application.task.logistics;

import com.xk.order.interfaces.task.logistics.LogisticsTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsTaskServiceImpl implements LogisticsTaskService {


    /**
     * 扫描超时
     * @return
     */
    @Override
    public Mono<Void> logisticsOrderExport() {
        return null;
    }
}
