package com.xk.order.application.service.order;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.acct.interfaces.dto.req.user.UserAddressIdReqDto;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.domain.event.order.OrderUpdateAddressEvent;
import com.xk.order.domain.event.order.OrderUpdateRemindShippingEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.interfaces.dto.req.order.LogisticsOrderAddressModifyReq;
import com.xk.order.interfaces.dto.req.order.OrderAddressModifyReq;
import com.xk.order.interfaces.dto.req.order.OrderRemindStatusReq;
import com.xk.order.interfaces.service.order.OrderAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderAppServiceImpl implements OrderAppService {

    private final OrderRootService orderRootService;
    private final UserQueryService userQueryService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private EventRootService eventRootService;

    @Lazy
    @Autowired
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Void> updateAddress(Mono<OrderAddressModifyReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(dto.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderRoot>> checkOrderStatus =
                    root -> root.checkUpdateAddress().thenReturn(root);

            Function<OrderRoot, Mono<OrderRoot>> doExecute = root -> {
                UserAddressIdReqDto reqDto = new UserAddressIdReqDto();
                reqDto.setSessionId(dto.getSessionId());
                reqDto.setUserAddressId(dto.getUserAddressId());
                return userQueryService.getUserAddress(Mono.just(reqDto))
                        .switchIfEmpty(Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                        .flatMap(rsp -> commandDispatcher.executeCommand(mono,
                                UpdateOrderCommand.class, command -> {
                                    command.setOrderNo(dto.getOrderNo());
                                    command.buildAddress(rsp);
                                    command.buildUpdate(userId);
                                    return command;
                                }))
                        .thenReturn(root);
            };

            Function<OrderRoot, Mono<Void>> publishEvent = root -> eventRootService
                    .publisheByMono(EventRoot.builder()
                            .domainEvent(OrderUpdateAddressEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderUpdateAddressEvent.class))
                                    .orderNo(dto.getOrderNo()).userAddressId(dto.getUserAddressId())
                                    .build())
                            .isQueue(true).build())
                    .doOnSuccess(
                            v -> log.info("OrderUpdateAddressEvent事件发布完成: {}", dto.getOrderNo()))
                    .then();

            return getOrderRoot.flatMap(checkOrderStatus).flatMap(doExecute).flatMap(publishEvent);
        }));
    }

    @BusiCode
    @Override
    public Mono<Void> logisticsUpdateAddress(Mono<LogisticsOrderAddressModifyReq> mono) {
        return Mono.empty();
    }

    @BusiCode
    @Override
    public Mono<Void> updateRemindShipping(Mono<OrderRemindStatusReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(dto.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<OrderRoot>> checkOrderStatus = root -> root
                    .checkUpdateRemindShipping(dto.getLogisticsOrderId()).thenReturn(root);

            Function<OrderRoot, Mono<OrderRoot>> doExecute = root -> commandDispatcher
                    .executeCommand(mono, UpdateOrderCommand.class, command -> {
                        command.setOrderNo(dto.getOrderNo());
                        command.setRemindShippingStatus(CommonStatusEnum.ENABLE.getCode());
                        return command;
                    }).thenReturn(root);

            Function<OrderRoot, Mono<Void>> publishEvent = root -> eventRootService
                    .publisheByMono(EventRoot.builder()
                            .domainEvent(OrderUpdateRemindShippingEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderUpdateRemindShippingEvent.class))
                                    .orderNo(dto.getOrderNo())
                                    .logisticsOrderId(dto.getLogisticsOrderId()).build())
                            .isQueue(true).build())
                    .doOnSuccess(v -> log.info("OrderUpdateRemindShippingEvent事件发布完成: {}",
                            dto.getOrderNo()))
                    .then();

            return getOrderRoot.flatMap(checkOrderStatus).flatMap(doExecute).flatMap(publishEvent);
        }));
    }
}
