package com.xk.order.application.handler.event.order;

import java.util.Date;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.payment.RefundCommand;
import com.xk.order.domain.event.order.OrderNotSoldOutEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderNotSoldOutEventHandler extends AbstractEventVerticle<OrderNotSoldOutEvent> {

    private final OrderRootService orderRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<OrderNotSoldOutEvent> mono) {
        return mono.flatMap(event -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRootNoLogistics(OrderIdentifierConvertor.map(event.getOrderNo()));

            Function<OrderRoot, Mono<Void>> executeCommand = root -> {
                RefundCommand command = new RefundCommand();
                command.setOrderNo(root.getIdentifier().getOrderNo());
                command.setAmount(root.getOrderPriceEntity().getPayAmount());
                command.setPayType(root.getOrderPayEntity().getPayType());
                command.setPayTime(new Date());
                command.setPlatformType(root.getOrderEntity().getPlatformType());
                command.setRemark("订单未售罄，自动退款");
                command.setPayNo(root.getOrderPayEntity().getPayNo());
                command.setUserId(root.getOrderEntity().getUserId());
                return commandDispatcher.executeCommand(Mono.just(command), RefundCommand.class);
            };

            return getOrderRoot.flatMap(executeCommand);
        });
    }
}
