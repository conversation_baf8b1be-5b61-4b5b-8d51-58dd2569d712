package com.xk.order.application.service.order;

import static com.xk.order.enums.order.OrderTypeEnum.MALL_PRODUCT;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.interfaces.dto.req.goods.SpecificationIdBatchReqDto;
import com.xk.goods.interfaces.dto.res.goods.SpecDetailResDto;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.order.application.action.command.order.CreateOrderCommand;
import com.xk.order.application.action.command.order.CreateOrderItemCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.dto.order.MallOrderPriceAppDto;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelDurationEnum;
import com.xk.order.interfaces.dto.req.order.MallOrderCreateReq;
import com.xk.order.interfaces.dto.req.order.MallOrderPriceReq;
import com.xk.order.interfaces.dto.rsp.order.MallOrderPriceRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderCreateRsp;
import com.xk.order.interfaces.service.order.MallOrderAppService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MallOrderAppServiceImpl implements MallOrderAppService {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final GoodsSearchQueryService goodsSearchQueryService;
    private final UserQueryService userQueryService;
    private final StockRootService stockRootService;
    private final LockRootService lockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<MallOrderPriceRsp> createCalculatePrice(Mono<MallOrderPriceReq> mono) {
        return mono.flatMap(this::doGetMallPrice)
                .map(appDto -> converter.convert(appDto, MallOrderPriceRsp.class));
    }

    @BusiCode
    @Override
    public Mono<OrderCreateRsp> createMall(Mono<MallOrderCreateReq> mono) {
        Function<MallOrderCreateReq, Mono<Boolean>> getLock =
                dto -> lockRootService.acquireTransactionObjectLockMono(
                        ZookeeperLockObject.LOCKS_PAY_CALLBACK, dto.getSpecificationId());
        return mono.flatMap(dto -> ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(userObjectRoot -> getLock.apply(dto).flatMap(lock -> {
                    Long userId = userObjectRoot.getUserDataObjectEntity().getUserId();
                    PlatformTypeEnum lastPlatformType =
                            userObjectRoot.getUserDataObjectEntity().getLastPlatformType();
                    if (Boolean.FALSE.equals(lock)) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.BUY_TIME_OUT));
                    }
                    return doGetMallPrice(dto).flatMap(priceDto -> {
                        if (!Objects.equals(priceDto.getPayAmount(), dto.getPayAmount())) {
                            return Mono.error(new XkOrderApplicationException(
                                    XkOrderApplicationErrorEnum.MALL_AMOUNT_NOT_MATCH));
                        }

                        // 准备订单基础数据
                        CreateOrderCommand orderCmd =
                                converter.convert(dto, CreateOrderCommand.class);
                        Date createDate = new Date();
                        Function<CreateOrderCommand, Mono<CreateOrderCommand>> processId =
                                cmd -> Mono
                                        .zip(orderRootService.generateId(),
                                                orderRootService.generateOrderNo(MALL_PRODUCT))
                                        .map(tuple -> {
                                            cmd.setOrderId(tuple.getT1());
                                            cmd.setOrderNo(tuple.getT2());
                                            return cmd;
                                        });

                        Function<CreateOrderCommand, Mono<CreateOrderCommand>> createCommand =
                                command -> commandDispatcher.executeCommand(Mono.just(command),
                                        CreateOrderCommand.class, cmd -> {
                                            cmd.setUserId(userId);
                                            cmd.setOrderType(MALL_PRODUCT.getCode());
                                            cmd.setPayAmount(priceDto.getPayAmount());
                                            cmd.setTotalAmount(priceDto.getTotalAmount());
                                            cmd.setOrderTotalBuyCount(dto.getBuyCount());
                                            cmd.buildCreate(userId, createDate);
                                            cmd.setBusinessType(
                                                    BusinessTypeEnum.XING_KA.getValue());
                                            if (lastPlatformType != null) {
                                                cmd.setPlatformType(lastPlatformType.getValue());
                                            }
                                            return cmd;
                                        }).thenReturn(command);

                        Function<CreateOrderItemCommand, Mono<OrderCreateRsp>> deductionStock =
                                command -> stockRootService
                                        .deductionStock(
                                                StringIdentifier.builder()
                                                        .id(command.getSpecificationId().toString())
                                                        .build(),
                                                command.getBuyCount(),
                                                StockBusinessTypeEnum.SPECIFICATION)
                                        .filter(Boolean::booleanValue)
                                        .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                                XkOrderApplicationErrorEnum.STOCK_NOT_ENOUGH)))
                                        .thenReturn(OrderCreateRsp.builder()
                                                .orderNo(command.getOrderNo())
                                                .cancelDeadlineTime(DateUtils.addMinutes(createDate,
                                                        OrderCancelDurationEnum.THREE_MINUTES
                                                                .getDuration()))
                                                .build());

                        return orderCmd.processAddress(dto.getUserAddressId(), userQueryService)
                                .flatMap(processId).flatMap(createCommand)
                                .flatMap(cmd -> createOrderItems(cmd, priceDto))
                                .flatMap(deductionStock);
                    });
                })));
    }

    private <T extends MallOrderPriceReq> Mono<MallOrderPriceAppDto> doGetMallPrice(T dto) {

        // 获取物料价格信息的函数
        Supplier<Mono<List<SpecDetailResDto>>> getMallPrice = () -> {
            SpecificationIdBatchReqDto reqDto = new SpecificationIdBatchReqDto();
            reqDto.setSessionId(dto.getSessionId());
            reqDto.setSpecificationId(Collections.singletonList(dto.getSpecificationId()));
            return goodsSearchQueryService.getSpecificationDetail(Mono.just(reqDto))
                    .filter(CollectionUtils::isNotEmpty).switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
        };

        // 计算最终价格的函数
        Function<List<SpecDetailResDto>, Mono<MallOrderPriceAppDto>> calculatePrice =
                specList -> stockRootService
                        .getRemainRealStock(StockBusinessTypeEnum.SPECIFICATION,
                                StringIdentifier.builder()
                                        .id(String.valueOf(dto.getSpecificationId())).build())
                        .flatMap(stockRemainValObj -> Mono
                                .just(stockRemainValObj.getRemainRealStock()))
                        .defaultIfEmpty(0L).flatMap(stock -> {
                            if (stock < dto.getBuyCount()) {
                                return Mono.error(new XkOrderApplicationException(
                                        XkOrderApplicationErrorEnum.STOCK_NOT_ENOUGH));
                            }

                            // 检查是否存在非商城商品
                            boolean hasNonMallGoods =
                                    specList.stream().anyMatch(spec -> !GoodsTypeEnum.MALL_PRODUCT
                                            .getCode().equals(spec.getGoodsType()));

                            if (hasNonMallGoods) {
                                return Mono.error(new SystemWrapperThrowable(
                                        SystemErrorEnum.VALIDATE_FAILURE));
                            }
                            MallOrderPriceAppDto appDto = converter.convert(specList.getFirst(),
                                    MallOrderPriceAppDto.class);

                            // 使用BigDecimal进行精确计算
                            BigDecimal totalAmountBD = specList.stream()
                                    .map(spec -> BigDecimal.valueOf(spec.getAmount()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .multiply(BigDecimal.valueOf(dto.getBuyCount()));

                            appDto.setBuyCount(dto.getBuyCount());
                            appDto.setTotalAmount(totalAmountBD.longValue());
                            appDto.setPayAmount(totalAmountBD.longValue());
                            return Mono.just(appDto);
                        });

        return getMallPrice.get().flatMap(calculatePrice);
    }

    /**
     * 创建订单项
     */
    private Mono<CreateOrderItemCommand> createOrderItems(CreateOrderCommand orderCmd,
            MallOrderPriceAppDto priceDto) {
        CreateOrderItemCommand itemCmd = converter.convert(priceDto, CreateOrderItemCommand.class);
        return orderItemRootService.generateId().flatMap(itemId -> {
            itemCmd.setOrderItemId(itemId);
            itemCmd.setOrderId(orderCmd.getOrderId());
            itemCmd.setOrderNo(orderCmd.getOrderNo());
            itemCmd.setOrderType(MALL_PRODUCT.getCode());
            itemCmd.setItemPayAmount(priceDto.getPayAmount());
            itemCmd.setItemTotalAmount(priceDto.getTotalAmount());
            itemCmd.buildCreate(orderCmd.getCreateId(), orderCmd.getCreateTime());
            return commandDispatcher.executeCommand(Mono.just(itemCmd),
                    CreateOrderItemCommand.class);
        }).thenReturn(itemCmd);
    }
}
