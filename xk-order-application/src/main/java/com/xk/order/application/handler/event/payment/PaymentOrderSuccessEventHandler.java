package com.xk.order.application.handler.event.payment;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.interfaces.dto.req.goods.SpecificationIdReqDto;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionIdReq;
import com.xk.goods.interfaces.dto.req.random.RandomDistributionItemReq;
import com.xk.goods.interfaces.dto.res.random.RandomDistributionItemRes;
import com.xk.goods.interfaces.query.goods.MerchantProductQueryService;
import com.xk.goods.interfaces.service.random.RandomDistributionService;
import com.xk.order.application.action.command.order.CreateOrderGiftCommand;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderPaidEvent;
import com.xk.order.domain.event.order.PaymentOrderSuccessEvent;
import com.xk.order.domain.event.payment.OrderPaymentRefundEvent;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.entity.OrderEntity;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.valobj.OrderCacheValObj;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelTypeEnum;
import com.xk.order.enums.order.OrderGiftPrizeStatusEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.order.enums.payment.RefundStatusEnum;
import com.xk.order.enums.payment.RefundTypeEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.promotion.domain.event.coupon.CouponUserUsedEvent;

import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentOrderSuccessEventHandler
        extends AbstractEventVerticle<PaymentOrderSuccessEvent> {

    /**
     * 卡密缓存,用于异常时回滚卡密
     */
    private static final Map<String, List<RandomDistributionItemRes>> RANDOM_ITEM_CACHE =
            new ConcurrentHashMap<>();
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final LockRootService lockRootService;
    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final StockRootService stockRootService;
    private final RandomDistributionService randomDistributionService;
    private final MerchantProductQueryService merchantProductQueryService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<PaymentOrderSuccessEvent> mono) {
        return mono.flatMap(event -> acquireLockAndGetOrder(event).flatMap(orderRoot -> {
            Flux<OrderItemEntity> cachedOrderItems = orderItemRootService
                    .searchEntityByOrderNo(OrderIdentifierConvertor.map(event.getOrderNo()))
                    .cache(Duration.ofSeconds(10));
            return checkOrderCancel(orderRoot, cachedOrderItems)
                    .flatMap(root -> checkOrderStatus(root, event))
                    .flatMap(root -> processOrderUpdate(root, event))
                    .flatMap(root -> processFortune(root, cachedOrderItems)
                            .flatMap(r -> processStockDeduction(root, cachedOrderItems))
                            .flatMap(r -> getOrderGift(r, cachedOrderItems))
                            .flatMap(r -> addOrderCache(r, cachedOrderItems))
                            .flatMap(r -> publishCouponUsedEvent(r, cachedOrderItems)))
                    .flatMap(r -> publishOrderPaidEvent(r, event, cachedOrderItems))
                    .onErrorResume(e -> handleProcessingError(e, event, orderRoot))
                    .doFinally(v -> RANDOM_ITEM_CACHE.remove(event.getOrderNo()));
        }));
    }

    /**
     * 获取订单
     * 
     * @param dto dto
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> acquireLockAndGetOrder(PaymentOrderSuccessEvent dto) {
        return lockRootService.acquireTransactionObjectLockMono(
                ZookeeperLockObject.LOCKS_PAY_CALLBACK, dto.getOrderNo()).flatMap(lock -> {
                    if (Boolean.FALSE.equals(lock)) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.PAY_LOCK_TIME_OUT));
                    }
                    return orderRootService.getRoot(OrderIdentifierConvertor.map(dto.getOrderNo()));
                });
    }

    /**
     * 检查订单状态
     * 
     * @param root root
     * @param dto dto
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> checkOrderStatus(OrderRoot root, PaymentOrderSuccessEvent dto) {
        return root.checkOrderPay(dto).thenReturn(root);
    }

    /**
     * 检查订单是否取消
     *
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> checkOrderCancel(OrderRoot root,
            Flux<OrderItemEntity> cachedOrderItems) {
        if (!root.getOrderEntity().getOrderStatus().equals(OrderStatusEnum.CANCEL)) {
            return Mono.just(root);
        }

        if (!root.getOrderEntity().getCancelType().equals(OrderCancelTypeEnum.TIME_OUT_CANCEL)) {
            log.warn("订单{}不是超时取消，无法支付", root.getIdentifier().getOrderNo());
            return Mono.error(new XkOrderApplicationException(
                    XkOrderApplicationErrorEnum.ORDER_PAY_TIME_OUT));
        }

        // 延迟队列之后才可以支付
        if (new Date().after(DateUtils.addMinutes(root.getOrderEntity().getCancelDeadlineTime(),
                OrderRoot.Constant.CANCEL_DELAY_MINUTE))) {
            return Mono.error(new XkOrderApplicationException(
                    XkOrderApplicationErrorEnum.ORDER_PAY_TIME_OUT));
        }
        return orderRootService.deleteCancelOrder(root)
                .then(orderRootService.removeCancelDelayQueue(root))
                .then(processOrderCancelStockDeduction(root, cachedOrderItems));
    }

    /**
     * 处理订单取消的库存扣减
     *
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> processOrderCancelStockDeduction(OrderRoot root,
            Flux<OrderItemEntity> cachedOrderItems) {
        return cachedOrderItems
                .flatMap(item -> stockRootService.deductionStock(
                        StringIdentifier.builder().id(item.getSpecificationId().toString()).build(),
                        item.getBuyCount(), StockBusinessTypeEnum.SPECIFICATION))
                .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_CANCEL_ERROR)))
                .then().thenReturn(root);
    }

    /**
     * 更新订单
     * 
     * @param root root
     * @param dto dto
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> processOrderUpdate(OrderRoot root, PaymentOrderSuccessEvent dto) {
        return commandDispatcher.executeCommand(Mono.just(new UpdateOrderCommand()),
                UpdateOrderCommand.class, command -> {
                    command.setOrderNo(dto.getOrderNo());
                    if (OrderTypeEnum.MERCHANT_PRODUCT
                            .equals(root.getOrderEntity().getOrderType())) {
                        command.setOrderStatus(OrderStatusEnum.ON_SALE.getCode());
                    } else {
                        command.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                    }
                    command.setPayStatus(dto.getPayStatus());
                    command.setPayPaymentId(dto.getPaymentId());
                    command.setPayNo(dto.getPayNo());
                    command.setPayType(dto.getPayType());
                    command.setPayStatus(dto.getPayStatus());
                    command.setPayTime(new Date());
                    return command;
                }).thenReturn(root);
    }

    /**
     * 校验福盒锁定并移除锁定
     * 
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> processFortune(OrderRoot root, Flux<OrderItemEntity> cachedOrderItems) {
        return cachedOrderItems.flatMap(entity -> {
            if (!ProductTypeEnum.FORTUNE_BOX.getCode().equals(entity.getProductType())) {
                return Mono.empty();
            }
            OrderItemLockValObj lockValObj = OrderItemLockValObj.builder()
                    .userId(entity.getUserId()).position(entity.getItemPosition())
                    .goodsId(entity.getGoodsId()).holdLock(true).build();
            return orderItemRootService.checkSpecificationLocked(lockValObj)
                    .then(orderItemRootService.removeSpecificationLocked(lockValObj));
        }).then().thenReturn(root);
    }

    /**
     * 扣减库存
     * 
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> processStockDeduction(OrderRoot root,
            Flux<OrderItemEntity> cachedOrderItems) {
        return cachedOrderItems
                .flatMap(entity -> deductSpecificationStock(entity).then(deductGoodsStock(entity)))
                .then().thenReturn(root);
    }

    /**
     * 扣减规格库存
     * 
     * @param entity entity
     * @return Mono<Void>
     */
    private Mono<Void> deductSpecificationStock(OrderItemEntity entity) {
        return deductStock(entity.getSpecificationId().toString(), entity.getBuyCount(),
                StockBusinessTypeEnum.SPECIFICATION);
    }

    /**
     * 扣减商品库存
     * 
     * @param entity entity
     * @return Mono<Void>
     */
    private Mono<Void> deductGoodsStock(OrderItemEntity entity) {
        return deductStock(entity.getGoodsId().toString(), entity.getBuyCount(),
                StockBusinessTypeEnum.GOODS);
    }

    /**
     * 通用库存扣减方法
     * 
     * @param id id
     * @param buyCount buyCount
     * @param businessType businessType
     * @return Mono<Void>
     */
    private Mono<Void> deductStock(String id, Integer buyCount,
            StockBusinessTypeEnum businessType) {
        return stockRootService
                .deductionPaidStock(StringIdentifier.builder().id(id).build(), buyCount,
                        businessType)
                .filter(Boolean::booleanValue)
                .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                        XkOrderApplicationErrorEnum.ORDER_STOCK_ERROR)))
                .then();
    }

    /**
     * 获取订单赠品
     * 
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> getOrderGift(OrderRoot root, Flux<OrderItemEntity> cachedOrderItems) {
        OrderEntity orderEntity = root.getOrderEntity();
        if (!OrderTypeEnum.MERCHANT_PRODUCT.equals(orderEntity.getOrderType())) {
            return Mono.just(root);
        }

        return cachedOrderItems.filter(this::hasValidDistributionId)
                .flatMap(entity -> processOrderItemGift(root, entity)).then().thenReturn(root);
    }

    /**
     * 检查订单项是否有有效的分发ID
     * 
     * @param entity entity
     * @return boolean
     */
    private boolean hasValidDistributionId(OrderItemEntity entity) {
        if (entity.getDistributionId() == null) {
            log.warn("订单编号{}的订单项{}没有distributionId", entity.getOrderItemId(),
                    entity.getOrderItemId());
            return false;
        }
        return true;
    }

    /**
     * 处理订单项赠品生成
     * 
     * @param root root
     * @param entity entity
     * @return Mono<Void>
     */
    private Mono<Void> processOrderItemGift(OrderRoot root, OrderItemEntity entity) {
        if (ProductTypeEnum.FORTUNE_BOX.getCode().equals(entity.getProductType())) {
            return processFortuneBoxGift(root, entity);
        }
        return processRegularGift(root, entity);
    }

    /**
     * 处理福盒类型赠品
     * 
     * @param root root
     * @param entity entity
     * @return Mono<Void>
     */
    private Mono<Void> processFortuneBoxGift(OrderRoot root, OrderItemEntity entity) {
        SpecificationIdReqDto reqDto = SpecificationIdReqDto.builder()
                .specificationId(entity.getSpecificationId()).build();

        return merchantProductQueryService.innerSpecSerialDetail(Mono.just(reqDto))
                .switchIfEmpty(Mono.error(
                        new XkOrderApplicationException(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                .flatMapMany(Flux::fromIterable).flatMap(itemRes -> {
                    GiftInfo giftInfo = GiftInfo.builder()
                            .businessGroupId(itemRes.getSerialGroupId())
                            .categoryName(itemRes.getCategoryName())
                            .giftBusinessId(itemRes.getSerialItemId())
                            .giftBusinessName(itemRes.getSerialName())
                            .giftAddr(itemRes.getSerialAddr())
                            .giftShowAddr(itemRes.getSerialShowAddr())
                            .giftGroupAddr(itemRes.getSerialTeamAddr())
                            .color(itemRes.getColorName()).limitEdition(itemRes.getLimitEdition())
                            .serialItemType(itemRes.getSerialItemType()).build();
                    return createOrderGiftCommand(root, entity, giftInfo);
                }).then();
    }

    /**
     * 处理非福盒赠品
     * 
     * @param root root
     * @param entity entity
     * @return Mono<Void>
     */
    private Mono<Void> processRegularGift(OrderRoot root, OrderItemEntity entity) {
        RandomDistributionItemReq reqDto = RandomDistributionItemReq.builder()
                .distributionId(entity.getDistributionId()).buyCount(entity.getBuyCount()).build();

        return randomDistributionService.updateDistributionItem(Mono.just(reqDto))
                // 保存卡密防止出现异常时无法回滚
                .doOnSuccess(list -> RANDOM_ITEM_CACHE.put(root.getIdentifier().getOrderNo(), list))
                .flatMapMany(Flux::fromIterable).flatMap(itemRes -> {
                    GiftInfo giftInfo = GiftInfo.builder()
                            .businessGroupId(itemRes.getSerialGroupId())
                            .categoryName(itemRes.getCategoryName())
                            .giftBusinessId(Long.valueOf(itemRes.getItemId()))
                            .giftBusinessName(itemRes.getSerialName())
                            .giftAddr(itemRes.getSerialAddr())
                            .giftShowAddr(itemRes.getSerialShowAddr())
                            .giftGroupAddr(itemRes.getSerialTeamAddr())
                            .color(itemRes.getColorName()).limitEdition(itemRes.getLimitEdition())
                            .serialItemType(itemRes.getSerialItemType()).build();
                    return createOrderGiftCommand(root, entity, giftInfo);
                }).then();
    }

    /**
     * 创建订单赠品command
     * 
     * @param root root
     * @param entity entity
     * @param giftInfo giftInfo
     * @return Mono<Void>
     */
    private Mono<Void> createOrderGiftCommand(OrderRoot root, OrderItemEntity entity,
            GiftInfo giftInfo) {
        return orderItemRootService.generateId().flatMap(giftId -> commandDispatcher.executeCommand(
                Mono.just(new CreateOrderGiftCommand()), CreateOrderGiftCommand.class,
                command -> buildCreateOrderGiftCommand(command, giftId, root, entity, giftInfo)))
                .then();
    }

    /**
     * 构建创建订单赠品command
     * 
     * @param command command
     * @param giftId giftId
     * @param root root
     * @param entity entity
     * @param giftInfo giftInfo
     * @return CreateOrderGiftCommand
     */
    private CreateOrderGiftCommand buildCreateOrderGiftCommand(CreateOrderGiftCommand command,
            Long giftId, OrderRoot root, OrderItemEntity entity, GiftInfo giftInfo) {
        OrderEntity orderEntity = root.getOrderEntity();

        // 设置基础订单信息
        command.setOrderGiftId(giftId);
        command.setOrderId(orderEntity.getOrderId());
        command.setCorpId(orderEntity.getCorpId());
        command.setOrderNo(root.getIdentifier().getOrderNo());
        command.setOrderItemId(entity.getOrderItemId());
        command.setGoodsId(entity.getGoodsId());
        command.setSpecificationId(entity.getSpecificationId());
        command.setDistributionId(entity.getDistributionId());
        command.setGiftPrizeStatus(OrderGiftPrizeStatusEnum.WAIT_PUBLICITY.getCode());

        // 设置礼品信息
        command.setGiftBusinessType(giftInfo.getSerialItemType());
        command.setBusinessGroupId(giftInfo.getBusinessGroupId());
        command.setCategoryName(giftInfo.getCategoryName());
        command.setGiftBusinessId(giftInfo.getGiftBusinessId());
        command.setGiftBusinessName(giftInfo.getGiftBusinessName());
        command.setGiftAddr(giftInfo.getGiftAddr());
        command.setGiftShowAddr(giftInfo.getGiftShowAddr());
        command.setGiftGroupAddr(giftInfo.getGiftGroupAddr());
        command.setColor(giftInfo.getColor());
        command.setLimitEdition(giftInfo.getLimitEdition());

        command.buildCreate(entity.getUserId());
        return command;
    }

    /**
     * 添加订单缓存
     * 
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> addOrderCache(final OrderRoot root,
            Flux<OrderItemEntity> cachedOrderItems) {
        return cachedOrderItems.flatMap(entity -> {
            if (!ProductTypeEnum.FORTUNE_BOX.getCode().equals(entity.getProductType())) {
                return Mono.empty();
            }
            OrderCacheValObj fortuneCache = buildFortunePositionCache(root, entity);
            return orderItemRootService.addFortunePositionCache(fortuneCache).thenReturn(entity);
        }).thenMany(cachedOrderItems).take(1).flatMap(entity -> {
            OrderCacheValObj paidCache = buildOrderPaidCache(root, entity);
            return orderItemRootService.addOrderPaidCache(paidCache);
        }).then().thenReturn(root);
    }

    /**
     * 构建福盒位置缓存对象
     * 
     * @param root root
     * @param entity entity
     * @return OrderCacheValObj
     */
    private OrderCacheValObj buildFortunePositionCache(OrderRoot root, OrderItemEntity entity) {
        return OrderCacheValObj.builder().productType(entity.getProductType())
                .itemPosition(entity.getItemPosition()).orderNo(root.getIdentifier().getOrderNo())
                .goodsId(entity.getGoodsId()).userId(root.getOrderEntity().getUserId())
                .buyCount(root.getOrderEntity().getOrderTotalBuyCount())
                .amount(root.getOrderPayEntity().getPaymentAmount())
                .specificationId(entity.getSpecificationId()).build();
    }

    /**
     * 构建订单支付缓存对象
     * 
     * @param root root
     * @param entity entity
     * @return OrderCacheValObj
     */
    private OrderCacheValObj buildOrderPaidCache(OrderRoot root, OrderItemEntity entity) {
        return OrderCacheValObj.builder().productType(entity.getProductType())
                .itemPosition(entity.getItemPosition()).orderNo(root.getIdentifier().getOrderNo())
                .goodsId(entity.getGoodsId()).userId(root.getOrderEntity().getUserId())
                .buyCount(root.getOrderEntity().getOrderTotalBuyCount())
                .amount(root.getOrderPayEntity().getPaymentAmount())
                .specificationId(entity.getSpecificationId()).build();
    }

    /**
     * 发布优惠券使用事件
     * 
     * @param root root
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<OrderRoot>
     */
    private Mono<OrderRoot> publishCouponUsedEvent(OrderRoot root,
            Flux<OrderItemEntity> cachedOrderItems) {
        Long couponUserId = root.getOrderPriceEntity().getCouponUserId();
        if (couponUserId == null) {
            return Mono.just(root);
        }
        return cachedOrderItems.take(1)
                .flatMap(orderItem -> eventRootService.publisheByMono(EventRoot.builder()
                        .domainEvent(CouponUserUsedEvent.builder()
                                .identifier(EventRoot
                                        .getCommonsDomainEventIdentifier(CouponUserUsedEvent.class))
                                .couponUserId(couponUserId).goodsId(orderItem.getGoodsId()).build())
                        .isQueue(true).build()))
                .then().thenReturn(root);
    }

    /**
     * 发布订单支付成功事件
     * 
     * @param root root
     * @param dto dto
     * @param cachedOrderItems cachedOrderItems
     * @return Mono<Void>
     */
    private Mono<Void> publishOrderPaidEvent(OrderRoot root, PaymentOrderSuccessEvent dto,
            Flux<OrderItemEntity> cachedOrderItems) {
        return cachedOrderItems.collectList()
                .flatMap(list -> Mono.just(EventRoot.builder().domainEvent(OrderPaidEvent.builder()
                        .identifier(EventRoot.getCommonsDomainEventIdentifier(OrderPaidEvent.class))
                        .orderNo(dto.getOrderNo()).orderType(root.getOrderEntity().getOrderType())
                        .orderTotalBuyCount(root.getOrderEntity().getOrderTotalBuyCount())
                        .userId(root.getOrderEntity().getUserId())
                        .goodsIdList(list.stream().map(OrderItemEntity::getGoodsId).toList())
                        .totalAmount(root.getOrderPriceEntity().getPayAmount()).build())
                        .isQueue(true).build()))
                .flatMap(eventRootService::publisheByMono)
                .doOnSuccess(v -> log.info("OrderPaidEvent事件发布完成: {}", dto.getOrderNo())).then();
    }

    /**
     * 处理异常
     * 
     * @param e e
     * @param event event
     * @param orderRoot orderRoot
     * @return Mono<Void>
     */
    private Mono<Void> handleProcessingError(Throwable e, PaymentOrderSuccessEvent event,
            OrderRoot orderRoot) {
        log.error("订单编号{}处理支付成功事件异常", orderRoot, e);

        Supplier<Mono<Boolean>> publishEvent = () -> {
            Integer platformType = PlatformTypeEnum.IOS_USER.getValue();
            if (orderRoot.getOrderEntity().getPlatformType() != null) {
                platformType = orderRoot.getOrderEntity().getPlatformType().getValue();
            }
            return eventRootService.publisheByMono(EventRoot.builder()
                    .domainEvent(OrderPaymentRefundEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderPaymentRefundEvent.class))
                            .orderNo(orderRoot.getIdentifier().getOrderNo())
                            .payNo(orderRoot.getOrderPayEntity().getPayNo())
                            .refundType(RefundTypeEnum.FAIL.getCode())
                            .refundStatus(RefundStatusEnum.PENDING.getCode())
                            .platformType(platformType).payType(event.getPayType())
                            .payTime(new Date())
                            .amount(orderRoot.getOrderPriceEntity().getPayAmount())
                            .remark(e.getMessage()).autoRefund(true).build())
                    .isQueue(false).transaction(false).build());
        };

        Function<Boolean, Mono<Void>> returnRandomItem = success -> {
            if (!RANDOM_ITEM_CACHE.containsKey(orderRoot.getIdentifier().getOrderNo())) {
                return Mono.empty();
            }
            List<RandomDistributionItemRes> itemResList =
                    RANDOM_ITEM_CACHE.get(orderRoot.getIdentifier().getOrderNo());
            if (CollectionUtils.isEmpty(itemResList)) {
                return Mono.empty();
            }
            List<Long> list = itemResList.stream().map(v -> Long.valueOf(v.getItemId())).toList();
            return randomDistributionService
                    .updateCallbackDistributionItem(Mono.just(RandomDistributionIdReq.builder()
                            .distributionId(itemResList.getFirst().getDistributionId())
                            .itemIdList(list).build()));
        };

        return publishEvent.get().flatMap(returnRandomItem).then(Mono.error(e));
    }

    /**
     * 礼品信息DTO - 用于减少方法参数传递
     */
    @Data
    @Builder
    private static class GiftInfo {
        private Long businessGroupId;
        private String categoryName;
        private Long giftBusinessId;
        private String giftBusinessName;
        private String giftAddr;
        private String giftShowAddr;
        private String giftGroupAddr;
        private String color;
        private String limitEdition;
        private Integer serialItemType;
    }
}
