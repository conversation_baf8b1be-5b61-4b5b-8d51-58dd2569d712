package com.xk.order.application.handler.event.gift;

import java.util.function.Function;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.goods.domain.event.gift.GiftReportDeleteEvent;
import com.xk.goods.interfaces.query.gift.GiftReportQueryService;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.action.command.order.UpdateOrderGiftCommand;
import com.xk.order.domain.event.order.OrderItemDeletePrizedEvent;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.enums.order.OrderGiftPrizeStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class GiftReportDeleteEventHandler extends AbstractEventVerticle<GiftReportDeleteEvent> {

    private final OrderItemRootService orderItemRootService;
    private final GiftReportQueryService giftReportQueryService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<GiftReportDeleteEvent> mono) {
        return mono.flatMap(event -> {
            Function<OrderGiftEntity, Mono<Void>> publishEvent =
                    entity -> eventRootService.publisheByMono(EventRoot.builder()
                            .domainEvent(OrderItemDeletePrizedEvent.builder()
                                    .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                            OrderItemDeletePrizedEvent.class))
                                    .giftReportId(event.getGiftReportId())
                                    .orderNo(entity.getOrderNo().getOrderNo())
                                    .orderItemId(entity.getOrderItemId().getOrderItemId()).build())
                            .isQueue(true).build()).then();

            Function<GiftReportDeleteEvent, Mono<OrderIdentifier>> updateOrderGift =
                    rep -> orderItemRootService
                            .findGiftEntityByGoodsIdAndBusinessId(
                                    OrderGiftEntity.builder().giftBusinessId(rep.getSerialItemId())
                                            .goodsId(event.getGoodsId()).build())
                            .flatMap(giftEntity -> {
                                giftEntity.setGiftPrizeStatus(OrderGiftPrizeStatusEnum.FINISH);
                                return commandDispatcher
                                        .executeCommand(Mono.just(giftEntity),
                                                UpdateOrderGiftCommand.class)
                                        .then(publishEvent.apply(giftEntity))
                                        .thenReturn(giftEntity.getOrderNo());
                            });

            // 同笔订单可能有并发问题
            Function<OrderIdentifier, Mono<Void>> updateOrder =
                    orderNo -> commandDispatcher.executeCommand(Mono.just(new UpdateOrderCommand()),
                            UpdateOrderCommand.class, command -> {
                                command.setOrderNo(orderNo.getOrderNo());
                                command.setPrizeStatus(CommonStatusEnum.DISABLE.getCode());
                                return command;
                            });

            return updateOrderGift.apply(event).flatMap(updateOrder).then();
        });
    }
}
