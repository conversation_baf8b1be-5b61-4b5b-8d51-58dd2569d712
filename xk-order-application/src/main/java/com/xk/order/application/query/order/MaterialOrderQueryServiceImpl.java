package com.xk.order.application.query.order;

import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.xk.order.application.action.query.order.MaterialOrderDetailQuery;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.rsp.order.MaterialOrderDetailRsp;
import com.xk.order.interfaces.query.order.MaterialOrderQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialOrderQueryServiceImpl implements MaterialOrderQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;

    @BusiCode
    @Override
    public Mono<MaterialOrderDetailRsp> detail(Mono<OrderNoRequireReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, MaterialOrderDetailQuery.class,
                MaterialOrderDetailRsp.class);
    }
}
