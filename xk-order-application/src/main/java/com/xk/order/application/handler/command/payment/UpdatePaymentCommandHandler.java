package com.xk.order.application.handler.command.payment;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.payment.UpdatePaymentCommand;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.domain.repository.payment.PaymentRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdatePaymentCommandHandler
        implements IActionCommandHandler<UpdatePaymentCommand, Void> {

    private final Converter converter;
    private final PaymentRootRepository paymentRootRepository;

    @Override
    public Mono<Void> execute(Mono<UpdatePaymentCommand> command) {
        return this.execute(command, PaymentEntity.class, converter::convert,
                paymentEntity -> paymentEntity, paymentRootRepository::updateByOrderNo);
    }
}
