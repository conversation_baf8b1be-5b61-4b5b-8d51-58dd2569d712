package com.xk.order.application.query.order;

import java.util.List;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.domain.model.object.goods.GoodsObjectRoot;
import com.myco.mydata.domain.model.object.goods.GoodsResValueObject;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.business.BusinessResTypeEnum;
import com.xk.order.application.action.query.order.OrderSearchByIdDetailQuery;
import com.xk.order.application.action.query.order.OrderSearchByIdQuery;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.interfaces.dto.req.order.*;
import com.xk.order.interfaces.dto.rsp.order.*;
import com.xk.order.interfaces.query.order.OrderQueryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderQueryServiceImpl implements OrderQueryService {

    private final OrderItemRootService orderItemRootService;
    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<OrderSearchByIdRsp> searchById(Mono<OrderNoReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, OrderSearchByIdQuery.class,
                OrderSearchByIdRsp.class);
    }

    @BusiCode
    @Override
    public Mono<List<OrderSearchItemByIdRsp>> searchItemById(Mono<OrderNoReq> mono) {
        return mono.flatMap(dto -> orderItemRootService
                .searchEntityByOrderNo(OrderIdentifier.builder().orderNo(dto.getOrderNo()).build())
                .map(entity -> OrderSearchItemByIdRsp.builder().orderItemId(entity.getOrderItemId())
                        .orderNo(dto.getOrderNo()).goodsId(entity.getGoodsId())
                        .buyCount(entity.getBuyCount()).build())
                .flatMap(res -> {
                    Mono<GoodsObjectRoot> goodsObject =
                            selectorRootService.getGoodsObject(res.getGoodsId());
                    Function<GoodsObjectRoot, Mono<OrderSearchItemByIdRsp>> getGoodsImage =
                            goodsObjectRoot -> {
                                res.setGoodsImages(goodsObjectRoot.getResList().stream().filter(
                                        goodsResValueObject -> BusinessResTypeEnum.PRODUCT_PICTURE
                                                .name().equals(goodsResValueObject.getResType()))
                                        .findFirst().map(GoodsResValueObject::getResAddr)
                                        .orElse(null));
                                return Mono.just(res);
                            };
                    return goodsObject.flatMap(getGoodsImage);
                }).collectList());
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchMaterial(Mono<SearchMaterialReq> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(1);
        pagination.setRecords(List.of(new SearchMaterialRsp()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchMall(Mono<SearchMallReq> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(1);
        pagination.setRecords(List.of(new SearchMallRsp()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchMerchant(Mono<SearchMerchantReq> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(1);
        pagination.setRecords(List.of(new SearchMerchantRsp()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<Pagination> searchMerchantCorp(Mono<SearchMerchantCorpReq> mono) {
        Pagination pagination = new Pagination();
        pagination.setTotalCount(1);
        pagination.setRecords(List.of(new SearchMerchantRsp()));
        return Mono.just(pagination);
    }

    @BusiCode
    @Override
    public Mono<List<OrderOptLogRsp>> optLog(Mono<OrderOptLogReq> mono) {
        return Mono.just(List.of(new OrderOptLogRsp()));
    }

    @BusiCode
    @Override
    public Mono<SearchStatisticsRsp> searchMaterialStatistics(Mono<SearchMaterialReq> mono) {
        return Mono.just(SearchStatisticsRsp.builder().build());
    }

    @BusiCode
    @Override
    public Mono<SearchStatisticsRsp> searchMallStatistics(Mono<SearchMallReq> mono) {
        return Mono.just(SearchStatisticsRsp.builder().build());
    }

    @BusiCode
    @Override
    public Mono<SearchStatisticsRsp> searchMerchantStatistics(Mono<SearchMerchantReq> mono) {
        return Mono.just(SearchStatisticsRsp.builder().build());
    }

    @BusiCode
    @Override
    public Mono<SearchStatisticsRsp> searchMerchantCorpStatistics(
            Mono<SearchMerchantCorpReq> mono) {
        return Mono.just(SearchStatisticsRsp.builder().build());
    }

    @BusiCode
    @Override
    public Mono<OrderSearchByIdDetailRsp> searchByIdDetail(Mono<OrderNoReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, OrderSearchByIdDetailQuery.class,
                OrderSearchByIdDetailRsp.class);
    }
}
