package com.xk.order.application.handler.command.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.logistics.CreateLogisticsSendGoodsCommand;
import com.xk.order.domain.model.logisticsSendGoods.LogisticsSendGoodsOrderRoot;
import com.xk.order.domain.model.logisticsSendGoods.entity.LogisticsSendGoodsEntity;
import com.xk.order.domain.model.logisticsSendGoods.id.LogisticsSendGoodsIdentifier;
import com.xk.order.domain.repository.logisticsSendGoods.LogisticsSendGoodsRootRepository;
import com.xk.order.domain.service.logisticsSendGoods.LogisticsSendGoodsRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateLogisticsSendGoodsCommandHandler
        implements IActionCommandHandler<CreateLogisticsSendGoodsCommand, Void> {

    private final Converter converter;
    private final LogisticsSendGoodsRootRepository sendGoodsRootRepository;

    private final LogisticsSendGoodsRootService logisticsSendGoodsRootService;

    @Override
    public Mono<Void> execute(Mono<CreateLogisticsSendGoodsCommand> command) {
        return command.flatMap(createSendGoodsCommand -> Flux
                .fromIterable(createSendGoodsCommand.getLogisticsOrderIdList())
                .flatMap(logisticsOrderId -> logisticsSendGoodsRootService.generateId()
                        .flatMap(id -> {
                            LogisticsSendGoodsOrderRoot root = LogisticsSendGoodsOrderRoot.builder()
                                    .identifier(
                                            LogisticsSendGoodsIdentifier.builder().id(-1L).build())
                                    .logisticsSendGoodsEntity(LogisticsSendGoodsEntity.builder()
                                            .id(-1L).id(id)
                                            .sendGoodsId(createSendGoodsCommand.getSendGoodsId())
                                            .logisticsOrderId(logisticsOrderId)
                                            .createId(createSendGoodsCommand.getCreateId())
                                            .createTime(createSendGoodsCommand.getCreateTime())
                                            .build())
                                    .build();
                            return sendGoodsRootRepository.save(root);
                        }))
                .then());
    }
}
