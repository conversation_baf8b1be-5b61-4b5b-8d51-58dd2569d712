package com.xk.order.application.query.order;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.application.handler.query.many.ActionQueryManyDispatcher;
import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.selector.SelectorRootService;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.infrastructure.cache.dao.merchant.FortunePositionDao;
import com.xk.infrastructure.cache.key.merchant.FortunePositionKey;
import com.xk.order.application.action.query.order.*;
import com.xk.order.domain.model.order.valobj.OrderItemLockValObj;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.enums.order.FortuneLockStatusEnum;
import com.xk.order.interfaces.dto.req.order.MerchantOrderAppGiftCountReq;
import com.xk.order.interfaces.dto.req.order.MerchantOrderAppGiftSearchReq;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.dto.req.order.SpecificationBatchReqDto;
import com.xk.order.interfaces.dto.rsp.FortuneStockRspDto;
import com.xk.order.interfaces.dto.rsp.order.*;
import com.xk.order.interfaces.query.order.MerchantOrderAppQueryService;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MerchantOrderAppQueryServiceImpl implements MerchantOrderAppQueryService {

    private final ActionQueryDispatcher<IActionQuery> actionQueryDispatcher;
    private final ActionQueryManyDispatcher<IActionQueryMany> actionQueryManyDispatcher;
    private final OrderItemRootService orderItemRootService;
    private final FortunePositionDao fortunePositionDao;
    private final SelectorRootService selectorRootService;

    @BusiCode
    @Override
    public Mono<MerchantOrderAppDetailRsp> detail(Mono<OrderNoRequireReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, MerchantOrderAppDetailQuery.class,
                MerchantOrderAppDetailRsp.class);
    }

    @BusiCode
    @Override
    public Mono<Pagination> giftSearchPager(Mono<MerchantOrderAppGiftSearchReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> actionQueryDispatcher
                .executeQuery(mono, MerchantOrderAppGiftQuery.class, query -> {
                    query.setUserId(userId);
                    return query;
                }, Pagination.class));
    }

    @BusiCode
    @Override
    public Mono<MerchantOrderAppColorDetailRsp> colorDetail(Mono<OrderNoRequireReq> mono) {
        return actionQueryManyDispatcher
                .executeQuery(mono, ColorDetailQuery.class, MerchantOrderAppColorDataRsp.class)
                .collectList().flatMap(list -> {
                    MerchantOrderAppColorDetailRsp rsp =
                            MerchantOrderAppColorDetailRsp.builder().dataRspList(list).build();
                    return Mono.just(list.getFirst()).flatMap(v -> selectorRootService
                            .getGoodsObject(v.getGoodsId()).doOnSuccess(goodsObject -> {
                                if (goodsObject == null) {
                                    return;
                                }
                                rsp.setProductType(
                                        ProductTypeEnum.valueOf(goodsObject.getGoodsInfo()
                                                .getCardGoods().getCardGoodsType()).getCode());
                            })).thenReturn(rsp);
                });
    }

    @BusiCode
    @Override
    public Mono<MerchantOrderAppColorFortuneDetailRsp> colorFortuneDetail(
            Mono<OrderNoRequireReq> mono) {
        return actionQueryManyDispatcher.executeQuery(mono, ColorFortuneDetailQuery.class,
                MerchantOrderAppColorFortuneDataRsp.class).collectList().flatMap(list -> {
                    MerchantOrderAppColorFortuneDetailRsp rsp =
                            MerchantOrderAppColorFortuneDetailRsp.builder().dataRspList(list)
                                    .build();
                    return Mono.just(rsp);
                });
    }

    @BusiCode
    @Override
    public Mono<Long> giftCountPager(Mono<MerchantOrderAppGiftCountReq> mono) {
        return actionQueryDispatcher.executeQuery(mono, MerchantOrderAppGiftCountQuery.class,
                Long.class);
    }

    @BusiCode
    @Override
    public Mono<List<FortuneStockRspDto>> searchFortuneStock(Mono<SpecificationBatchReqDto> mono) {
        return mono.flatMapMany(dto -> {
            Map<String, Long> allFortunePosition = fortunePositionDao
                    .getAllValue(FortunePositionKey.builder().goodsId(dto.getGoodsId()).build());
            return Flux.range(0, dto.getSpecificationIdList().size())
                    .flatMap(item -> orderItemRootService
                            .querySpecificationLocked(OrderItemLockValObj.builder().position(item)
                                    .goodsId(dto.getGoodsId()).build())
                            .filter(Boolean::booleanValue)
                            .flatMap(v -> Mono.just(FortuneLockStatusEnum.LOCKED))
                            .switchIfEmpty(allFortunePosition.containsKey(String.valueOf(item))
                                    ? Mono.just(FortuneLockStatusEnum.SOLD)
                                    : Mono.just(FortuneLockStatusEnum.SELECTABLE))
                            .map(status -> FortuneStockRspDto.builder()
                                    .specificationId(Long.valueOf(item))
                                    .lockStatus(status.getCode()).build()));
        }).collectList();
    }
}
