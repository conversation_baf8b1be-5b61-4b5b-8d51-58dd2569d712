package com.xk.order.application.convertor.sendgoods;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.order.application.action.command.sendgoods.CreateSendGoodsCommand;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.valobj.SendOrderAddrValueObject;

import io.github.linpeilie.BaseMapper;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class SendGoodsRootToCreateSendGoodsCommandMapper
        implements BaseMapper<SendGoodsRoot, CreateSendGoodsCommand> {
    @Override
    public CreateSendGoodsCommand convert(SendGoodsRoot source) {
        return convert(source, new CreateSendGoodsCommand());

    }

    @Override
    public CreateSendGoodsCommand convert(SendGoodsRoot source, CreateSendGoodsCommand target) {
        if (source == null) {
            return null;
        }

        target.setLogisticsOrderIdList(source.getLogisticsOrderIdentifierList().stream()
                .map(LogisticsOrderIdentifier::getLogisticsOrderId).toList());



        // 转换SendGoodsEntity中的字段
        if (source.getSendGoodsEntity() != null) {
            SendGoodsEntity entity = source.getSendGoodsEntity();

            // 基础信息
            target.setSendGoodsId(entity.getSendGoodsId());

            // 物流公司信息
            target.setLogisticsCorpName(entity.getLogisticsCorpName());
            target.setLogisticsNo(entity.getLogisticsNo());

            SendOrderAddrValueObject sendOrderAddr = entity.getSendOrderAddr();

            // 收货地址信息
            target.setReceivingAddressId(sendOrderAddr.getAddressId());
            target.setReceivingMobile(sendOrderAddr.getMobile());
            target.setReceivingProvince(sendOrderAddr.getProvince());
            target.setReceivingCity(sendOrderAddr.getCity());
            target.setReceivingDistrict(sendOrderAddr.getDistrict());
            target.setReceivingAddressSite(sendOrderAddr.getAddressSite());
            target.setReceivingAddressDetail(sendOrderAddr.getAddressDetail());
            target.setReceivingName(sendOrderAddr.getName());

            // 物流状态信息
            target.setLogisticsStatus(entity.getLogisticsStatus().getCode());

            target.setCreateId(entity.getCreateId());

            // 价格和时间信息
            target.setPrice(entity.getPrice());
            target.setSendGoodsTime(entity.getSendGoodsTime());
            target.setCreateTime(entity.getSendGoodsTime());
        }

        return target;
    }
}
