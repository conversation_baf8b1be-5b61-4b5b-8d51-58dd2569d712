package com.xk.order.application.action.command.payment;

import java.util.Date;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.interfaces.dto.req.order.PayOrderReq;
import com.xk.order.interfaces.dto.req.payment.PaymentCreateReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = PaymentCreateReq.class,
                uses = {PlatformTypeEnumConvertor.class, CommonStatusEnumConvertor.class,
                        PayStatusEnum.class,},
                convertGenerate = false),
        @AutoMapper(target = PayOrderReq.class, convertGenerate = false),
        @AutoMapper(target = PaymentEntity.class, reverseConvertGenerate = false)})
public class CreatePaymentCommand extends AbstractActionCommand {

    /**
     * 收付单号
     */
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String username;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 收付状态
     */
    private PayStatusEnum payStatus;

    /**
     * 发起平台
     */
    private PlatformTypeEnum platformType;

    /**
     * 收款完成时间
     */
    private Date payTime;

    /**
     * 收款金额
     */
    private String amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private CommonStatusEnum deleted;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    public void buildCreate(Long userId) {
        this.userId = userId;
        this.createId = userId;
        this.createTime = new Date();
    }
}
