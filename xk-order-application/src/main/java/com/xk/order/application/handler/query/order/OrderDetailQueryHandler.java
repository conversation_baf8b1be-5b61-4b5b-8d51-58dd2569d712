package com.xk.order.application.handler.query.order;

import java.util.ArrayList;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.xk.order.application.action.query.order.OrderDetailQuery;
import com.xk.order.domain.model.order.OrderRoot;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.service.order.OrderRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class OrderDetailQueryHandler
        implements IActionQueryHandler<OrderDetailQuery, SendGoodsRoot> {

    private final OrderRootService orderRootService;
    private final Converter converter;

    @Override
    public Mono<SendGoodsRoot> execute(Mono<OrderDetailQuery> mono) {
        return mono.flatMap(query -> {
            Mono<OrderRoot> getOrderRoot = orderRootService
                    .getRoot(OrderIdentifier.builder().orderNo(query.getOrderNo()).build())
                    .switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));

            Function<OrderRoot, Mono<SendGoodsRoot>> getDetail = root -> {
                SendGoodsEntity sendGoodsEntity = converter.convert(root, SendGoodsEntity.class);
                return Mono.just(SendGoodsRoot.builder()
                        .identifier(SendGoodsIdentifier.builder().sendGoodsId(-1L).build())
                        .sendGoodsEntity(sendGoodsEntity)
                        .logisticsOrderIdentifierList(new ArrayList<>())
                        .logisticsValueObjectList(new ArrayList<>()).build());
            };

            return getOrderRoot.flatMap(getDetail);
        });
    }
}
