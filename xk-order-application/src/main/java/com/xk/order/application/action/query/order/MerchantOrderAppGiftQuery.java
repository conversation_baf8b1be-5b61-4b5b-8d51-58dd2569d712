package com.xk.order.application.action.query.order;

import com.myco.mydata.application.action.query.PagerQuery;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.dto.req.order.MerchantOrderAppGiftSearchReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = MerchantOrderAppGiftSearchReq.class, convertGenerate = false)})
public class MerchantOrderAppGiftQuery extends PagerQuery implements IActionQuery {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品编号
     */
    private Long goodsId;

    /**
     * 赠品名称
     */
    private String giftBusinessName;

    /**
     * 获奖状态 1-待公布 2-已完成 3-获赠卡
     */
    private Integer giftPrizeStatus;

    /**
     * 只显示限编 1-限编 0-未限编
     */
    private Integer onlyLimitEdition;
}
