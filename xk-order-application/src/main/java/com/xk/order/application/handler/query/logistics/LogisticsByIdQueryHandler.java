package com.xk.order.application.handler.query.logistics;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.order.application.action.query.logistics.LogisticsByIdQuery;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class LogisticsByIdQueryHandler
        implements IActionQueryHandler<LogisticsByIdQuery, LogisticsOrderRoot> {

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    @Override
    public Mono<LogisticsOrderRoot> execute(Mono<LogisticsByIdQuery> query) {
        return query.flatMap(
                id -> logisticsOrderRootQueryRepository
                        .selectById(
                                LogisticsOrderRoot.builder()
                                        .identifier(LogisticsOrderIdentifier.builder()
                                                .logisticsOrderId(id.getLogisticsOrderId()).build())
                                        .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                                .logisticsOrderId(id.getLogisticsOrderId()).build())
                                        .build())
                        .map(logisticsOrderEntity -> LogisticsOrderRoot.builder()
                                .identifier(LogisticsOrderIdentifier.builder()
                                        .logisticsOrderId(
                                                logisticsOrderEntity.getLogisticsOrderId())
                                        .build())
                                .logisticsOrderEntity(logisticsOrderEntity).build()));
    }

}
