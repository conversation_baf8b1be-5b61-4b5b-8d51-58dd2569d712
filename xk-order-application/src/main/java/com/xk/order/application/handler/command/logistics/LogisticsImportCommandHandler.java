package com.xk.order.application.handler.command.logistics;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.domain.model.filetask.FileTaskEntity;
import com.xk.domain.model.filetask.FileTaskIdentifier;
import com.xk.domain.model.filetask.FileTaskRoot;
import com.xk.domain.repository.filetask.FileTaskRootRepository;
import com.xk.domain.service.filetask.FileTaskRootDomainService;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.enums.filetask.FileTaskBizTypeEnum;
import com.xk.enums.filetask.FileTaskStatusEnum;
import com.xk.enums.filetask.FileTaskTypeEnum;
import com.xk.order.application.action.command.logistics.LogisticsImportCommand;
import com.xk.order.application.dto.logistics.LogisticsImportDetail;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.domain.repository.logisticsSendGoods.LogisticsSendGoodsRootRepository;
import com.xk.order.domain.service.logisticsSendGoods.LogisticsSendGoodsRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsImportCommandHandler
        implements IActionCommandHandler<LogisticsImportCommand, Void> {

    private final Converter converter;
    private final LogisticsSendGoodsRootRepository sendGoodsRootRepository;

    private final FileTaskRootDomainService fileTaskRootDomainService;

    private final LogisticsSendGoodsRootService logisticsSendGoodsRootService;

    private final FileTaskRootRepository fileTaskRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> execute(Mono<LogisticsImportCommand> command) {
        return command.doOnNext(cmd -> log.info("Processing logistics import for type: {}",
                cmd.getOrderType())).flatMap(importCommand -> {
                    // 读取CSV文件并转换为LogisticsImportDetail列表
                    return readCsvToLogisticsImportDetailList(importCommand.getExcelBase64())
                            .flatMap(logisticsImportDetails -> {
                                log.info("Successfully parsed {} logistics import records",
                                        logisticsImportDetails.size());

                                // 这里可以添加后续的业务处理逻辑
                                // 例如：验证数据、保存到数据库等
                                return processLogisticsImportDetails(logisticsImportDetails,
                                        importCommand);
                            });
                }).doOnSuccess(result -> log.info("Logistics import processed successfully"))
                .doOnError(error -> log.error("Failed to process logistics import", error));
    }

    /**
     * 检测字节数组的编码
     *
     * @param bytes 文件字节数组
     * @return 检测到的编码，默认返回UTF-8
     */
    public static String detectEncoding(byte[] bytes) throws IOException {
        byte[] buf = new byte[4096];
        ByteArrayInputStream fis = new ByteArrayInputStream(bytes);

        // 创建检测器实例
        UniversalDetector detector = new UniversalDetector(null);

        // 读取文件并喂给检测器
        int nread;
        while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
            detector.handleData(buf, 0, nread);
        }

        // 通知检测器数据已结束
        detector.dataEnd();

        // 获取检测结果
        String encoding = detector.getDetectedCharset();

        // 重置检测器以备下次使用
        detector.reset();
        fis.close();

        return encoding != null ? encoding : "UTF-8"; // 默认返回UTF-8
    }

    /**
     * 读取CSV Base64文件并转换为LogisticsImportDetail列表
     */
    public Mono<List<LogisticsImportDetail>> readCsvToLogisticsImportDetailList(String csvBase64) {
        return Mono.fromCallable(() -> {
            try {
                // 1. 解码Base64字符串
                byte[] csvBytes = Base64.getDecoder().decode(csvBase64);

                // 2. 自动解析编码
                // String encoding = detectEncoding(csvBytes);

                // 3. 使用EasyExcel读取CSV
                try (InputStream inputStream = new ByteArrayInputStream(csvBytes)) {
                    List<LogisticsImportDetail> logisticsImportDetails =
                            EasyExcel.read(inputStream).head(LogisticsImportDetail.class)
                                    .registerReadListener(new UpdateReadListener())
                                    .charset(Charset.forName(detectEncoding(csvBytes))) // 设置检测到的编码
                                    .excelType(ExcelTypeEnum.CSV) // 指定为CSV格式
                                    .sheet().doReadSync();

                    return validateAndCleanData(logisticsImportDetails);
                }

                // // 2. 创建输入流并读取CSV内容
                // ByteArrayInputStream inputStream = new ByteArrayInputStream(csvBytes);
                // BufferedReader reader = new BufferedReader(
                // new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                //
                // // 3. 解析CSV文件
                // List<LogisticsImportDetail> logisticsImportDetails =
                // parseCsvToLogisticsImportDetails(reader);
                //
                // // 4. 数据验证和清理
                // return validateAndCleanData(logisticsImportDetails);

            } catch (Exception e) {
                log.error("Failed to read CSV file from Base64", e);
                throw new RuntimeException("CSV文件读取失败: " + e.getMessage(), e);
            }
        }).onErrorMap(throwable -> {
            if (throwable instanceof RuntimeException) {
                return throwable;
            }
            return new RuntimeException("处理CSV文件时发生未知错误", throwable);
        });
    }

    /**
     * 解析CSV内容为LogisticsImportDetail列表
     */
    public List<LogisticsImportDetail> parseCsvToLogisticsImportDetails(BufferedReader reader)
            throws Exception {
        List<LogisticsImportDetail> details = new ArrayList<>();
        String line;
        boolean isFirstLine = true;
        int lineNumber = 0;

        while ((line = reader.readLine()) != null) {
            lineNumber++;

            // 跳过空行
            if (line.trim().isEmpty()) {
                continue;
            }

            // 跳过标题行（第一行）
            if (isFirstLine) {
                isFirstLine = false;
                log.debug("CSV header line: {}", line);
                continue;
            }

            try {
                LogisticsImportDetail detail = parseCsvLine(line, lineNumber);
                if (detail != null) {
                    details.add(detail);
                }
            } catch (Exception e) {
                log.warn("Failed to parse CSV line {}: {}, error: {}", lineNumber, line,
                        e.getMessage());
                // 继续处理下一行，不中断整个导入过程
            }
        }

        log.info("Successfully parsed {} records from CSV file", details.size());
        return details;
    }

    /**
     * 解析单行CSV数据 CSV格式：订单号,商品名称,商品数量,物流公司,物流单号,地址站点,详细地址
     */
    public LogisticsImportDetail parseCsvLine(String line, int lineNumber) {
        // 简单的CSV解析（处理逗号分隔）
        String[] fields = line.split(",", -1); // -1 保留空字段

        // 验证字段数量
        if (fields.length < 5) {
            log.warn("CSV line {} has insufficient fields (expected at least 5, got {}): {}",
                    lineNumber, fields.length, line);
            return null;
        }

        LogisticsImportDetail detail = new LogisticsImportDetail();

        // 按顺序映射字段
        detail.setLogisticsOrderId(getFieldValueLong(fields, 0)); // 订单号
        detail.setOrderNo(getFieldValue(fields, 1)); // 订单号
        detail.setGoodsName(getFieldValue(fields, 2)); // 商品名称
        detail.setGoodsCount(getFieldValue(fields, 3)); // 商品数量
        detail.setLogisticsCorpName(getFieldValue(fields, 4)); // 物流公司
        detail.setLogisticsNo(getFieldValue(fields, 5)); // 物流单号
        detail.setConsigneeName(getFieldValue(fields, 6)); // 物流单号
        detail.setReceivingMobile(getFieldValue(fields, 7)); // 物流单号
        detail.setAddressSite(getFieldValue(fields, 8)); // 地址站点
        detail.setAddressDetail(getFieldValue(fields, 9)); // 详细地址

        return detail;
    }

    /**
     * 安全获取字段值
     */
    public String getFieldValue(String[] fields, int index) {
        if (index >= fields.length) {
            return null;
        }
        String value = fields[index].trim();
        return value.isEmpty() ? null : value;
    }

    /**
     * 安全获取字段值
     */
    public Long getFieldValueLong(String[] fields, int index) {
        if (index >= fields.length) {
            return null;
        }
        String value = fields[index].trim();
        return value.isEmpty() ? null : Long.parseLong(value);
    }

    /**
     * 验证和清理数据
     */
    public List<LogisticsImportDetail> validateAndCleanData(List<LogisticsImportDetail> rawData) {
        return rawData.stream().filter(detail -> {
            // 基本数据验证
            if (detail == null) {
                log.warn("Found null LogisticsImportDetail, skipping");
                return false;
            }

            // 验证必填字段
            if (isBlank(detail.getOrderNo())) {
                log.warn("Found LogisticsImportDetail with empty orderNo, skipping: {}", detail);
                return false;
            }

            if (isBlank(detail.getLogisticsCorpName())) {
                log.warn("Found LogisticsImportDetail with empty logisticsCorpName, skipping: {}",
                        detail);
                return false;
            }

            if (isBlank(detail.getLogisticsNo())) {
                log.warn("Found LogisticsImportDetail with empty logisticsNo, skipping: {}",
                        detail);
                return false;
            }

            return true;
        }).map(this::cleanData).toList();
    }

    /**
     * 清理单条数据
     */
    public LogisticsImportDetail cleanData(LogisticsImportDetail detail) {
        // 清理字符串字段的空白字符
        if (detail.getOrderNo() != null) {
            detail.setOrderNo(detail.getOrderNo().trim());
        }
        if (detail.getGoodsName() != null) {
            detail.setGoodsName(detail.getGoodsName().trim());
        }
        if (detail.getGoodsCount() != null) {
            detail.setGoodsCount(detail.getGoodsCount().trim());
        }
        if (detail.getLogisticsCorpName() != null) {
            detail.setLogisticsCorpName(detail.getLogisticsCorpName().trim());
        }
        if (detail.getLogisticsNo() != null) {
            detail.setLogisticsNo(detail.getLogisticsNo().trim());
        }
        if (detail.getAddressSite() != null) {
            detail.setAddressSite(detail.getAddressSite().trim());
        }
        if (detail.getAddressDetail() != null) {
            detail.setAddressDetail(detail.getAddressDetail().trim());
        }

        return detail;
    }

    /**
     * 处理物流导入详情列表
     */
    public Mono<Void> processLogisticsImportDetails(List<LogisticsImportDetail> details,
            LogisticsImportCommand command) {
        // 这里可以添加具体的业务处理逻辑
        // 例如：
        // 1. 批量验证物流订单是否存在
        // 2. 更新物流信息
        // 3. 发送通知等

        return ReadSynchronizationUtils.getUserObjectMono(true).flatMap(userObjectRoot -> {
            UserDataObjectEntity userDataObjectEntity = userObjectRoot.getUserDataObjectEntity();
            return fileTaskRootDomainService.generateId().flatMap(taskId -> {
                Date date = new Date();
                FileTaskRoot fileTaskRoot = FileTaskRoot.builder()
                        .identifier(FileTaskIdentifier.builder().fileTaskId(taskId).build())
                        .fileTaskEntity(FileTaskEntity.builder()
                                .fileTaskBizType(
                                        FileTaskBizTypeEnum.getByCode(command.getFileTaskBizType()))
                                .fileTaskType(FileTaskTypeEnum.IMPORT)
                                .fileTaskBizStatus(FileTaskBizStatusEnum.CREATE_TASK)
                                .createId(userDataObjectEntity.getUserId())
                                .userNick(userDataObjectEntity.getNickname()).fileTaskId(taskId)
                                .fileName(command.getFileName()).fileTaskName(command.getFileName())
                                .totalCount(details.size())
                                .createId(userDataObjectEntity.getUserId())
                                // 做一个定时器，扫描数据库中的总数和实际数量，如果大于0，小于总数 则更新为处理中，如果等于总数
                                // 则更新完诶处理完成，如果时间过长，则强制更新为完成
                                .createTime(date).fileTaskStatus(FileTaskStatusEnum.TO_BE_PROCESSED)
                                .build())
                        .build();

                return fileTaskRootRepository.save(fileTaskRoot).thenReturn(fileTaskRoot);
            }).flatMap(fileTaskRoot -> Flux.fromIterable(details).flatMap(detail -> {
                SendGoodsCreateEvent sendGoodsCreateEvent = SendGoodsCreateEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(SendGoodsCreateEvent.class))
                        .logisticsOrderId(detail.getLogisticsOrderId()).orderNo(detail.getOrderNo())
                        .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED)
                        .logisticsNo(detail.getLogisticsNo())
                        .logisticsCorpName(detail.getLogisticsCorpName())
                        .addressSite(detail.getAddressSite())
                        .addressDetail(detail.getAddressDetail())
                        .fileTaskId(fileTaskRoot.getFileTaskEntity().getFileTaskId())
                        .goodsName(detail.getGoodsName()).goodsCount(detail.getGoodsCount())
                        .sendGoodsTime(fileTaskRoot.getFileTaskEntity().getCreateTime())
                        .hasImport(true).build();
                EventRoot eventRoot =
                        EventRoot.builder().domainEvent(sendGoodsCreateEvent).isQueue(true).build();
                return eventRootService.publisheByMono(eventRoot);
            }).then());
        });
    }


    /**
     * 检查字符串是否为空或空白
     */
    public boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
}

