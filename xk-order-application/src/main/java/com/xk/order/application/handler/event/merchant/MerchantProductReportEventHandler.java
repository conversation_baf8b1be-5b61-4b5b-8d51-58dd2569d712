package com.xk.order.application.handler.event.merchant;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.goods.domain.event.merchant.MerchantProductReportEvent;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.application.action.command.order.UpdateOrderGiftCommand;
import com.xk.order.domain.model.order.entity.OrderGiftEntity;
import com.xk.order.domain.repository.order.OrderItemRootQueryRepository;
import com.xk.order.enums.order.OrderGiftPrizeStatusEnum;
import com.xk.order.enums.order.OrderStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class MerchantProductReportEventHandler
        extends AbstractEventVerticle<MerchantProductReportEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final OrderItemRootQueryRepository orderItemRootQueryRepository;

    @Override
    public boolean isBlockExecute() {
        return true;
    }

    @Override
    public Mono<Void> handle(Mono<MerchantProductReportEvent> mono) {
        return mono.flatMap(event -> {
            HashSet<Long> idSet = new HashSet<>(event.getSerialItemIdList());
            Set<String> orderNoSet = new HashSet<>();
            Date updateTime = new Date();

            return orderItemRootQueryRepository
                    .findGiftEntityByGoodsId(
                            OrderGiftEntity.builder().goodsId(event.getGoodsId()).build())
                    .collectList().flatMapMany(giftList -> {
                        giftList.forEach(v -> {
                            if (idSet.contains(v.getGiftBusinessId())) {
                                orderNoSet.add(v.getOrderNo().getOrderNo());
                            }
                        });
                        return Flux.fromIterable(giftList);
                    }).flatMap(v -> {
                        if (orderNoSet.add(v.getOrderNo().getOrderNo())) {
                            return commandDispatcher.executeCommand(
                                    Mono.just(new UpdateOrderCommand()), UpdateOrderCommand.class,
                                    command -> {
                                        command.setOrderNo(v.getOrderNo().getOrderNo());
                                        command.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
                                        command.setOrderStatusTime(updateTime);
                                        command.setUpdateId(-1L);
                                        command.setUpdateTime(updateTime);
                                        return command;
                                    });
                        }
                        if (idSet.contains(v.getGiftBusinessId())) {
                            return Mono.empty();
                        }
                        return commandDispatcher.executeCommand(Mono.just(v),
                                UpdateOrderGiftCommand.class, command -> {
                                    command.setGiftPrizeStatus(
                                            OrderGiftPrizeStatusEnum.FINISH.getCode());
                                    return command;
                                });
                    }).then();
        });
    }
}
