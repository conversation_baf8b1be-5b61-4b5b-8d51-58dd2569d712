package com.xk.order.application.dto.logistics;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsImportDetail {

    @ExcelProperty("物流订单ID")
    private Long logisticsOrderId;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("发货数")
    private String goodsCount;

    @ExcelProperty("物流公司名")
    private String logisticsCorpName;

    @ExcelProperty("物流单号")
    private String LogisticsNo;

    @ExcelProperty("收货人姓名")
    private String consigneeName;

    @ExcelProperty("收货人手机号")
    private String receivingMobile;

    @ExcelProperty("发货省市区")
    private String addressSite;

    @ExcelProperty("发货详细地址")
    private String addressDetail;

}
