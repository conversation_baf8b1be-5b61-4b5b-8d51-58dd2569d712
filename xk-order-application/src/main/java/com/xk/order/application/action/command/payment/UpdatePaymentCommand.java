package com.xk.order.application.action.command.payment;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import com.xk.order.domain.model.payment.entity.PaymentEntity;
import com.xk.order.enums.payment.PayStatusEnum;
import com.xk.order.enums.payment.PaymentPayTypeEnum;
import com.xk.order.infrastructure.convertor.payment.PayStatusEnumConvertor;
import com.xk.order.infrastructure.convertor.payment.PaymentPayTypeEnumConvertor;
import com.xk.order.interfaces.dto.req.order.PayOrderReq;
import com.xk.order.interfaces.dto.req.payment.PaymentCreateReq;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = PayOrderReq.class,
                uses = {PayStatusEnumConvertor.class, PaymentPayTypeEnumConvertor.class,
                        PlatformTypeEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = PaymentCreateReq.class,
                uses = {PayStatusEnumConvertor.class, PaymentPayTypeEnumConvertor.class,
                        PlatformTypeEnumConvertor.class},
                convertGenerate = false),
        @AutoMapper(target = PaymentEntity.class, reverseConvertGenerate = false)})
public class UpdatePaymentCommand extends AbstractActionCommand {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 流水号
     */
    private String payNo;

    /**
     * 发起平台
     */
    private PlatformTypeEnum platformType;

    /**
     * 支付状态
     */
    private PayStatusEnum payStatus;

    /**
     * 支付类型
     */
    private PaymentPayTypeEnum payType;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
