package com.xk.order.application.service.logistics;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.application.handler.query.ActionQueryDispatcher;
import com.myco.mydata.application.handler.query.IActionQuery;
import com.myco.mydata.config.domain.model.item.ItemValueIdentifier;
import com.myco.mydata.config.domain.service.item.DictItemDomainService;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.object.user.UserDataObjectEntity;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.model.session.SessionRoot;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.acct.interfaces.dto.req.user.UserAddressIdReqDto;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.domain.model.filetask.FileTaskEntity;
import com.xk.domain.model.filetask.FileTaskIdentifier;
import com.xk.domain.model.filetask.FileTaskRoot;
import com.xk.domain.repository.filetask.FileTaskRootRepository;
import com.xk.domain.service.filetask.FileTaskRootDomainService;
import com.xk.enums.filetask.FileTaskBizStatusEnum;
import com.xk.enums.filetask.FileTaskBizTypeEnum;
import com.xk.enums.filetask.FileTaskStatusEnum;
import com.xk.enums.filetask.FileTaskTypeEnum;
import com.xk.order.application.action.command.logistics.*;
import com.xk.order.application.action.command.order.UpdateOrderLogisticsOrderCommand;
import com.xk.order.application.action.command.sendgoods.CreateSendGoodsCommand;
import com.xk.order.application.action.query.logistics.LogisticsByIdQuery;
import com.xk.order.application.action.query.order.OrderDetailQuery;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.logistics.LogisticsOrderExportEvent;
import com.xk.order.domain.event.logistics.LogisticsOrderGenerateEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsConfirmEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.order.domain.service.sendgoods.SendGoodsRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.order.interfaces.dto.req.logistics.*;
import com.xk.order.interfaces.service.logistics.LogisticsOrderService;
import com.xk.search.interfaces.dto.req.logistics.SearchLogisticsConfirmReqDto;
import com.xk.search.interfaces.service.logistics.LogisticsSearchService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsOrderServiceImpl implements LogisticsOrderService {

    private final ActionQueryDispatcher<IActionQuery> queryDispatcher;

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;

    private final SendGoodsRootService sendGoodsRootService;

    private final FileTaskRootDomainService fileTaskRootDomainService;

    private final FileTaskRootRepository fileTaskRootRepository;

    private final Converter converter;

    private final LockRootService lockRootService;

    private final DictItemDomainService dictItemDomainService;

    private final LogisticsSearchService logisticsSearchService;

    private final LogisticsOrderRootQueryRepository logisticsOrderRootQueryRepository;

    private final UserQueryService userQueryService;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Void> createExport(Mono<LogisticsExportReqDto> mono) {
        return mono.flatMap(
                req -> ReadSynchronizationUtils.getUserObjectMono(true).flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();
                    UserTypeEnum userType = userDataObjectEntity.getUserType();
                    try {
                        lockRootService.acquireTransactionObjectLock(
                                ZookeeperLockObject.LOCKS_LOGISTICS_ORDER_EXPORT,
                                req.getOrderType());
                    } catch (Throwable e) {
                        return Mono.error(e);
                    }

                    return fileTaskRootDomainService.generateId().flatMap(taskId -> {
                        Long corpId = null;
                        if (userType.equals(UserTypeEnum.MERCHANT_KAS)) {
                            corpId = userDataObjectEntity.getCorpId();
                        }

                        DateTimeFormatter formatter =
                                DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
                        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(req.getOrderType());
                        LogisticsOrderExportEvent createEvent = LogisticsOrderExportEvent.builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        LogisticsOrderExportEvent.class))
                                .orderTypeEnum(orderTypeEnum).taskId(taskId)
                                .fileName(String.format("%s_发货记录_%s", orderTypeEnum.getMsg(),
                                        LocalDateTime.now().format(formatter)))
                                .startTime(req.getStartTime()).endTime(req.getEndTime())
                                .filePath("/send/goods/export").corpId(corpId).build();

                        Date date = new Date();

                        EventRoot eventRootMono =
                                EventRoot.builder().domainEvent(createEvent).isQueue(true).build();

                        /**
                         * 创建任务
                         */
                        return fileTaskRootRepository.save(FileTaskRoot.builder()
                                .identifier(FileTaskIdentifier.builder().fileTaskId(taskId).build())
                                .fileTaskEntity(FileTaskEntity.builder()
                                        .ossPath(createEvent.getFilePath()).commitTime(date)
                                        .fileTaskBizType(FileTaskBizTypeEnum
                                                .getByCode(req.getFileTaskBizType()))
                                        .fileTaskType(FileTaskTypeEnum.EXPORT).createTime(date)
                                        .fileTaskBizStatus(FileTaskBizStatusEnum.CREATE_TASK)
                                        .createId(userDataObjectEntity.getUserId())
                                        .userNick(userDataObjectEntity.getNickname())
                                        .fileTaskId(taskId)
                                        .fileName(
                                                String.format("%s0.csv", createEvent.getFileName()))
                                        .fileTaskName(createEvent.getFileName())
                                        .fileTaskDescription(String.format("%s至%s",
                                                DateUtils.formatDate(req.getStartTime(),
                                                        "yyyy-MM-dd"),
                                                DateUtils.formatDate(req.getEndTime(),
                                                        "yyyy-MM-dd")))
                                        .fileTaskStatus(FileTaskStatusEnum.TO_BE_PROCESSED).build())
                                .build()).thenReturn(eventRootMono);

                    });
                    // 发送任务导出事件
                }).flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot))).then();
    }

    @BusiCode
    @Override
    public Mono<Void> createMakefile(Mono<LogisticsGenerateReqDto> mono) {
        return mono.flatMap(
                req -> ReadSynchronizationUtils.getUserObjectMono(true).flatMap(userObjectRoot -> {
                    UserDataObjectEntity userDataObjectEntity =
                            userObjectRoot.getUserDataObjectEntity();

                    return fileTaskRootDomainService.generateId().flatMap(taskId -> {

                        DateTimeFormatter formatter =
                                DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
                        LogisticsOrderTypeEnum logisticsOrderType =
                                LogisticsOrderTypeEnum.getByCode(req.getLogisticsOrderType());
                        LogisticsOrderGenerateEvent createEvent = LogisticsOrderGenerateEvent
                                .builder()
                                .identifier(EventRoot.getCommonsDomainEventIdentifier(
                                        LogisticsOrderGenerateEvent.class))
                                .fileName(String.format("%s_生成文件_%s", logisticsOrderType.getMsg(),
                                        LocalDateTime.now().format(formatter)))
                                .filePath("/send/goods/generate").taskId(taskId)
                                .exportTaskId(req.getFileTaskId())
                                .build();
                        EventRoot eventRootMono =
                                EventRoot.builder().domainEvent(createEvent).isQueue(true).build();

                        Date date = new Date();

                        /**
                         * 创建任务
                         */
                        return fileTaskRootRepository.save(FileTaskRoot.builder()
                                .identifier(FileTaskIdentifier.builder().fileTaskId(taskId).build())
                                .fileTaskEntity(FileTaskEntity.builder()
                                        .ossPath(createEvent.getFilePath()).commitTime(date)
                                        .fileTaskBizType(FileTaskBizTypeEnum
                                                .getByCode(req.getFileTaskBizType()))
                                        .fileTaskType(FileTaskTypeEnum.EXPORT).createTime(date)
                                        .fileTaskBizStatus(FileTaskBizStatusEnum.CREATE_TASK)
                                        .createId(userDataObjectEntity.getUserId())
                                        .userNick(userDataObjectEntity.getNickname())
                                        .fileTaskId(taskId)
                                        .fileName(
                                                String.format("%s0.csv", createEvent.getFileName()))
                                        .fileTaskName(createEvent.getFileName())
                                        .fileTaskDescription(createEvent.getTaskId().toString())
                                        .build())
                                .build()).thenReturn(eventRootMono);

                    });
                    // 发送任务导出事件
                }).flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot))).then();
    }

    @BusiCode
    @Override
    public Mono<Void> createImportLogistics(Mono<LogisticsImportReqDto> mono) {
        return commandDispatcher.executeCommand(mono, LogisticsImportCommand.class);
    }

    @BusiCode
    @Override
    public Mono<Void> createSendGoods(Mono<LogisticsSendReqDto> mono) {
        // 发布发货创建事件
        Function<Mono<Tuple2<SendGoodsRoot, LogisticsOrderRoot>>, Mono<Void>> publishEvent =
                tuple2Mono -> tuple2Mono.flatMap(tuple2 -> {
                    SendGoodsRoot t1 = tuple2.getT1();
                    LogisticsOrderRoot t2 = tuple2.getT2();
                    Long userId = -1L;
                    try {
                        userId = ReadSynchronizationUtils.getUserId();
                    } catch (ExceptionWrapperThrowable e) {
                        throw new RuntimeException(e);
                    }
                    Long finalUserId = userId;
                    return Flux.fromIterable(t1.getLogisticsOrderIdentifierList())
                            .flatMap(logisticsOrderIdentifier -> {
                                EventRoot eventRoot = EventRoot.builder()
                                        .domainEvent(SendGoodsCreateEvent.builder()
                                                .identifier(
                                                        EventRoot.getCommonsDomainEventIdentifier(
                                                                SendGoodsCreateEvent.class))
                                                .logisticsNo(
                                                        t1.getSendGoodsEntity().getLogisticsNo())
                                                .logisticsOrderType(t2.getLogisticsOrderEntity()
                                                        .getOrderValueObject()
                                                        .getLogisticsOrderType())
                                                .orderNo(t2.getLogisticsOrderEntity()
                                                        .getOrderValueObject().getOrderNo())
                                                .logisticsCorpName(t1.getSendGoodsEntity()
                                                        .getLogisticsCorpName())
                                                .logisticsOrderStatus(t2.getLogisticsOrderEntity()
                                                        .getLogisticsOrderStatus())
                                                .logisticsOrderId(logisticsOrderIdentifier
                                                        .getLogisticsOrderId())
                                                .sendGoodsTime(
                                                        t1.getSendGoodsEntity().getSendGoodsTime())
                                                .hasImport(false).userId(finalUserId).build())
                                        .build();
                                return eventRootService.publisheByMono(eventRoot);
                            }).then();
                });

        // 获取物流订单根对象
        Function<Long, Mono<LogisticsOrderRoot>> getLogisticsOrderRoot = logisticsOrderId -> {
            LogisticsByIdQuery query =
                    LogisticsByIdQuery.builder().logisticsOrderId(logisticsOrderId).build();
            return queryDispatcher.executeQuery(Mono.just(query), LogisticsByIdQuery.class,
                    LogisticsOrderRoot.class);
        };

        // 根据订单号获取发货根对象
        Function<String, Mono<SendGoodsRoot>> getSendGoodsRoot = orderNo -> {
            OrderDetailQuery query = OrderDetailQuery.builder().orderNo(orderNo).build();
            return queryDispatcher.executeQuery(Mono.just(query), OrderDetailQuery.class,
                    SendGoodsRoot.class);
        };

        // 验证地址并更新发货信息
        Function<Tuple2<SendGoodsRoot, LogisticsSendReqDto>, Mono<SendGoodsRoot>> validateAndUpdateSendGoods =
                tuple -> {
                    SendGoodsRoot sendGoodsRoot = tuple.getT1();
                    LogisticsSendReqDto request = tuple.getT2();
                    SendGoodsEntity entity = sendGoodsRoot.getSendGoodsEntity();

                    // 验证地址是否发生变更
                    if (!entity.getSendOrderAddr().getAddressId()
                            .equals(request.getUserAddressId())) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.ORDER_ADDR_CHANGE));
                    }

                    // 更新物流信息
                    entity.setLogisticsStatusSource("系统");
                    entity.setLogisticsNo(request.getLogisticsNo());
                    entity.setLogisticsCorpName(request.getLogisticsCorpName());

                    // 添加物流订单标识
                    sendGoodsRoot.getLogisticsOrderIdentifierList().add(LogisticsOrderIdentifier
                            .builder().logisticsOrderId(request.getLogisticsOrderId()).build());

                    return Mono.just(sendGoodsRoot);
                };

        // 创建发货命令
        Function<Tuple2<SendGoodsRoot, Long>, Mono<Long>> createSendGoodsCommand = tuple -> {
            SendGoodsRoot sendGoodsRoot = tuple.getT1();
            Long userId = tuple.getT2();

            return sendGoodsRootService.generateId().flatMap(sendGoodsId -> {
                CreateSendGoodsCommand command =
                        converter.convert(sendGoodsRoot, CreateSendGoodsCommand.class);
                command.setCreateId(userId);
                command.setSendGoodsId(sendGoodsId);

                return commandDispatcher.executeCommand(Mono.just(command),
                        CreateSendGoodsCommand.class, cmd -> cmd).thenReturn(sendGoodsId);
            });
        };

        // 创建物流发货关联命令
        Function<Tuple3<SendGoodsRoot, Long, Long>, Mono<Long>> createLogisticsSendGoodsCommand =
                tuple -> {
                    SendGoodsRoot sendGoodsRoot = tuple.getT1();
                    Long sendGoodsId = tuple.getT2();
                    Long userId = tuple.getT3();

                    CreateLogisticsSendGoodsCommand command = CreateLogisticsSendGoodsCommand
                            .builder().sendGoodsId(sendGoodsId)
                            .logisticsOrderIdList(sendGoodsRoot.getLogisticsOrderIdentifierList()
                                    .stream().map(LogisticsOrderIdentifier::getLogisticsOrderId)
                                    .collect(Collectors.toList()))
                            .createTime(sendGoodsRoot.getSendGoodsEntity().getCreateTime())
                            .createId(userId).build();

                    return commandDispatcher
                            .executeCommand(Mono.just(command),
                                    CreateLogisticsSendGoodsCommand.class, cmd -> cmd)
                            .thenReturn(sendGoodsId);
                };

        // 更新物流单状态
        Function<List<Long>, Mono<UpdateLogisticsOrderCommand>> updateLogisticsOrderStatus =
                logisticsOrderId -> {
                    Long userId = -1L;
                    try {
                        userId = ReadSynchronizationUtils.getUserId();
                    } catch (ExceptionWrapperThrowable e) {
                        throw new RuntimeException(e);
                    }

                    UpdateLogisticsOrderCommand command = UpdateLogisticsOrderCommand.builder()
                            .logisticsOrderIdList(logisticsOrderId)
                            .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED)
                            .updateTime(new Date()).userId(userId).build();

                    return commandDispatcher.executeCommand(Mono.just(command),
                            UpdateLogisticsOrderCommand.class, cmd -> cmd).thenReturn(command);
                };

        return mono
                .doOnNext(req -> log.info("Processing single logistics send request for order: {}",
                        req.getLogisticsOrderId()))
                .flatMap(logisticsSendReqDto -> {
                    ItemValueIdentifier logisticsCorp = ItemValueIdentifier.builder()
                            .itemValue(logisticsSendReqDto.getLogisticsCorpName())
                            .itemKey("LOGISTICS_CORP").lang(SystemLanguageLocale.ZH).build();
                    return dictItemDomainService.getById(logisticsCorp).flatMap(itemValueRspDto -> {
                        logisticsSendReqDto.setLogisticsCorpName(itemValueRspDto.getItemName());
                        return Mono.just(logisticsSendReqDto);
                    });
                }).flatMap(request -> {
                    // 1. 获取物流订单并获取发货信息
                    Mono<LogisticsOrderRoot> logisticsOrderRootMono =
                            getLogisticsOrderRoot.apply(request.getLogisticsOrderId());
                    return logisticsOrderRootMono
                            .map(logisticsOrderRoot -> logisticsOrderRoot
                                    .getLogisticsOrderEntity().getOrderValueObject().getOrderNo())
                            .flatMap(getSendGoodsRoot)
                            .flatMap(sendGoodsRoot -> validateAndUpdateSendGoods
                                    .apply(Tuples.of(sendGoodsRoot, request)))
                            .flatMap(sendGoodsRoot -> {
                                // 2. 创建发货记录和关联记录
                                return ReadSynchronizationUtils.getUserIdMono()
                                        .flatMap(userId -> createSendGoodsCommand
                                                .apply(Tuples.of(sendGoodsRoot, userId)).flatMap(
                                                        sendGoodsId -> createLogisticsSendGoodsCommand
                                                                .apply(Tuples.of(sendGoodsRoot,
                                                                        sendGoodsId, userId))))
                                        .thenReturn(sendGoodsRoot);
                            })
                            .flatMap(sendGoodsRoot -> updateLogisticsOrderStatus.apply(sendGoodsRoot
                                    .getLogisticsOrderIdentifierList().stream()
                                    .map(LogisticsOrderIdentifier::getLogisticsOrderId).toList())
                                    .thenReturn(sendGoodsRoot))
                            .flatMap(sendGoodsRoot -> publishEvent.apply(
                                    Mono.zip(Mono.just(sendGoodsRoot), logisticsOrderRootMono)));
                }).doOnSuccess(result -> log.info("Single logistics send processed successfully"))
                .doOnError(error -> log.error("Failed to process single logistics send", error));
    }

    @BusiCode
    @Override
    public Mono<Void> updateEdit(Mono<LogisticsEditReqDto> mono) {
        return commandDispatcher.executeCommand(mono, UpdateLogisticsNoCommand.class);
    }

    @BusiCode
    @Override
    public Mono<Void> createSendGoodsBatch(Mono<LogisticsSendBatchReqDto> mono) {


        // 获取物流订单根对象
        Function<Long, Mono<LogisticsOrderRoot>> getLogisticsOrderRoot = logisticsOrderId -> {
            LogisticsByIdQuery query =
                    LogisticsByIdQuery.builder().logisticsOrderId(logisticsOrderId).build();
            return queryDispatcher.executeQuery(Mono.just(query), LogisticsByIdQuery.class,
                    LogisticsOrderRoot.class);
        };

        // 发布发货创建事件
        BiFunction<List<SendGoodsRoot>, LogisticsSendBatchReqDto, Mono<Void>> publishEvent =
                (sendGoodsRoots, request) -> {
                    Long userId = -1L;
                    try {
                        userId = ReadSynchronizationUtils.getUserId();
                    } catch (ExceptionWrapperThrowable e) {
                        throw new RuntimeException(e);
                    }
                    Long finalUserId = userId;
                    return Flux.fromIterable(sendGoodsRoots)
                            .flatMap(sendGoodsRoot -> Flux
                                    .fromIterable(sendGoodsRoot.getLogisticsOrderIdentifierList())
                                    .flatMap(logisticsOrderIdentifier -> {
                                        return getLogisticsOrderRoot
                                                .apply(logisticsOrderIdentifier
                                                        .getLogisticsOrderId())
                                                .flatMap(logisticsOrderRoot -> {
                                                    return logisticsOrderRootQueryRepository
                                                            .selectById(LogisticsOrderRoot.builder()
                                                                    .identifier(
                                                                            LogisticsOrderIdentifier
                                                                                    .builder()
                                                                                    .logisticsOrderId(
                                                                                            logisticsOrderIdentifier
                                                                                                    .getLogisticsOrderId())
                                                                                    .build())
                                                                    .logisticsOrderEntity(
                                                                            LogisticsOrderEntity
                                                                                    .builder()
                                                                                    .logisticsOrderId(
                                                                                            logisticsOrderIdentifier
                                                                                                    .getLogisticsOrderId())
                                                                                    .build())
                                                                    .build())
                                                            .flatMap(logisticsOrderEntity -> {
                                                                EventRoot eventRoot = EventRoot
                                                                        .builder()
                                                                        .domainEvent(
                                                                                SendGoodsCreateEvent
                                                                                        .builder()
                                                                                        .identifier(
                                                                                                EventRoot
                                                                                                        .getCommonsDomainEventIdentifier(
                                                                                                                SendGoodsCreateEvent.class))
                                                                                        .logisticsNo(
                                                                                                request.getLogisticsNo())
                                                                                        .logisticsCorpName(
                                                                                                request.getLogisticsCorpName())
                                                                                        .logisticsOrderType(
                                                                                                logisticsOrderRoot
                                                                                                        .getLogisticsOrderEntity()
                                                                                                        .getOrderValueObject()
                                                                                                        .getLogisticsOrderType())
                                                                                        .orderNo(
                                                                                                logisticsOrderRoot
                                                                                                        .getLogisticsOrderEntity()
                                                                                                        .getOrderValueObject()
                                                                                                        .getOrderNo())
                                                                                        .logisticsOrderStatus(
                                                                                                logisticsOrderRoot
                                                                                                        .getLogisticsOrderEntity()
                                                                                                        .getLogisticsOrderStatus())
                                                                                        .logisticsOrderId(
                                                                                                logisticsOrderIdentifier
                                                                                                        .getLogisticsOrderId())
                                                                                        .sendGoodsTime(
                                                                                                sendGoodsRoot
                                                                                                        .getSendGoodsEntity()
                                                                                                        .getSendGoodsTime())
                                                                                        .createTime(
                                                                                                logisticsOrderEntity
                                                                                                        .getCreateTime())
                                                                                        .hasImport(
                                                                                                false)
                                                                                        .userId(finalUserId)
                                                                                        .build())
                                                                        .isQueue(true).build();
                                                                return eventRootService
                                                                        .publisheByMono(eventRoot);
                                                            });
                                                });
                                    }).then())
                            .then();
                };

        // 根据订单号获取发货根对象
        Function<String, Mono<SendGoodsRoot>> getSendGoodsRoot = orderNo -> {
            OrderDetailQuery query = OrderDetailQuery.builder().orderNo(orderNo).build();
            return queryDispatcher.executeQuery(Mono.just(query), OrderDetailQuery.class,
                    SendGoodsRoot.class);
        };

        // 验证地址并更新发货信息
        Function<Tuple2<SendGoodsRoot, LogisticsSendBatchReqDto>, Mono<SendGoodsRoot>> validateAndUpdateSendGoods =
                tuple -> {
                    SendGoodsRoot sendGoodsRoot = tuple.getT1();
                    LogisticsSendBatchReqDto request = tuple.getT2();
                    SendGoodsEntity entity = sendGoodsRoot.getSendGoodsEntity();

                    // 验证地址是否发生变更
                    if (!entity.getSendOrderAddr().getAddressId()
                            .equals(request.getUserAddressId())) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.ORDER_ADDR_CHANGE));
                    }

                    // 更新物流信息
                    entity.setLogisticsStatusSource("系统");
                    entity.setLogisticsNo(request.getLogisticsNo());
                    entity.setLogisticsCorpName(request.getLogisticsCorpName());

                    return Mono.just(sendGoodsRoot);
                };

        // 处理单个物流订单
        Function<Tuple2<Long, LogisticsSendBatchReqDto>, Mono<SendGoodsRoot>> processSingleLogisticsOrder =
                tuple -> {
                    Long logisticsOrderId = tuple.getT1();
                    LogisticsSendBatchReqDto request = tuple.getT2();

                    return getLogisticsOrderRoot.apply(logisticsOrderId)
                            .map(logisticsOrderRoot -> logisticsOrderRoot
                                    .getLogisticsOrderEntity().getOrderValueObject().getOrderNo())
                            .flatMap(getSendGoodsRoot)
                            .flatMap(sendGoodsRoot -> validateAndUpdateSendGoods
                                    .apply(Tuples.of(sendGoodsRoot, request)))
                            .map(sendGoodsRoot -> {
                                // 添加物流订单标识
                                sendGoodsRoot.getLogisticsOrderIdentifierList()
                                        .add(LogisticsOrderIdentifier.builder()
                                                .logisticsOrderId(logisticsOrderId).build());
                                return sendGoodsRoot;
                            });
                };

        // 创建发货命令
        Function<Tuple2<SendGoodsRoot, Long>, Mono<Long>> createSendGoodsCommand = tuple -> {
            SendGoodsRoot sendGoodsRoot = tuple.getT1();
            Long userId = tuple.getT2();

            return sendGoodsRootService.generateId().flatMap(sendGoodsId -> {
                CreateSendGoodsCommand command =
                        converter.convert(sendGoodsRoot, CreateSendGoodsCommand.class);
                command.setCreateId(userId);
                command.setSendGoodsId(sendGoodsId);

                return commandDispatcher.executeCommand(Mono.just(command),
                        CreateSendGoodsCommand.class, cmd -> cmd).thenReturn(sendGoodsId);
            });
        };

        // 创建物流发货关联命令
        Function<Tuple3<List<SendGoodsRoot>, Long, Long>, Mono<Long>> createLogisticsSendGoodsCommand =
                tuple -> {
                    List<SendGoodsRoot> sendGoodsRoots = tuple.getT1();
                    Long sendGoodsId = tuple.getT2();
                    Long userId = tuple.getT3();

                    return Flux.fromIterable(sendGoodsRoots).flatMap(sendGoodsRoot -> {
                        CreateLogisticsSendGoodsCommand command =
                                CreateLogisticsSendGoodsCommand.builder().sendGoodsId(sendGoodsId)
                                        .logisticsOrderIdList(sendGoodsRoot
                                                .getLogisticsOrderIdentifierList().stream()
                                                .map(LogisticsOrderIdentifier::getLogisticsOrderId)
                                                .collect(Collectors.toList()))
                                        .createTime(
                                                sendGoodsRoot.getSendGoodsEntity().getCreateTime())
                                        .createId(userId).build();

                        return commandDispatcher
                                .executeCommand(Mono.just(command),
                                        CreateLogisticsSendGoodsCommand.class, cmd -> cmd)
                                .thenReturn(sendGoodsId);
                    }).collectList().map(List::getFirst);

                };

        // 更新物流单状态
        Function<List<SendGoodsRoot>, Mono<UpdateLogisticsOrderCommand>> updateLogisticsOrderStatus =
                sendGoodsRoots -> {
                    ArrayList<Long> orderIds = new ArrayList<>();
                    for (SendGoodsRoot sendGoodsRoot : sendGoodsRoots) {
                        orderIds.addAll(sendGoodsRoot.getLogisticsOrderIdentifierList().stream()
                                .map(LogisticsOrderIdentifier::getLogisticsOrderId).toList());
                    }

                    Long userId = -1L;
                    try {
                        userId = ReadSynchronizationUtils.getUserId();
                    } catch (ExceptionWrapperThrowable e) {
                        throw new RuntimeException(e);
                    }
                    UpdateLogisticsOrderCommand command =
                            UpdateLogisticsOrderCommand.builder().logisticsOrderIdList(orderIds)
                                    .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED)
                                    .updateTime(new Date()).userId(userId).build();

                    return commandDispatcher.executeCommand(Mono.just(command),
                            UpdateLogisticsOrderCommand.class, cmd -> cmd).thenReturn(command);
                };

        return mono.doOnNext(
                req -> log.info("Processing batch logistics send request for {} logistics orders",
                        req.getLogisticsOrderIdList().size()))
                .flatMap(logisticsSendReqDto -> {
                    ItemValueIdentifier logisticsCorp = ItemValueIdentifier.builder()
                            .itemValue(logisticsSendReqDto.getLogisticsCorpName())
                            .itemKey("LOGISTICS_CORP").lang(SystemLanguageLocale.ZH).build();
                    return dictItemDomainService.getById(logisticsCorp).flatMap(itemValueRspDto -> {
                        logisticsSendReqDto.setLogisticsCorpName(itemValueRspDto.getItemName());
                        return Mono.just(logisticsSendReqDto);
                    });
                }).flatMap(request -> {
                    // 1. 批量处理所有物流订单
                    return Flux.fromIterable(request.getLogisticsOrderIdList())
                            .flatMap(logisticsOrderId -> processSingleLogisticsOrder
                                    .apply(Tuples.of(logisticsOrderId, request)))
                            .collectList().flatMap(sendGoodsRoots -> {
                                SendGoodsRoot first = sendGoodsRoots.getFirst();

                                // 2. 创建发货记录和关联记录
                                return ReadSynchronizationUtils.getUserIdMono()
                                        .flatMap(userId -> createSendGoodsCommand
                                                .apply(Tuples.of(first, userId)).flatMap(
                                                        sendGoodsId -> createLogisticsSendGoodsCommand
                                                                .apply(Tuples.of(sendGoodsRoots,
                                                                        sendGoodsId, userId))))
                                        .flatMap(sendGoodsRoot -> updateLogisticsOrderStatus
                                                .apply(sendGoodsRoots).thenReturn(sendGoodsRoots))
                                        .flatMap(sendGoodsRootList -> publishEvent
                                                .apply(sendGoodsRootList, request));
                            });
                }).doOnSuccess(result -> log.info("Batch logistics send processed successfully"))
                .doOnError(error -> log.error("Failed to process batch logistics send", error));
    }

    @BusiCode
    @Override
    public Mono<Void> cancel(Mono<LogisticsCancelReqDto> mono) {
        return null;
    }

    /**
     * 确认收货
     * 
     * @param mono mono
     * @return Mono<Void>
     */
    @BusiCode
    @Override
    public Mono<Void> updateConfirm(Mono<LogisticsConfirmReqDto> mono) {
        Date confimDate = new Date();

        /**
         * 获取物流订单状态
         */
        Function<LogisticsConfirmReqDto, Mono<LogisticsOrderRoot>> getLogisticsOrderRoot =
                reqDto -> {
                    LogisticsByIdQuery query = LogisticsByIdQuery.builder()
                            .logisticsOrderId(reqDto.getLogisticsOrderId()).build();
                    return queryDispatcher.executeQuery(Mono.just(query), LogisticsByIdQuery.class,
                            LogisticsOrderRoot.class);
                };

        /**
         * 检查物流订单状态
         */
        Function<LogisticsOrderRoot, Mono<LogisticsOrderRoot>> checkLogisticsOrderStatus =
                logisticsOrderRoot -> {
                    if (!logisticsOrderRoot.getLogisticsOrderEntity().getLogisticsOrderStatus()
                            .equals(LogisticsOrderStatusEnum.TO_BE_RECEIVED)) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.ORDER_CONFIRM_ERROR));
                    }
                    return Mono.just(logisticsOrderRoot);
                };

        /**
         * 更新物流订单状态
         */
        Function<LogisticsOrderRoot, Mono<LogisticsOrderRoot>> updateLogisticsOrderStatus =
                logisticsOrderRoot -> {
                    Long userId = -1L;
                    try {
                        userId = ReadSynchronizationUtils.getUserId();
                    } catch (ExceptionWrapperThrowable e) {
                        throw new RuntimeException(e);
                    }

                    UpdateLogisticsOrderCommand command = UpdateLogisticsOrderCommand.builder()
                            .logisticsOrderIdList(List.of(logisticsOrderRoot
                                    .getLogisticsOrderEntity().getLogisticsOrderId()))
                            .logisticsOrderStatus(LogisticsOrderStatusEnum.RECEIVED)
                            .updateTime(confimDate).userId(userId).build();

                    return commandDispatcher.executeCommand(Mono.just(command),
                            UpdateLogisticsOrderCommand.class, cmd -> cmd)
                            .thenReturn(logisticsOrderRoot);
                };

        Function<LogisticsOrderRoot, Mono<LogisticsOrderRoot>> saveOrder = logisticsOrderRoot -> {
            // 构建更新物流订单命令
            UpdateOrderLogisticsOrderCommand command = UpdateOrderLogisticsOrderCommand.builder()
                    .logisticsOrderId(
                            logisticsOrderRoot.getLogisticsOrderEntity().getLogisticsOrderId())
                    .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity()
                            .getOrderValueObject().getLogisticsOrderType().getCode())
                    .orderNo(logisticsOrderRoot.getLogisticsOrderEntity().getOrderValueObject()
                            .getOrderNo())
                    .logisticsOrderStatus(LogisticsOrderStatusEnum.RECEIVED.getCode())
                    .updateTime(new Date()).build();

            return commandDispatcher
                    .executeCommand(Mono.just(command), UpdateOrderLogisticsOrderCommand.class)
                    .thenReturn(logisticsOrderRoot);
        };

        Function<LogisticsOrderRoot, Mono<LogisticsOrderRoot>> saveSearchOrder =
                logisticsOrderRoot -> {
                    SearchLogisticsConfirmReqDto reqDto = SearchLogisticsConfirmReqDto.builder()
                            .confirmTime(confimDate)
                            .logisticsOrderId(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getLogisticsOrderId())
                            .orderNo(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getOrderValueObject().getOrderNo())
                            .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity()
                                    .getOrderValueObject().getLogisticsOrderType().getCode())
                            .logisticsOrderStatus(LogisticsOrderStatusEnum.RECEIVED.getCode())
                            .build();
                    reqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
                    log.debug("保存搜索领域订单：{}", JSONObject.toJSONString(reqDto));
                    return logisticsSearchService
                            .updateConfirm(Mono.just(reqDto))
                            .thenReturn(logisticsOrderRoot);
                };

        Function<LogisticsOrderRoot, Mono<Void>> publishEvent = logisticsOrderRoot -> {
            EventRoot eventRoot = EventRoot.builder().domainEvent(SendGoodsConfirmEvent.builder()
                    .identifier(
                            EventRoot.getCommonsDomainEventIdentifier(SendGoodsConfirmEvent.class))
                    .confirmTime(confimDate)
                    .logisticsOrderId(
                            logisticsOrderRoot.getLogisticsOrderEntity().getLogisticsOrderId())
                    .orderNo(logisticsOrderRoot.getLogisticsOrderEntity().getOrderValueObject()
                            .getOrderNo())
                    .logisticsOrderType(logisticsOrderRoot.getLogisticsOrderEntity()
                            .getOrderValueObject().getLogisticsOrderType())
                    .createTime(logisticsOrderRoot.getLogisticsOrderEntity().getCreateTime())
                    .logisticsOrderStatus(LogisticsOrderStatusEnum.RECEIVED).build()).build();
            return eventRootService.publisheByMono(eventRoot).then();
        };

        return mono.flatMap(getLogisticsOrderRoot).flatMap(checkLogisticsOrderStatus)
                .flatMap(updateLogisticsOrderStatus).flatMap(saveOrder).flatMap(saveSearchOrder)
                .flatMap(publishEvent);
    }

    @BusiCode
    @Override
    public Mono<Void> updateAddress(Mono<LogisticsOrderAddressModifyReq> mono) {
        return ReadSynchronizationUtils.getUserIdMono().flatMap(userId -> mono.flatMap(dto -> {
            Mono<LogisticsOrderEntity> getOrderRoot =
                    logisticsOrderRootQueryRepository
                            .selectById(LogisticsOrderRoot.builder()
                                    .identifier(LogisticsOrderIdentifier.builder()
                                            .logisticsOrderId(dto.getLogisticsOrderId()).build())
                                    .logisticsOrderEntity(LogisticsOrderEntity.builder()
                                            .logisticsOrderId(dto.getLogisticsOrderId()).build())
                                    .build());


            Function<LogisticsOrderEntity, Mono<Void>> doExecute = root -> {
                UserAddressIdReqDto reqDto = new UserAddressIdReqDto();
                reqDto.setSessionId(dto.getSessionId());
                reqDto.setUserAddressId(dto.getUserAddressId());
                return userQueryService.getUserAddress(Mono.just(reqDto))
                        .switchIfEmpty(Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                        .flatMap(rsp -> commandDispatcher.executeCommand(mono,
                                UpdateLogisticsOrderAddrCommand.class, command -> {
                                    command.setLogisticsOrderId(dto.getLogisticsOrderId());
                                    command.setLogisticsOrderType(root.getOrderValueObject()
                                            .getLogisticsOrderType().getCode());
                                    command.setUserAddressId(dto.getUserAddressId());
                                    command.setReceivingMobile(rsp.getMobile());
                                    command.setConsigneeName(rsp.getConsigneeName());
                                    command.setAddressSite(rsp.getAddressSite());
                                    command.setAddressDetail(rsp.getAddressDetail());
                                    command.setProvinceCode(rsp.getProvinceCode());
                                    command.setCityCode(rsp.getCityCode());
                                    command.setDistrictCode(rsp.getDistrictCode());
                                    return command;
                                }));
            };

            return getOrderRoot.flatMap(logisticsOrderEntity -> {
                if (!logisticsOrderEntity.getLogisticsOrderStatus()
                        .equals(LogisticsOrderStatusEnum.TO_BE_SHIPPED)) {
                    return Mono.error(new XkOrderApplicationException(
                            XkOrderApplicationErrorEnum.LOGISTICS_ORDER_ADDR_CHANGE_ERROR));
                }
                return Mono.just(logisticsOrderEntity);
            }).flatMap(doExecute);
        }));
    }

}
