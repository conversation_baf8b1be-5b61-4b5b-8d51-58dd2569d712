package com.xk.order.application.action.command.order;

import java.util.Date;

import com.myco.framework.cache.annotations.KeyProperty;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.order.application.dto.order.MallOrderPriceAppDto;
import com.xk.order.application.dto.order.MaterialOrderPriceDetailAppDto;
import com.xk.order.application.dto.order.MerchantOrderPriceDetailAppDto;
import com.xk.order.domain.model.order.OrderItemRoot;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.infrastructure.convertor.order.OrderTypeEnumConvertor;

import io.github.linpeilie.Converter;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.AutoMappings;
import lombok.*;
import lombok.experimental.Accessors;
import reactor.core.publisher.Mono;

@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = OrderItemEntity.class,
                uses = {CommonStatusEnumConvertor.class, OrderIdentifierConvertor.class,
                        OrderTypeEnumConvertor.class},
                reverseConvertGenerate = false),
        @AutoMapper(target = MerchantOrderPriceDetailAppDto.class, convertGenerate = false),
        @AutoMapper(target = MallOrderPriceAppDto.class, convertGenerate = false),
        @AutoMapper(target = MaterialOrderPriceDetailAppDto.class, convertGenerate = false)})
public class CreateOrderItemCommand extends AbstractActionCommand {

    /**
     * 订单条目ID
     */
    @KeyProperty
    private Long orderItemId;

    /**
     * 条目位置
     */
    private Integer itemPosition;

    /**
     * 订单ID（业务主键）
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品主图
     */
    private String goodsImage;

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;

    /**
     * 商品类型 1-福盒 2-边锋盒子 3-错卡密 4-原盒
     */
    private Integer productType;

    /**
     * 随机模式 1-选队/随机球员 2-选队/随机卡种 10-非选队/随机卡种(不带编) 11-非选队/随机卡种(带编) 12-非选队/随机球队 13-非选队/随机球员
     */
    private Integer randomType;

    /**
     * 剩余随机状态
     */
    private Integer remainRandomStatus;

    /**
     * 购买数量
     */
    private Integer buyCount;

    /**
     * 规格ID
     */
    private Long specificationId;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 库存ID
     */
    private Long stockId;

    /**
     * 价格ID
     */
    private Long priceId;

    /**
     * 购买单价
     */
    private Long unitPrice;

    /**
     * 条目总金额
     */
    private Long itemTotalAmount;

    /**
     * 条目优惠金额
     */
    private Long itemDiscountAmount;

    /**
     * 条目实付
     */
    private Long itemPayAmount;

    /**
     * 分发id
     */
    private Long distributionId;

    /**
     * 随机条目id
     */
    private Long randomItemId;

    /**
     * 创建人ID
     */
    @AutoMappings({
            @AutoMapping(targetClass = OrderItemEntity.class, target = "createValObj.createId")})
    private Long createId;

    /**
     * 创建时间
     */
    @AutoMappings({
            @AutoMapping(targetClass = OrderItemEntity.class, target = "createValObj.createTime")})
    private Date createTime;

    public void buildCreate(Long createId, Date createTime) {
        this.createId = createId;
        this.userId = createId;
        this.createTime = createTime;
    }

    public Mono<OrderItemRoot> buildRoot(Converter converter) {
        OrderItemEntity orderItemEntity = converter.convert(this, OrderItemEntity.class);
        OrderItemRoot root = OrderItemRoot.builder().identifier(orderItemEntity.getIdentifier())
                .orderItemEntity(orderItemEntity).build();
        return Mono.just(root);
    }
}
