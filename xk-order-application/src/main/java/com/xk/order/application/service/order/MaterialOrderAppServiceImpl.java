package com.xk.order.application.service.order;

import static com.xk.order.enums.order.OrderTypeEnum.MATERIAL_PRODUCT;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.lock.ZookeeperLockObject;
import com.myco.mydata.domain.model.object.user.UserObjectRoot;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.lock.LockRootService;
import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.common.CommonStatusEnum;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.enums.goods.GoodsTypeEnum;
import com.xk.goods.interfaces.dto.req.goods.SpecificationIdBatchReqDto;
import com.xk.goods.interfaces.dto.res.goods.SpecDetailResDto;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.order.application.action.command.order.CreateOrderCommand;
import com.xk.order.application.action.command.order.CreateOrderItemCommand;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.dto.order.MaterialOrderPriceAppDto;
import com.xk.order.application.dto.order.MaterialOrderPriceDetailAppDto;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelDurationEnum;
import com.xk.order.interfaces.dto.req.order.MaterialOrderCreateReq;
import com.xk.order.interfaces.dto.req.order.MaterialOrderPriceReq;
import com.xk.order.interfaces.dto.rsp.order.MaterialOrderPriceRsp;
import com.xk.order.interfaces.dto.rsp.order.OrderCreateRsp;
import com.xk.order.interfaces.service.order.MaterialOrderAppService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialOrderAppServiceImpl implements MaterialOrderAppService {

    private final OrderRootService orderRootService;
    private final OrderItemRootService orderItemRootService;
    private final GoodsSearchQueryService goodsSearchQueryService;
    private final UserQueryService userQueryService;
    private final StockRootService stockRootService;
    private final LockRootService lockRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private final Converter converter;

    @BusiCode
    @Override
    public Mono<MaterialOrderPriceRsp> createCalculatePrice(Mono<MaterialOrderPriceReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false)
                .flatMap(user -> mono.flatMap(dto -> doGetMaterialPrice(user, dto)))
                .map(appDto -> converter.convert(appDto, MaterialOrderPriceRsp.class));
    }

    @BusiCode
    @Override
    public Mono<OrderCreateRsp> createMaterial(Mono<MaterialOrderCreateReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(false).flatMap(user -> {
            Long userId = user.getUserDataObjectEntity().getUserId();
            Long corpId = user.getUserDataObjectEntity().getCorpId();
            PlatformTypeEnum lastPlatformType =
                    user.getUserDataObjectEntity().getLastPlatformType();
            Date createDate = new Date();

            return mono.flatMap(dto -> {
                Mono<Boolean> acquireLock = lockRootService.acquireTransactionObjectLockMono(
                        ZookeeperLockObject.LOCKS_CORP_FREE_QUOTA, corpId);

                Function<Boolean, Mono<MaterialOrderPriceAppDto>> checkLockAndGetPrice = lock -> {
                    if (Boolean.FALSE.equals(lock)) {
                        return Mono.error(new XkOrderApplicationException(
                                XkOrderApplicationErrorEnum.UPDATING_FREE_QUOTA));
                    }
                    return doGetMaterialPrice(user, dto);
                };

                Function<MaterialOrderPriceAppDto, Mono<MaterialOrderPriceAppDto>> validatePrice =
                        priceDto -> {
                            if (!priceDto.getPayAmount().equals(dto.getPayAmount())) {
                                return Mono.error(new XkOrderApplicationException(
                                        XkOrderApplicationErrorEnum.MATERIAL_AMOUNT_NOT_MATCH));
                            }
                            return Mono.just(priceDto);
                        };

                Function<MaterialOrderPriceAppDto, Mono<Tuple2<CreateOrderCommand, MaterialOrderPriceAppDto>>> prepareOrderCommand =
                        priceDto -> {
                            CreateOrderCommand orderCmd =
                                    converter.convert(dto, CreateOrderCommand.class);
                            return orderCmd.processAddress(dto.getUserAddressId(), userQueryService)
                                    .flatMap(
                                            cmd -> Mono
                                                    .zip(orderRootService.generateId(),
                                                            orderRootService.generateOrderNo(
                                                                    MATERIAL_PRODUCT))
                                                    .map(tuple -> {
                                                        cmd.setOrderId(tuple.getT1());
                                                        cmd.setOrderNo(tuple.getT2());
                                                        return cmd;
                                                    }))
                                    .map(cmd -> Tuples.of(cmd, priceDto));
                        };

                Function<Tuple2<CreateOrderCommand, MaterialOrderPriceAppDto>, Mono<Tuple2<CreateOrderCommand, MaterialOrderPriceAppDto>>> executeOrderCommand =
                        tuple -> {
                            CreateOrderCommand command = tuple.getT1();
                            MaterialOrderPriceAppDto priceDto = tuple.getT2();
                            return commandDispatcher.executeCommand(Mono.just(command),
                                    CreateOrderCommand.class, cmd -> {
                                        cmd.setUserId(userId);
                                        cmd.setCorpId(corpId);
                                        cmd.setOrderType(MATERIAL_PRODUCT.getCode());
                                        cmd.setPayAmount(priceDto.getPayAmount());
                                        cmd.setTotalAmount(priceDto.getTotalAmount());
                                        cmd.setOrderTotalBuyCount(priceDto.getOrderTotalBuyCount());
                                        cmd.buildCreate(userId, createDate);
                                        if (priceDto.getFreeQuota() > 0) {
                                            cmd.setFreeQuotaDiscountStatus(
                                                    CommonStatusEnum.ENABLE.getCode());
                                        }
                                        cmd.setFreeQuotaDiscountAmount(priceDto.getFreeQuota());
                                        if (lastPlatformType != null) {
                                            cmd.setPlatformType(lastPlatformType.getValue());
                                        }
                                        cmd.setBusinessType(BusinessTypeEnum.XING_KA.getValue());
                                        return cmd;
                                    }).thenReturn(tuple);
                        };

                Function<Tuple2<CreateOrderCommand, MaterialOrderPriceAppDto>, Mono<OrderCreateRsp>> createOrderItemsAndResponse =
                        tuple -> {
                            CreateOrderCommand cmd = tuple.getT1();
                            MaterialOrderPriceAppDto priceDto = tuple.getT2();
                            return createOrderItems(cmd, priceDto).thenReturn(OrderCreateRsp
                                    .builder().orderNo(cmd.getOrderNo())
                                    .cancelDeadlineTime(DateUtils.addMinutes(cmd.getCreateTime(),
                                            OrderCancelDurationEnum.THREE_MINUTES.getDuration()))
                                    .build());
                        };

                return acquireLock.flatMap(checkLockAndGetPrice).flatMap(validatePrice)
                        .flatMap(prepareOrderCommand).flatMap(executeOrderCommand)
                        .flatMap(createOrderItemsAndResponse);
            });
        });
    }

    /**
     * 创建订单项
     */
    private Mono<Void> createOrderItems(CreateOrderCommand orderCmd,
            MaterialOrderPriceAppDto priceDto) {
        if (CollectionUtils.isEmpty(priceDto.getDetailDtoList())) {
            return Mono.error(new SystemWrapperThrowable(SystemErrorEnum.VALIDATE_FAILURE));
        }

        return Flux.fromIterable(priceDto.getDetailDtoList())
                .flatMap(detailDto -> orderItemRootService.generateId().flatMap(itemId -> {
                    CreateOrderItemCommand itemCmd =
                            converter.convert(detailDto, CreateOrderItemCommand.class);
                    itemCmd.setOrderItemId(itemId);
                    itemCmd.setOrderId(orderCmd.getOrderId());
                    itemCmd.setOrderNo(orderCmd.getOrderNo());
                    itemCmd.setOrderType(MATERIAL_PRODUCT.getCode());
                    itemCmd.setItemPayAmount(detailDto.getSpecPayAmount());
                    itemCmd.setItemTotalAmount(detailDto.getSpecTotalAmount());
                    itemCmd.buildCreate(orderCmd.getCreateId(), orderCmd.getCreateTime());
                    return commandDispatcher.executeCommand(Mono.just(itemCmd),
                            CreateOrderItemCommand.class);
                })).then();
    }

    private <T extends MaterialOrderPriceReq> Mono<MaterialOrderPriceAppDto> doGetMaterialPrice(
            UserObjectRoot user, T dto) {
        Long corpId = user.getUserDataObjectEntity().getCorpId();
        if (corpId == null) {
            return Mono.error(new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
        }

        // 获取物料价格信息的函数
        Supplier<Mono<List<SpecDetailResDto>>> getMaterialPrice = () -> {
            SpecificationIdBatchReqDto reqDto = new SpecificationIdBatchReqDto();
            reqDto.setSessionId(dto.getSessionId());
            reqDto.setSpecificationId(dto.getSpecificationIdList());
            return goodsSearchQueryService.getSpecificationDetail(Mono.just(reqDto))
                    .filter(CollectionUtils::isNotEmpty).switchIfEmpty(Mono.error(
                            new SystemWrapperThrowable(SystemErrorEnum.GEN_RECORD_NOT_EXISTS)));
        };

        // 计算最终价格的函数
        Function<List<SpecDetailResDto>, Mono<MaterialOrderPriceAppDto>> calculatePrice =
                specList -> stockRootService
                        .getRemainRealStock(StockBusinessTypeEnum.CORP_FREE_QUOTA,
                                StringIdentifier.builder().id(corpId.toString()).build())
                        .flatMap(stockRemainValObj -> Mono
                                .just(stockRemainValObj.getRemainRealStock()))
                        .defaultIfEmpty(0L).flatMap(freeQuota -> {
                            MaterialOrderPriceAppDto appDto = new MaterialOrderPriceAppDto();

                            // 检查是否存在非物料商品
                            boolean hasNonMaterialGoods = specList.stream()
                                    .anyMatch(spec -> !GoodsTypeEnum.MATERIAL_PRODUCT.getCode()
                                            .equals(spec.getGoodsType())); // 假设有 isMaterialGoods()

                            if (hasNonMaterialGoods) {
                                return Mono.error(new SystemWrapperThrowable(
                                        SystemErrorEnum.VALIDATE_FAILURE));
                            }

                            // 使用BigDecimal进行精确计算
                            BigDecimal totalAmountBD = specList.stream()
                                    .map(spec -> BigDecimal.valueOf(spec.getAmount()))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            BigDecimal freeQuotaBD = BigDecimal.valueOf(freeQuota);
                            BigDecimal usableFreeQuotaBD =
                                    totalAmountBD.compareTo(freeQuotaBD) < 0 ? totalAmountBD
                                            : freeQuotaBD;

                            BigDecimal remainFreeQuotaBD = freeQuotaBD.subtract(usableFreeQuotaBD);
                            BigDecimal payAmountBD = totalAmountBD.subtract(usableFreeQuotaBD);

                            BigDecimal orderTotalBuyCount = BigDecimal.ZERO;
                            appDto.setTotalAmount(totalAmountBD.longValue());
                            appDto.setFreeQuota(usableFreeQuotaBD.longValue());
                            appDto.setRemainFreeQuota(remainFreeQuotaBD.longValue());
                            appDto.setPayAmount(payAmountBD.longValue());

                            List<MaterialOrderPriceDetailAppDto> detailList = new ArrayList<>();
                            BigDecimal allocatedFreeQuotaBD = BigDecimal.ZERO;

                            // 先计算n-1个规格的免费额度
                            for (int i = 0; i < specList.size() - 1; i++) {
                                SpecDetailResDto spec = specList.get(i);
                                BigDecimal specAmountBD = BigDecimal.valueOf(spec.getAmount());

                                // 计算比例 (specAmount / totalAmount)
                                BigDecimal ratioBD = specAmountBD.divide(totalAmountBD, 10,
                                        RoundingMode.HALF_UP);

                                // 计算该规格的免费额度
                                BigDecimal specFreeQuotaBD = usableFreeQuotaBD.multiply(ratioBD)
                                        .setScale(0, RoundingMode.HALF_UP);

                                allocatedFreeQuotaBD = allocatedFreeQuotaBD.add(specFreeQuotaBD);

                                // 计算该规格的支付金额
                                BigDecimal specPayAmountBD = specAmountBD.subtract(specFreeQuotaBD);
                                orderTotalBuyCount =
                                        orderTotalBuyCount.add(new BigDecimal(spec.getSpecName()));
                                detailList.add(MaterialOrderPriceDetailAppDto.builder()
                                        .specificationId(spec.getSpecificationId())
                                        .specTotalAmount(spec.getAmount())
                                        .specFreeQuota(specFreeQuotaBD.longValue())
                                        .buyCount(Integer.valueOf(spec.getSpecName()))
                                        .specPayAmount(specPayAmountBD.longValue())
                                        .specName(spec.getSpecName()).goodsId(spec.getGoodsId())
                                        .goodsImage(spec.getGoodsImage())
                                        .goodsName(spec.getGoodsName())
                                        .unitPrice(spec.getUnitPrice())
                                        .currencyType(spec.getCurrencyType())
                                        .priceId(spec.getPriceId()).stockId(spec.getStockId())
                                        .build());
                            }

                            // 最后一个规格的免费额度用总额减去已分配的额度，确保总和正确
                            if (!specList.isEmpty()) {
                                SpecDetailResDto lastSpec = specList.getLast();
                                BigDecimal lastSpecFreeQuotaBD =
                                        usableFreeQuotaBD.subtract(allocatedFreeQuotaBD);
                                BigDecimal lastSpecPayAmountBD =
                                        BigDecimal.valueOf(lastSpec.getAmount())
                                                .subtract(lastSpecFreeQuotaBD);
                                orderTotalBuyCount = orderTotalBuyCount
                                        .add(new BigDecimal(lastSpec.getSpecName()));
                                detailList.add(MaterialOrderPriceDetailAppDto.builder()
                                        .specificationId(lastSpec.getSpecificationId())
                                        .specTotalAmount(lastSpec.getAmount())
                                        .specFreeQuota(lastSpecPayAmountBD.longValue())
                                        .buyCount(Integer.valueOf(lastSpec.getSpecName()))
                                        .specPayAmount(lastSpecPayAmountBD.longValue())
                                        .specName(lastSpec.getSpecName())
                                        .goodsId(lastSpec.getGoodsId())
                                        .goodsName(lastSpec.getGoodsName())
                                        .unitPrice(lastSpec.getUnitPrice())
                                        .currencyType(lastSpec.getCurrencyType())
                                        .priceId(lastSpec.getPriceId())
                                        .stockId(lastSpec.getStockId()).build());
                            }

                            appDto.setOrderTotalBuyCount(orderTotalBuyCount.intValue());
                            appDto.setDetailDtoList(detailList);
                            return Mono.just(appDto);
                        });

        return getMaterialPrice.get().flatMap(calculatePrice);
    }
}
