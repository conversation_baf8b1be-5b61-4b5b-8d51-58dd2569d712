package com.xk.order.application.handler.command.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.order.CreateOrderLogisticsOrderCommand;
import com.xk.order.domain.repository.order.OrderRootRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateOrderLogisticsOrderCommandHandler
        implements IActionCommandHandler<CreateOrderLogisticsOrderCommand, Void> {

    private final Converter converter;
    private final OrderRootRepository orderRootRepository;

    @Override
    public Mono<Void> execute(Mono<CreateOrderLogisticsOrderCommand> mono) {
        return mono.flatMap(command -> orderRootRepository.save(command.buildRoot(converter)));
    }
}
