package com.xk.order.application.handler.event.order;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.service.stock.StockRootService;
import com.xk.enums.stock.StockBusinessTypeEnum;
import com.xk.goods.domain.event.goods.GoodsStockEmptyEvent;
import com.xk.order.domain.event.logistics.LogisticsOrderCreateEvent;
import com.xk.order.domain.event.order.OrderPaidEvent;
import com.xk.order.domain.model.logistics.LogisticsOrderRoot;
import com.xk.order.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.order.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.order.domain.model.logistics.valobj.OrderValueObject;
import com.xk.order.domain.model.order.entity.OrderItemEntity;
import com.xk.order.domain.model.order.id.OrderIdentifier;
import com.xk.order.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.order.domain.service.logistics.LogisticsOrderRootService;
import com.xk.order.domain.service.order.OrderItemRootService;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.order.enums.order.OrderTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaidEventHandler extends AbstractEventVerticle<OrderPaidEvent> {

    private final OrderItemRootService orderItemRootService;

    private final StockRootService stockRootService;

    private final LogisticsOrderRootService logisticsOrderRootService;

    private final LogisticsOrderRootRepository logisticsOrderRootRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<OrderPaidEvent> mono) {
        return mono
                .doOnNext(event -> log.info("Processing order paid event for order: {}",
                        event.getOrderNo()))
                .flatMap(this::processOrderPaidEvent)
                .doOnSuccess(result -> log.info("Order paid event processed successfully"))
                .doOnError(error -> log.error("Failed to process order paid event", error));
    }

    /**
     * 处理订单支付事件的主要逻辑
     */
    private Mono<Void> processOrderPaidEvent(OrderPaidEvent event) {
        // 获取订单项并处理库存售罄检查
        Flux<OrderItemEntity> orderItems = getOrderItems(event);
        Mono<Void> stockProcessing =
                orderItems.take(1).flatMap(item -> processStockEmpty(item, event)).then();

        // 处理物流订单创建
        Mono<Void> logisticsProcessing = processLogisticsOrder(event);

        return stockProcessing.then(logisticsProcessing);
    }

    /**
     * 获取订单项
     */
    private Flux<OrderItemEntity> getOrderItems(OrderPaidEvent event) {
        OrderIdentifier orderIdentifier =
                OrderIdentifier.builder().orderNo(event.getOrderNo()).build();
        return orderItemRootService.searchEntityByOrderNo(orderIdentifier);
    }

    /**
     * 处理物流订单创建
     */
    private Mono<Void> processLogisticsOrder(OrderPaidEvent event) {
        return switch (event.getOrderType()) {
            case OrderTypeEnum.MALL_PRODUCT, OrderTypeEnum.MATERIAL_PRODUCT ->
                createAndSaveLogisticsOrder(event).flatMap(this::publishLogisticsOrderCreateEvent);
            default -> {
                log.debug("Order type {} does not require logistics order creation",
                        event.getOrderType());
                yield Mono.empty();
            }
        };
    }

    /**
     * 创建并保存物流订单
     */
    private Mono<LogisticsOrderRoot> createAndSaveLogisticsOrder(OrderPaidEvent event) {
        return createLogisticsOrderRoot(event)
                .flatMap(logisticsOrderRoot -> logisticsOrderRootRepository.save(logisticsOrderRoot)
                        .thenReturn(logisticsOrderRoot))
                .doOnSuccess(root -> log.debug(
                        "Logistics order created for order: {}, logistics order ID: {}",
                        event.getOrderNo(), root.getLogisticsOrderEntity().getLogisticsOrderId()));
    }

    /**
     * 创建物流订单根对象
     */
    private Mono<LogisticsOrderRoot> createLogisticsOrderRoot(OrderPaidEvent event) {
        return logisticsOrderRootService.generateId()
                .map(id -> LogisticsOrderRoot.builder()
                        .identifier(LogisticsOrderIdentifier.builder().logisticsOrderId(id).build())
                        .logisticsOrderEntity(buildLogisticsOrderEntity(id, event)).build());
    }

    /**
     * 构建物流订单实体
     */
    private LogisticsOrderEntity buildLogisticsOrderEntity(Long id, OrderPaidEvent event) {
        Date createTime = new Date();
        return LogisticsOrderEntity.builder().logisticsOrderId(id)
                .orderValueObject(OrderValueObject.builder().orderNo(event.getOrderNo())
                        .logisticsOrderType(mapToLogisticsOrderType(event.getOrderType())).build())
                .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_SHIPPED).createId(-1L)
                .createTime(createTime).orderStatusTime(createTime).build();

    }

    /**
     * 发布物流订单创建事件(用于search和数仓同步数据用)
     */
    private Mono<Void> publishLogisticsOrderCreateEvent(LogisticsOrderRoot logisticsOrderRoot) {
        LogisticsOrderEntity entity = logisticsOrderRoot.getLogisticsOrderEntity();

        LogisticsOrderCreateEvent createEvent = LogisticsOrderCreateEvent.builder()
                .identifier(
                        EventRoot.getCommonsDomainEventIdentifier(LogisticsOrderCreateEvent.class))
                .logisticsOrderType(entity.getOrderValueObject().getLogisticsOrderType())
                .orderNo(entity.getOrderValueObject().getOrderNo())
                .logisticsOrderId(entity.getLogisticsOrderId()).createTime(entity.getCreateTime())
                .userId(-1L)
                .build();

        EventRoot eventRoot = EventRoot.builder().domainEvent(createEvent).isQueue(true).build();

        return eventRootService.publisheByMono(eventRoot).then()
                .doOnSuccess(v -> log.debug("Published logistics order create event for order: {}",
                        entity.getOrderValueObject().getOrderNo()));
    }

    /**
     * 将订单类型映射为物流订单类型
     */
    private LogisticsOrderTypeEnum mapToLogisticsOrderType(OrderTypeEnum orderType) {
        return switch (orderType) {
            case OrderTypeEnum.MALL_PRODUCT -> LogisticsOrderTypeEnum.MALL;
            case OrderTypeEnum.MATERIAL_PRODUCT -> LogisticsOrderTypeEnum.MATERIAL;
            default -> {
                log.warn("Unsupported order type for logistics: {}", orderType);
                yield null;
            }
        };
    }

    /**
     * 处理商品是否售罄的事件
     *
     * @param itemEntity itemEntity
     * @param event event
     * @return Mono<Void>
     */
    private Mono<Void> processStockEmpty(OrderItemEntity itemEntity, OrderPaidEvent event) {
        if (OrderTypeEnum.MATERIAL_PRODUCT.equals(event.getOrderType())) {
            return Mono.empty();
        }
        StringIdentifier businessId =
                StringIdentifier.builder().id(itemEntity.getGoodsId().toString()).build();
        return stockRootService.getRemainRealStock(StockBusinessTypeEnum.GOODS, businessId)
                .flatMap(stock -> {
                    if (stock.getRemainRealStock() > 0) {
                        return Mono.empty();
                    }

                    return stockRootService
                            .getPaidRealStock(StockBusinessTypeEnum.GOODS, businessId)
                            .flatMap(v -> {
                                // 证明还存在待支付的订单
                                if (v.getPaidRealStock() > 0) {
                                    return Mono.empty();
                                }
                                return eventRootService.publisheByMono(EventRoot.builder()
                                        .domainEvent(GoodsStockEmptyEvent.builder()
                                                .identifier(
                                                        EventRoot.getCommonsDomainEventIdentifier(
                                                                GoodsStockEmptyEvent.class))
                                                .goodsId(itemEntity.getGoodsId()).build())
                                        .isQueue(true).build()).then();
                            });
                });
    }
}
