package com.xk.order.application.service.order;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.myco.mydata.commons.util.MD5Util;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.SystemWrapperThrowable;
import com.myco.mydata.domain.model.proxy.annotation.BusiCode;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.context.ReadSynchronizationUtils;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.order.application.commons.OrderDictEnum;
import com.xk.order.application.commons.XkOrderApplicationErrorEnum;
import com.xk.order.application.support.XkOrderApplicationException;
import com.xk.order.domain.event.order.OrderCancelEvent;
import com.xk.order.domain.event.order.OrderCancelPaidEvent;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderCancelTypeEnum;
import com.xk.order.enums.order.OrderStatusEnum;
import com.xk.order.enums.order.OrderTypeEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;
import com.xk.order.interfaces.dto.req.order.OrderCloseReq;
import com.xk.order.interfaces.dto.req.order.OrderNoRequireReq;
import com.xk.order.interfaces.service.order.PayOrderService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class PayOrderServiceImpl implements PayOrderService {

    private final OrderRootService orderRootService;
    private final DictObjectDomainService dictObjectDomainService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @BusiCode
    @Override
    public Mono<Void> updateClosePaidOrder(Mono<OrderCloseReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    UserTypeEnum userType = userObject.getUserDataObjectEntity().getUserType();
                    if (!UserTypeEnum.MANAGER.equals(userType)) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    EventRoot eventRoot = EventRoot.builder().domainEvent(OrderCancelPaidEvent
                            .builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderCancelPaidEvent.class))
                            .orderNo(dto.getOrderNo())
                            .orderCancelType(OrderCancelTypeEnum.ADMIN_CANCEL)
                            .updateId(userObject.getUserDataObjectEntity().getUserId()).build())
                            .isQueue(true).build();
                    return dictObjectDomainService
                            .getSystemConfigValue(OrderDictEnum.MANAGER_TRADING_PASSWORD)
                            .flatMap(config -> {
                                if (!Objects.equals(config, MD5Util.md5(dto.getPassword()))) {
                                    return Mono.error(new XkOrderApplicationException(
                                            XkOrderApplicationErrorEnum.TRADING_PASSWORD_ERROR));
                                }

                                return orderRootService
                                        .getRootNoLogistics(
                                                OrderIdentifierConvertor.map(dto.getOrderNo()))
                                        .switchIfEmpty(Mono.error(new SystemWrapperThrowable(
                                                SystemErrorEnum.GEN_RECORD_NOT_EXISTS)))
                                        .flatMap(v -> v.checkNoneMerchantRefundPaidStatus()
                                                .thenReturn(eventRoot));
                            });
                })).flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot).then());
    }

    @BusiCode
    @Override
    public Mono<Void> updateCloseMerchantPayOrder(Mono<OrderCloseReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    UserTypeEnum userType = userObject.getUserDataObjectEntity().getUserType();
                    if (!UserTypeEnum.MANAGER.equals(userType)) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    EventRoot eventRoot = EventRoot.builder().domainEvent(OrderCancelEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderCancelEvent.class))
                            .orderNo(dto.getOrderNo()).retryCount(0)
                            .orderCancelType(OrderCancelTypeEnum.ADMIN_CANCEL)
                            .updateId(userObject.getUserDataObjectEntity().getUserId()).build())
                            .isQueue(true).build();
                    return dictObjectDomainService
                            .getSystemConfigValue(OrderDictEnum.MANAGER_TRADING_PASSWORD)
                            .flatMap(config -> {
                                if (!Objects.equals(config, MD5Util.md5(dto.getPassword()))) {
                                    return Mono.error(new XkOrderApplicationException(
                                            XkOrderApplicationErrorEnum.TRADING_PASSWORD_ERROR));
                                }
                                return orderRootService
                                        .getRootNoLogistics(
                                                OrderIdentifierConvertor.map(dto.getOrderNo()))
                                        .filter(root -> OrderTypeEnum.MERCHANT_PRODUCT
                                                .equals(root.getOrderEntity().getOrderType()))
                                        .switchIfEmpty(Mono.error(new SystemWrapperThrowable(
                                                SystemErrorEnum.UNSUPPORTED_OPERATION)))
                                        .filter(root -> root.getOrderEntity().getOrderStatus()
                                                .equals(OrderStatusEnum.WAIT_PAID))
                                        .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                                XkOrderApplicationErrorEnum.ORDER_CANCEL_STATUS_ERROR)))
                                        .thenReturn(eventRoot);
                            });
                })).flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot).then());
    }

    @BusiCode
    @Override
    public Mono<Void> updateCorpCloseMerchantPayOrder(Mono<OrderNoRequireReq> mono) {
        return ReadSynchronizationUtils.getUserObjectMono(true)
                .flatMap(userObject -> mono.flatMap(dto -> {
                    Long corpId = userObject.getUserDataObjectEntity().getCorpId();
                    if (corpId == null) {
                        return Mono.error(
                                new SystemWrapperThrowable(SystemErrorEnum.UNSUPPORTED_OPERATION));
                    }
                    EventRoot eventRoot = EventRoot.builder().domainEvent(OrderCancelEvent.builder()
                            .identifier(EventRoot
                                    .getCommonsDomainEventIdentifier(OrderCancelEvent.class))
                            .orderNo(dto.getOrderNo()).retryCount(0)
                            .orderCancelType(OrderCancelTypeEnum.CORP_CANCEL)
                            .updateId(userObject.getUserDataObjectEntity().getUserId()).build())
                            .isQueue(true).build();
                    return orderRootService
                            .getRootNoLogistics(OrderIdentifierConvertor.map(dto.getOrderNo()))
                            .filter(root -> root.getOrderEntity().getOrderStatus()
                                    .equals(OrderStatusEnum.WAIT_PAID))
                            .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                    XkOrderApplicationErrorEnum.ORDER_CANCEL_STATUS_ERROR)))
                            .filter(root -> Objects.equals(root.getOrderEntity().getCorpId(),
                                    corpId))
                            .switchIfEmpty(Mono.error(new XkOrderApplicationException(
                                    SystemErrorEnum.UNSUPPORTED_OPERATION)))
                            .thenReturn(eventRoot);
                })).flatMap(eventRoot -> eventRootService.publisheByMono(eventRoot).then());
    }


}
