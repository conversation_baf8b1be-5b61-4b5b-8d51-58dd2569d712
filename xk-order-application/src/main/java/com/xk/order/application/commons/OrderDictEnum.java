package com.xk.order.application.commons;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum OrderDictEnum implements DictObjectEnum {

    ORDER_EXPIRE_TIME("180"),
    PAY_ORDER_GOODS_NAME("星卡"),
    MANAGER_TRADING_PASSWORD("123456")
    ;

    private static final Map<String, OrderDictEnum> MAP;

    static {
        MAP = Arrays.stream(OrderDictEnum.values())
                .collect(Collectors.toMap(OrderDictEnum::name, enumValue -> enumValue));
    }

    private final String defaultValue;

    public static OrderDictEnum getEnum(String name) {
        return MAP.get(name);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return name();
    }
}
