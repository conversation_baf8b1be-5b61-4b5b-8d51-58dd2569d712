package com.xk.order.application.dto.order;

import java.util.List;

import com.xk.order.interfaces.dto.rsp.order.MaterialOrderPriceRsp;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = MaterialOrderPriceRsp.class, reverseConvertGenerate = false)})
public class MaterialOrderPriceAppDto {

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 免费额度减免
     */
    private Long freeQuota;

    /**
     * 剩余免费额度
     */
    private Long remainFreeQuota;

    /**
     * 合计金额
     */
    private Long payAmount;

    /**
     * 订单购买数量
     */
    private Integer orderTotalBuyCount;

    private List<MaterialOrderPriceDetailAppDto> detailDtoList;
}
