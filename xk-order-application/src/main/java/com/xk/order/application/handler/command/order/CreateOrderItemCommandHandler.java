package com.xk.order.application.handler.command.order;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.xk.order.application.action.command.order.CreateOrderItemCommand;
import com.xk.order.domain.service.order.OrderItemRootService;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class CreateOrderItemCommandHandler
        implements IActionCommandHandler<CreateOrderItemCommand, Void> {

    private final Converter converter;
    private final OrderItemRootService orderItemRootService;

    @Override
    public Mono<Void> execute(Mono<CreateOrderItemCommand> mono) {
        return mono.flatMap(command -> orderItemRootService.saveRoot(command.buildRoot(converter)));
    }
}
