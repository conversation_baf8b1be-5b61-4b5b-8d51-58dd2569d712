package com.xk.order.application.handler.event.sendgoods;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.logistics.UpdateLogisticsOrderCommand;
import com.xk.order.application.action.command.order.UpdateOrderLogisticsOrderCommand;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateDetailEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 发货创建事件处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendGoodsCreateEventHandler extends AbstractEventVerticle<SendGoodsCreateEvent> {

    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<SendGoodsCreateEvent> mono) {
        return mono.doOnNext(event -> log.info(
                "Processing send goods create event: logisticsOrderId={}, orderNo={}, status={}",
                event.getLogisticsOrderId(), event.getOrderNo(), event.getLogisticsOrderStatus()))
                .flatMap(this::updateLogisticsOrderStatus)
                .flatMap(this::updateLogisticsOrder)
                .then(mono.flatMap(this::publishDetailEventOnSuccess))
                .onErrorResume(
                        error -> mono.flatMap(event -> publishDetailEventOnError(event, error)))
                .doOnSuccess(v -> log.info("Send goods create event processed successfully"))
                .doOnError(error -> log.error("Failed to process send goods create event", error));
    }

    /**
     * 更新订单
     */
    public Mono<Void> updateLogisticsOrder(SendGoodsCreateEvent event) {
        UpdateOrderLogisticsOrderCommand command = buildUpdateOrderCommand(event);

        return commandDispatcher
                .executeCommand(Mono.just(command), UpdateOrderLogisticsOrderCommand.class)
                .doOnSuccess(v -> log.debug("Successfully updated logistics order: {}",
                        event.getLogisticsOrderId()))
                .doOnError(error -> log.error("Failed to update logistics order: {}",
                        event.getLogisticsOrderId(), error));
    }

    /**
     * 更新物流订单
     * 
     * @param event event
     * @return Mono<SendGoodsCreateEvent>
     */
    public Mono<SendGoodsCreateEvent> updateLogisticsOrderStatus(SendGoodsCreateEvent event) {
        if (!event.getHasImport()) {
            return Mono.just(event);
        }
        log.debug("导入更新物流订单数据: {}", JSONObject.toJSONString(event));
        UpdateLogisticsOrderCommand command = UpdateLogisticsOrderCommand.builder()
                .logisticsOrderIdList(List.of(event.getLogisticsOrderId()))
                .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED)
                .updateTime(event.getSendGoodsTime()).userId(event.getUserId()).build();

        return commandDispatcher
                .executeCommand(Mono.just(command), UpdateLogisticsOrderCommand.class, cmd -> cmd)
                .thenReturn(event);
    }

    /**
     * 构建更新订单命令
     */
    public UpdateOrderLogisticsOrderCommand buildUpdateOrderCommand(SendGoodsCreateEvent event) {
        return UpdateOrderLogisticsOrderCommand.builder()
                .logisticsOrderId(event.getLogisticsOrderId()).orderNo(event.getOrderNo())
                .logisticsOrderType(event.getLogisticsOrderType() != null
                        ? event.getLogisticsOrderType().getCode()
                        : null)
                .logisticsCorpName(event.getLogisticsCorpName())
                .logisticsOrderStatus(LogisticsOrderStatusEnum.TO_BE_RECEIVED.getCode())
                .logisticsNo(event.getLogisticsNo()).updateTime(new Date()).build();
    }

    /**
     * 成功时发布详情事件
     */
    public Mono<Void> publishDetailEventOnSuccess(SendGoodsCreateEvent event) {
        if (!shouldPublishDetailEvent(event)) {
            return Mono.empty();
        }

        SendGoodsCreateDetailEvent detailEvent = buildDetailEvent(event, null);
        return publishDetailEvent(detailEvent).doOnSuccess(v -> log
                .debug("Successfully published detail event for order: {}", event.getOrderNo()));
    }

    /**
     * 错误时发布详情事件
     */
    public Mono<Void> publishDetailEventOnError(SendGoodsCreateEvent event, Throwable error) {
        if (!shouldPublishDetailEvent(event)) {
            return Mono.empty();
        }

        SendGoodsCreateDetailEvent detailEvent = buildDetailEvent(event, error.getMessage());
        return publishDetailEvent(detailEvent).doOnSuccess(
                v -> log.debug("Successfully published error detail event for order: {}",
                        event.getOrderNo()))
                .onErrorResume(publishError -> {
                    log.error("Failed to publish error detail event for order: {}",
                            event.getOrderNo(), publishError);
                    return Mono.empty();
                });
    }

    /**
     * 判断是否需要发布详情事件
     */
    public boolean shouldPublishDetailEvent(SendGoodsCreateEvent event) {
        Boolean hasImport = event.getHasImport();
        return hasImport != null && hasImport;
    }

    /**
     * 构建详情事件
     */
    public SendGoodsCreateDetailEvent buildDetailEvent(SendGoodsCreateEvent event,
            String errorMessage) {
        SendGoodsCreateDetailEvent.SendGoodsCreateDetailEventBuilder builder =
                SendGoodsCreateDetailEvent.builder()
                        .identifier(EventRoot
                                .getCommonsDomainEventIdentifier(SendGoodsCreateDetailEvent.class))
                        .logisticsOrderId(event.getLogisticsOrderId()).orderNo(event.getOrderNo())
                        .goodsName(event.getGoodsName()).goodsCount(event.getGoodsCount())
                        .logisticsCorpName(event.getLogisticsCorpName())
                        .logisticsNo(event.getLogisticsNo())
                        .logisticsOrderStatus(event.getLogisticsOrderStatus())
                        .fileTaskId(event.getFileTaskId());

        if (errorMessage != null) {
            builder.errRemark(errorMessage);
        }

        return builder.build();
    }

    /**
     * 发布详情事件
     */
    public Mono<Void> publishDetailEvent(SendGoodsCreateDetailEvent detailEvent) {
        EventRoot eventRoot = EventRoot.builder().domainEvent(detailEvent).isQueue(true).build();

        return eventRootService.publisheByMono(eventRoot).then()
                .doOnError(error -> log.error("Failed to publish detail event", error));
    }
}
