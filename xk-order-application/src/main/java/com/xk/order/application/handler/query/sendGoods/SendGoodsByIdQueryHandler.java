package com.xk.order.application.handler.query.sendGoods;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.xk.order.application.action.query.sendgoods.SendGoodsByIdQuery;
import com.xk.order.domain.model.logisticsSendGoods.LogisticsSendGoodsOrderRoot;
import com.xk.order.domain.model.logisticsSendGoods.entity.LogisticsSendGoodsEntity;
import com.xk.order.domain.model.logisticsSendGoods.id.LogisticsSendGoodsIdentifier;
import com.xk.order.domain.model.sendGoods.SendGoodsRoot;
import com.xk.order.domain.model.sendGoods.entity.SendGoodsEntity;
import com.xk.order.domain.model.sendGoods.id.SendGoodsIdentifier;
import com.xk.order.domain.repository.logisticsSendGoods.LogisticsSendGoodsRootQueryRepository;
import com.xk.order.domain.repository.sendgoods.SendGoodsRootQueryRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class SendGoodsByIdQueryHandler
        implements IActionQueryHandler<SendGoodsByIdQuery, SendGoodsEntity> {

    private final LogisticsSendGoodsRootQueryRepository logisticsSendGoodsRootQueryRepository;

    private final SendGoodsRootQueryRepository sendGoodsRootQueryRepository;

    @Override
    public Mono<SendGoodsEntity> execute(Mono<SendGoodsByIdQuery> query) {
        return query.flatMap(id -> logisticsSendGoodsRootQueryRepository
                .selectById(LogisticsSendGoodsOrderRoot.builder()
                        .identifier(LogisticsSendGoodsIdentifier.builder().id(-1L).build())
                        .logisticsSendGoodsEntity(LogisticsSendGoodsEntity.builder()
                                .logisticsOrderId(id.getLogisticsOrderId()).build())
                        .build())
                .collectList().flatMap(list -> {
                    if (CollectionUtils.isNotEmpty(list)) {
                        return sendGoodsRootQueryRepository.selectById(SendGoodsRoot.builder()
                                .identifier(SendGoodsIdentifier.builder()
                                        .sendGoodsId(list.getFirst().getSendGoodsId()).build())
                                .sendGoodsEntity(SendGoodsEntity.builder()
                                        .sendGoodsId(list.getFirst().getSendGoodsId()).build())
                                .build());
                    }
                    return Mono.empty();
                }));

    }

}
