package com.xk.order.application.handler.event.filetask;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.domain.repository.filetask.FileTaskRootQueryRepository;
import com.xk.order.domain.event.task.OrderImportScanEvent;
import com.xk.order.domain.event.task.OrderImportStatusUpdateEvent;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderImportScanEventHandler extends AbstractEventVerticle<OrderImportScanEvent> {

    private final Converter converter;

    private final FileTaskRootQueryRepository fileTaskRootQueryRepository;

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<Void> handle(Mono<OrderImportScanEvent> event) {
        return fileTaskRootQueryRepository.selectByStatus().flatMap(fileTaskEntity -> {
            OrderImportStatusUpdateEvent updateEvent = OrderImportStatusUpdateEvent.builder()
                    .identifier(EventRoot
                            .getCommonsDomainEventIdentifier(OrderImportStatusUpdateEvent.class))
                    .fileTaskId(fileTaskEntity.getFileTaskId())
                    .totalCount(fileTaskEntity.getTotalCount())
                    .createTime(fileTaskEntity.getCreateTime()).build();
            EventRoot eventRoot = EventRoot.builder().domainEvent(updateEvent).isTry(false).build();
            log.debug("发送任务更新消息：{}", JSONObject.toJSONString(updateEvent));
            return eventRootService.publisheByMono(eventRoot);
        }).then();
    }

    @Override
    public boolean isBlockExecute() {
        return true;
    }
}
