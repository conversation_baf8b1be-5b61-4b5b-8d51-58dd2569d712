package com.xk.order.application.handler.event.payment;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.myco.mydata.application.handler.command.ActionCommandDispatcher;
import com.myco.mydata.infrastructure.event.handler.vertx.AbstractEventVerticle;
import com.xk.order.application.action.command.order.UpdateOrderCommand;
import com.xk.order.domain.event.payment.RefundSuccessEvent;
import com.xk.order.domain.service.order.OrderRootService;
import com.xk.order.enums.order.OrderRefundStatusEnum;
import com.xk.order.infrastructure.convertor.order.OrderIdentifierConvertor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefundSuccessEventHandler extends AbstractEventVerticle<RefundSuccessEvent> {

    private final OrderRootService orderRootService;
    private final ActionCommandDispatcher<AbstractActionCommand> commandDispatcher;


    @Override
    public Mono<Void> handle(Mono<RefundSuccessEvent> mono) {
        return mono.flatMap(event -> orderRootService
                .getRoot(OrderIdentifierConvertor.map(event.getOrderNo())).flatMap(root -> {
                    if (!Objects.equals(root.getOrderPayEntity().getPayNo(), event.getPayNo())) {
                        return Mono.empty();
                    }
                    return commandDispatcher.executeCommand(Mono.just(new UpdateOrderCommand()),
                            UpdateOrderCommand.class, command -> {
                                command.setOrderNo(event.getOrderNo());
                                command.setRefundStatus(OrderRefundStatusEnum.REFUNDED.getCode());
                                return command;
                            });
                }));
    }
}
