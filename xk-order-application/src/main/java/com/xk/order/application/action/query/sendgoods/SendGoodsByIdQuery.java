package com.xk.order.application.action.query.sendgoods;

import com.myco.mydata.application.handler.query.IActionQuery;
import com.xk.order.interfaces.dto.req.logistics.LogisticsQueryByIdReqDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = LogisticsQueryByIdReqDto.class)})
public class SendGoodsByIdQuery implements IActionQuery {

    private Long logisticsOrderId;

}
