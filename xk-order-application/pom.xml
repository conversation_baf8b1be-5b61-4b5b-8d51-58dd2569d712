<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.order</groupId>
        <artifactId>xk-order</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-order-application</artifactId>
    <packaging>jar</packaging>
    <name>xk-order-application</name>
    <description>xk-order-application</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.order.application.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.order.application.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.order.application.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkOrderApplicationConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.order</groupId>
            <artifactId>xk-order-domain-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.order</groupId>
            <artifactId>xk-order-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.order</groupId>
            <artifactId>xk-order-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.order</groupId>
            <artifactId>xk-order-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.goods</groupId>
            <artifactId>xk-goods-domain-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.ewd</groupId>
            <artifactId>xk-ewd-domain-event</artifactId>
        </dependency>
    </dependencies>
</project>
