package com.xk.message.domain.model.template.entity;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.message.enums.message.BusinessTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageTemplateEntity implements Entity<LongIdentifier> {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板参数 逗号隔开
     */
    private String templateParam;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;

    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

    /**
     * 标题
     */
    private String title;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(templateId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
