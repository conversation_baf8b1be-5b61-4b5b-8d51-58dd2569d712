package com.xk.message.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {
    USER(100000, "用户账户"),
    ORDER(200000, "订单管理"),
    PAY_SYSTEM(300000, "支付系统"),
    GOODS_CENTER(400000, "商品中心"),
    SERVICE_SYSTEM(500000, "客服系统"),
    RISK_SYSTEM(600000, "风控系统"),
    OPERATION_CENTER(700000, "运维中心"),
    APPLICATION(800000, "应用管理"),
    LIVE_SYSTEM(900000, "直播系统"),

    ;

    private static final Map<Integer, BusinessTypeEnum> MAP;

    static {
        MAP = Arrays.stream(BusinessTypeEnum.values())
                .collect(Collectors.toMap(BusinessTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static BusinessTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

    public Integer getValue() {
        return this.code;
    }
}
