package com.xk.message.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessSceneEnum {
    /**
     * 历史场景兼容
     */
    ACCOUNT_ORDER_PAY_SUCCESS(1, "下单成功通知", null),
    SEND_ACCOUNT(2, "发货通知", null),
    VERIFICATION_CODE(3, "验证码", null),
    GOODS_CHECK_PASS(4, "商品审核通过", null),
    GOODS_CHECK_REJECT(5, "商品审核驳回", null),
    /**
     * 历史场景兼容
     */

    REGISTRATION_AUTH_SCENE_1(100101, "用户手机号注册", BusinessModuleEnum.REGISTRATION_AUTH),
    LOGIN_AUTH_SCENE_1(100201, "用户手机号登录", BusinessModuleEnum.LOGIN_AUTH),
    ACCOUNT_SECURITY_SCENE_1(100301, "修改绑定手机号", BusinessModuleEnum.ACCOUNT_SECURITY),
    ACCOUNT_SECURITY_SCENE_2(100302, "修改密码", BusinessModuleEnum.ACCOUNT_SECURITY),
    CREATE_ORDER_SCENE_1(200101, "用户下单成功", BusinessModuleEnum.CREATE_ORDER),
    SHIPMENT_HANDLE_SCENE_1(200201, "商家后台操作发货", BusinessModuleEnum.SHIPMENT_HANDLE),
    ORDER_REFUND_SCENE_1(200301, "用户/商家/运营取消订单退款", BusinessModuleEnum.ORDER_REFUND),
    ORDER_CANCEL_SCENE_1(200401, "用户/商家/运营取消订单", BusinessModuleEnum.ORDER_CANCEL),
    PAY_TIMEOUT_SCENE_1(300101, "订单未支付自动关闭", BusinessModuleEnum.PAY_TIMEOUT),
    GOODS_SHELVED_SCENE_1(400101, "商家上架新商品", BusinessModuleEnum.GOODS_SHELVED),
    GOODS_PUBLICITY_SCENE_1(400201, "商家开始公示", BusinessModuleEnum.GOODS_PUBLICITY),
    GOODS_PUBLICITY_SCENE_2(400202, "商家已公示", BusinessModuleEnum.GOODS_PUBLICITY),
    WORK_ORDER_HANDLE_SCENE_1(500101, "客服回复用户咨询", BusinessModuleEnum.WORK_ORDER_HANDLE),
    ILLEGAL_HANDLE_SCENE_1(600101, "用户举报商品下架", BusinessModuleEnum.ILLEGAL_HANDLE),
    SYSTEM_MAINTENANCE_SCENE_1(700101, "系统升级维护预告", BusinessModuleEnum.SYSTEM_MAINTENANCE),
    VERSION_UPDATING_SCENE_1(800101, "强制更新版本上线", BusinessModuleEnum.VERSION_UPDATING),
    PUBLICITY_LIVE_SCENE_1(900101, "直播公示提示", BusinessModuleEnum.PUBLICITY_LIVE),

    ;

    private static final Map<Integer, BusinessSceneEnum> MAP;

    static {
        MAP = Arrays.stream(BusinessSceneEnum.values())
                .collect(Collectors.toMap(BusinessSceneEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;
    private final BusinessModuleEnum parent;

    public static BusinessSceneEnum getByCode(Integer code) {
        return MAP.get(code);
    }

    public Integer getValue() {
        return this.code;
    }
}
