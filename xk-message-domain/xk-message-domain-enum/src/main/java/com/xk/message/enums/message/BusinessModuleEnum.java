package com.xk.message.enums.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BusinessModuleEnum {
    REGISTRATION_AUTH(100100, "注册认证", BusinessTypeEnum.USER),
    LOGIN_AUTH(100200, "登录认证", BusinessTypeEnum.USER),
    ACCOUNT_SECURITY(100300, "账户安全", BusinessTypeEnum.USER),
    CREATE_ORDER(200100, "订单创建", BusinessTypeEnum.ORDER),
    SHIPMENT_HANDLE(200200, "发货处理", BusinessTypeEnum.ORDER),
    ORDER_REFUND(200300, "订单退款", BusinessTypeEnum.ORDER),
    ORDER_CANCEL(200400, "订单取消", BusinessTypeEnum.ORDER),
    PAY_TIMEOUT(300100, "支付超时", BusinessTypeEnum.PAY_SYSTEM),
    GOODS_SHELVED(400100, "商品上新", BusinessTypeEnum.GOODS_CENTER),
    GOODS_PUBLICITY(400200, "商品公示", BusinessTypeEnum.GOODS_CENTER),
    WORK_ORDER_HANDLE(500100, "工单处理", BusinessTypeEnum.SERVICE_SYSTEM),
    ILLEGAL_HANDLE(600100, "违规处理", BusinessTypeEnum.RISK_SYSTEM),
    SYSTEM_MAINTENANCE(700100, "系统维护", BusinessTypeEnum.OPERATION_CENTER),
    VERSION_UPDATING(800100, "版本更新", BusinessTypeEnum.APPLICATION),
    PUBLICITY_LIVE(900100, "公示直播", BusinessTypeEnum.LIVE_SYSTEM),

    ;

    private static final Map<Integer, BusinessModuleEnum> MAP;

    static {
        MAP = Arrays.stream(BusinessModuleEnum.values())
                .collect(Collectors.toMap(BusinessModuleEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;
    private final BusinessTypeEnum parent;

    public static BusinessModuleEnum getByCode(Integer code) {
        return MAP.get(code);
    }

    public Integer getValue() {
        return this.code;
    }
}
