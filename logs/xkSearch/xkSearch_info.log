[NettyClientSelector_1:600]2025-08-16 09:51:02.030 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:438]2025-08-16 09:51:02.041 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:591]2025-08-16 09:51:02.068 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:438]2025-08-16 09:51:02.068 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:446]2025-08-16 09:51:02.135 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:413]2025-08-16 09:51:02.136 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:493]2025-08-16 09:51:02.136 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:696]2025-08-16 09:51:02.136 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[main-SendThread(*************:2181):1460]2025-08-16 09:51:02.136 WARN  [ClientCnxn:] - Session 0x100000114063188 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063188, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[NettyClientSelector_1:670]2025-08-16 09:51:02.140 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:515]2025-08-16 09:51:02.142 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:574]2025-08-16 09:51:02.142 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:523]2025-08-16 09:51:02.147 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[nacos-grpc-client-executor-*************-161:1533]2025-08-16 09:51:02.300 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Receive server push request, request = ClientDetectionRequest, requestId = 10409
[nacos-grpc-client-executor-*************-161:1533]2025-08-16 09:51:02.300 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Ack server push request, request = ClientDetectionRequest, requestId = 10409
[nacos-grpc-client-executor-*************-161:1533]2025-08-16 09:51:02.302 ERROR [GrpcClient:] - [1755285243040_60.191.37.34_37610]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 09:51:02.303 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 09:51:02.303 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main-EventThread:1461]2025-08-16 09:51:02.323 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[Curator-ConnectionStateManager-0:97]2025-08-16 09:51:02.323 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[nacos-grpc-client-executor-*************-188:1534]2025-08-16 09:51:02.681 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 12316
[nacos-grpc-client-executor-*************-188:1534]2025-08-16 09:51:02.681 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 12316
[nacos-grpc-client-executor-*************-188:1534]2025-08-16 09:51:02.681 ERROR [GrpcClient:] - [1755285243218_60.191.37.34_37718]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 09:51:02.685 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 09:51:02.685 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 09:51:02.974 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Success to connect a server [*************:8848], connectionId = 1755309055894_60.191.37.34_48139
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 09:51:02.974 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Abandon prev connection, server is *************:8848, connectionId is 1755285243040_60.191.37.34_37610
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 09:51:02.974 INFO  [client:] - Close current connection 1755285243040_60.191.37.34_37610
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.974 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.974 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.975 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.975 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.975 INFO  [naming:] - Grpc connection connect
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 09:51:03.071 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Success to connect a server [*************:8848], connectionId = 1755309055972_60.191.37.34_48205
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 09:51:03.071 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755285243218_60.191.37.34_37718
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 09:51:03.071 INFO  [client:] - Close current connection 1755285243218_60.191.37.34_37718
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 09:51:03.071 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 09:51:03.071 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 09:51:03.071 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 09:51:03.071 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Connected,notify listen context...
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:03.611 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:03.656 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[NettyClientSelector_1:422]2025-08-16 09:51:05.022 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:686]2025-08-16 09:51:05.039 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:06.658 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:06.679 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:09.681 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[main-SendThread(*************:2181):1460]2025-08-16 09:51:12.961 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):1460]2025-08-16 09:51:12.961 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):1460]2025-08-16 09:51:12.972 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:50114, server: *************/*************:2181
[main-SendThread(*************:2181):1460]2025-08-16 09:51:12.982 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063188 has expired
[main-SendThread(*************:2181):1460]2025-08-16 09:51:12.982 WARN  [ClientCnxn:] - Session 0x100000114063188 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063188 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:1461]2025-08-16 09:51:12.982 WARN  [ConnectionState:] - Session expired event received
[main-EventThread:1461]2025-08-16 09:51:12.982 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@64a0a1c6
[main-EventThread:1461]2025-08-16 09:51:12.984 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:1461]2025-08-16 09:51:12.984 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:1461]2025-08-16 09:51:12.984 INFO  [ConnectionStateManager:] - State change: LOST
[main-EventThread:1461]2025-08-16 09:51:12.984 INFO  [ClientCnxn:] - EventThread shut down for session: 0x100000114063188
[Curator-ConnectionStateManager-0:97]2025-08-16 09:51:12.984 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[main-SendThread(*************:2181):1573]2025-08-16 09:51:22.051 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):1573]2025-08-16 09:51:22.051 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):1573]2025-08-16 09:51:22.063 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:50656, server: *************/*************:2181
[main-SendThread(*************:2181):1573]2025-08-16 09:51:22.076 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x100000114063189, negotiated timeout = 40000
[main-EventThread:1574]2025-08-16 09:51:22.077 INFO  [ConnectionStateManager:] - State change: RECONNECTED
[Curator-ConnectionStateManager-0:97]2025-08-16 09:51:22.077 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: RECONNECTED
[main-EventThread:1574]2025-08-16 09:51:22.090 INFO  [EnsembleTracker:] - New config event received: {}
[NettyClientSelector_1:438]2025-08-16 10:19:09.907 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:455]2025-08-16 10:19:09.910 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:505]2025-08-16 10:19:09.910 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:616]2025-08-16 10:19:09.910 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[nacos-grpc-client-executor-*************-458:2097]2025-08-16 10:19:09.915 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 12317
[nacos-grpc-client-executor-*************-458:2097]2025-08-16 10:19:09.916 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 12317
[nacos-grpc-client-executor-*************-458:2097]2025-08-16 10:19:09.916 ERROR [GrpcClient:] - [1755309055972_60.191.37.34_48205]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:19:09.916 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:19:09.916 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[NettyClientSelector_1:114]2025-08-16 10:19:09.926 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:404]2025-08-16 10:19:09.926 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:404]2025-08-16 10:19:09.926 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:481]2025-08-16 10:19:09.927 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:545]2025-08-16 10:19:09.928 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:623]2025-08-16 10:19:09.928 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[main-SendThread(*************:2181):1573]2025-08-16 10:19:09.930 WARN  [ClientCnxn:] - Session 0x100000114063189 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063189, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[NettyClientSelector_1:200]2025-08-16 10:19:09.930 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:591]2025-08-16 10:19:09.936 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[nacos-grpc-client-executor-*************-437:2095]2025-08-16 10:19:09.937 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Receive server push request, request = ClientDetectionRequest, requestId = 9670
[nacos-grpc-client-executor-*************-437:2095]2025-08-16 10:19:09.937 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Ack server push request, request = ClientDetectionRequest, requestId = 9670
[nacos-grpc-client-executor-*************-437:2095]2025-08-16 10:19:09.937 ERROR [GrpcClient:] - [1755309055894_60.191.37.34_48139]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:19:09.937 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:19:09.937 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[NettyClientSelector_1:413]2025-08-16 10:19:09.972 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:446]2025-08-16 10:19:09.976 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:523]2025-08-16 10:19:09.978 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:600]2025-08-16 10:19:09.980 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:696]2025-08-16 10:19:09.980 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:19:10.027 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Success to connect a server [*************:8848], connectionId = 1755310743089_60.191.37.34_40780
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:19:10.027 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Abandon prev connection, server is *************:8848, connectionId is 1755309055894_60.191.37.34_48139
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:19:10.027 INFO  [client:] - Close current connection 1755309055894_60.191.37.34_48139
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 INFO  [naming:] - Grpc connection connect
[main-EventThread:1574]2025-08-16 10:19:10.030 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[Curator-ConnectionStateManager-0:97]2025-08-16 10:19:10.030 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:19:10.036 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Success to connect a server [*************:8848], connectionId = 1755310743078_60.191.37.34_40762
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:19:10.036 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755309055972_60.191.37.34_48205
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:19:10.036 INFO  [client:] - Close current connection 1755309055972_60.191.37.34_48205
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:19:10.036 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:19:10.036 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:19:10.036 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:19:10.036 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Connected,notify listen context...
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 10:19:12.102 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[main-SendThread(*************:2181):1573]2025-08-16 10:19:20.659 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):1573]2025-08-16 10:19:20.659 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):1573]2025-08-16 10:19:20.671 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:51738, server: *************/*************:2181
[main-SendThread(*************:2181):1573]2025-08-16 10:19:20.682 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063189 has expired
[main-EventThread:1574]2025-08-16 10:19:20.682 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(*************:2181):1573]2025-08-16 10:19:20.682 WARN  [ClientCnxn:] - Session 0x100000114063189 for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063189 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:1574]2025-08-16 10:19:20.682 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@64a0a1c6
[main-EventThread:1574]2025-08-16 10:19:20.685 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:1574]2025-08-16 10:19:20.686 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:1574]2025-08-16 10:19:20.686 INFO  [ConnectionStateManager:] - State change: LOST
[main-EventThread:1574]2025-08-16 10:19:20.686 INFO  [ClientCnxn:] - EventThread shut down for session: 0x100000114063189
[Curator-ConnectionStateManager-0:97]2025-08-16 10:19:20.686 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[main-SendThread(*************:2181):2131]2025-08-16 10:19:29.721 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):2131]2025-08-16 10:19:29.721 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):2131]2025-08-16 10:19:29.730 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:52473, server: *************/*************:2181
[main-SendThread(*************:2181):2131]2025-08-16 10:19:29.740 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x10000011406318a, negotiated timeout = 40000
[main-EventThread:2132]2025-08-16 10:19:29.740 INFO  [ConnectionStateManager:] - State change: RECONNECTED
[Curator-ConnectionStateManager-0:97]2025-08-16 10:19:29.740 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: RECONNECTED
[main-EventThread:2132]2025-08-16 10:19:29.750 INFO  [EnsembleTracker:] - New config event received: {}
[NettyClientSelector_1:438]2025-08-16 10:32:10.276 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:114]2025-08-16 10:32:10.287 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:404]2025-08-16 10:32:10.287 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:463]2025-08-16 10:32:10.288 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:463]2025-08-16 10:32:10.288 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:515]2025-08-16 10:32:10.289 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:481]2025-08-16 10:32:10.289 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:623]2025-08-16 10:32:10.290 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:686]2025-08-16 10:32:10.290 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:670]2025-08-16 10:32:10.290 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:702]2025-08-16 10:32:10.290 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[main-SendThread(*************:2181):2131]2025-08-16 10:32:10.290 WARN  [ClientCnxn:] - Session 0x10000011406318a for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x10000011406318a, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-*************-695:2627]2025-08-16 10:32:10.293 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Receive server push request, request = ClientDetectionRequest, requestId = 12318
[nacos-grpc-client-executor-*************-695:2627]2025-08-16 10:32:10.293 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Ack server push request, request = ClientDetectionRequest, requestId = 12318
[nacos-grpc-client-executor-*************-717:2625]2025-08-16 10:32:10.293 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 10414
[nacos-grpc-client-executor-*************-717:2625]2025-08-16 10:32:10.293 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 10414
[nacos-grpc-client-executor-*************-717:2625]2025-08-16 10:32:10.293 ERROR [GrpcClient:] - [1755310743078_60.191.37.34_40762]Request stream onCompleted, switch server
[nacos-grpc-client-executor-*************-695:2627]2025-08-16 10:32:10.293 ERROR [GrpcClient:] - [1755310743089_60.191.37.34_40780]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:32:10.293 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:32:10.293 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:32:10.293 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:32:10.293 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[NettyClientSelector_1:523]2025-08-16 10:32:10.307 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:554]2025-08-16 10:32:10.308 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:600]2025-08-16 10:32:10.309 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:32:10.367 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Success to connect a server [*************:8848], connectionId = 1755311523438_60.191.37.34_46457
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:32:10.367 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755310743078_60.191.37.34_40762
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:32:10.367 INFO  [client:] - Close current connection 1755310743078_60.191.37.34_40762
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:32:10.367 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:32:10.368 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:32:10.368 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:32:10.368 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Connected,notify listen context...
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:32:10.372 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Success to connect a server [*************:8848], connectionId = 1755311523449_60.191.37.34_46468
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:32:10.372 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Abandon prev connection, server is *************:8848, connectionId is 1755310743089_60.191.37.34_40780
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:32:10.372 INFO  [client:] - Close current connection 1755310743089_60.191.37.34_40780
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 INFO  [naming:] - Grpc connection connect
[main-EventThread:2132]2025-08-16 10:32:10.391 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[Curator-ConnectionStateManager-0:97]2025-08-16 10:32:10.391 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 10:32:11.867 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[main-SendThread(*************:2181):2131]2025-08-16 10:32:20.869 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):2131]2025-08-16 10:32:20.869 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):2131]2025-08-16 10:32:20.878 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:65190, server: *************/*************:2181
[main-SendThread(*************:2181):2131]2025-08-16 10:32:20.886 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x10000011406318a has expired
[main-EventThread:2132]2025-08-16 10:32:20.886 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(*************:2181):2131]2025-08-16 10:32:20.886 WARN  [ClientCnxn:] - Session 0x10000011406318a for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x10000011406318a has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:2132]2025-08-16 10:32:20.886 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@64a0a1c6
[main-EventThread:2132]2025-08-16 10:32:20.888 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:2132]2025-08-16 10:32:20.889 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:2132]2025-08-16 10:32:20.889 INFO  [ConnectionStateManager:] - State change: LOST
[main-EventThread:2132]2025-08-16 10:32:20.889 INFO  [ClientCnxn:] - EventThread shut down for session: 0x10000011406318a
[Curator-ConnectionStateManager-0:97]2025-08-16 10:32:20.889 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[main-SendThread(*************:2181):2662]2025-08-16 10:32:29.921 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):2662]2025-08-16 10:32:29.921 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):2662]2025-08-16 10:32:29.933 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:49208, server: *************/*************:2181
[main-SendThread(*************:2181):2662]2025-08-16 10:32:29.945 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x10000011406318b, negotiated timeout = 40000
[main-EventThread:2663]2025-08-16 10:32:29.945 INFO  [ConnectionStateManager:] - State change: RECONNECTED
[Curator-ConnectionStateManager-0:97]2025-08-16 10:32:29.945 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: RECONNECTED
[main-EventThread:2663]2025-08-16 10:32:29.958 INFO  [EnsembleTracker:] - New config event received: {}
[NettyClientSelector_1:455]2025-08-16 10:48:20.886 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:535]2025-08-16 10:48:20.886 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:616]2025-08-16 10:48:20.889 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:616]2025-08-16 10:48:20.889 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:481]2025-08-16 10:48:20.893 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:545]2025-08-16 10:48:20.893 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[main-SendThread(*************:2181):2662]2025-08-16 10:48:20.902 WARN  [ClientCnxn:] - Session 0x10000011406318b for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x10000011406318b, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[NettyClientSelector_1:636]2025-08-16 10:48:20.902 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:413]2025-08-16 10:48:20.918 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:446]2025-08-16 10:48:20.918 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[nacos-grpc-client-executor-*************-966:3134]2025-08-16 10:48:20.922 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 12319
[nacos-grpc-client-executor-*************-966:3134]2025-08-16 10:48:20.922 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 12319
[nacos-grpc-client-executor-*************-966:3134]2025-08-16 10:48:20.922 ERROR [GrpcClient:] - [1755311523438_60.191.37.34_46457]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:48:20.922 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:48:20.922 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[NettyClientSelector_1:696]2025-08-16 10:48:20.931 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[nacos-grpc-client-executor-*************-942:3139]2025-08-16 10:48:20.961 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Receive server push request, request = ClientDetectionRequest, requestId = 10417
[nacos-grpc-client-executor-*************-942:3139]2025-08-16 10:48:20.961 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Ack server push request, request = ClientDetectionRequest, requestId = 10417
[nacos-grpc-client-executor-*************-942:3139]2025-08-16 10:48:20.961 ERROR [GrpcClient:] - [1755311523449_60.191.37.34_46468]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:48:20.961 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:48:20.961 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:48:20.995 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Success to connect a server [*************:8848], connectionId = 1755312494052_60.191.37.34_45981
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:48:20.995 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755311523438_60.191.37.34_46457
[com.alibaba.nacos.client.remote.worker.1:63]2025-08-16 10:48:20.995 INFO  [client:] - Close current connection 1755311523438_60.191.37.34_46457
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:48:20.995 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:48:20.995 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:48:20.996 INFO  [client:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:62]2025-08-16 10:48:20.996 INFO  [ClientWorker:] - [1cc6a30d-befc-45d0-b07b-cba5da8e2900_config-0] Connected,notify listen context...
[main-EventThread:2663]2025-08-16 10:48:21.003 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[Curator-ConnectionStateManager-0:97]2025-08-16 10:48:21.003 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:48:21.014 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Success to connect a server [*************:8848], connectionId = 1755312494096_60.191.37.34_46001
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:48:21.014 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Abandon prev connection, server is *************:8848, connectionId is 1755311523449_60.191.37.34_46468
[com.alibaba.nacos.client.remote.worker.1:298]2025-08-16 10:48:21.014 INFO  [client:] - Close current connection 1755311523449_60.191.37.34_46468
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 INFO  [client:] - [926c22cf-e271-4cff-86e3-d1f8b1d39d32] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 INFO  [naming:] - Grpc connection connect
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 10:48:22.228 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch
[main-SendThread(*************:2181):2662]2025-08-16 10:48:31.667 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):2662]2025-08-16 10:48:31.667 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):2662]2025-08-16 10:48:31.679 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /192.168.13.28:62861, server: *************/*************:2181
[main-SendThread(*************:2181):2662]2025-08-16 10:48:31.691 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x10000011406318b has expired
[main-EventThread:2663]2025-08-16 10:48:31.691 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(*************:2181):2662]2025-08-16 10:48:31.691 WARN  [ClientCnxn:] - Session 0x10000011406318b for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x10000011406318b has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:2663]2025-08-16 10:48:31.692 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@64a0a1c6
[main-EventThread:2663]2025-08-16 10:48:31.693 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:2663]2025-08-16 10:48:31.693 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:2663]2025-08-16 10:48:31.693 INFO  [ConnectionStateManager:] - State change: LOST
[main-EventThread:2663]2025-08-16 10:48:31.693 INFO  [ClientCnxn:] - EventThread shut down for session: 0x10000011406318b
[Curator-ConnectionStateManager-0:97]2025-08-16 10:48:31.694 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[Thread-4:55]2025-08-16 10:48:37.570 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-2:50]2025-08-16 10:48:37.570 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-4:55]2025-08-16 10:48:37.571 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[Thread-2:50]2025-08-16 10:48:37.572 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-6:61]2025-08-16 10:48:37.573 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-6:61]2025-08-16 10:48:37.573 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
[vert.x-eventloop-thread-0:335]2025-08-16 10:48:37.650 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:336]2025-08-16 10:48:37.652 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:337]2025-08-16 10:48:37.652 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-4:339]2025-08-16 10:48:37.652 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:338]2025-08-16 10:48:37.652 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-5:340]2025-08-16 10:48:37.652 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-6:341]2025-08-16 10:48:37.654 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-7:342]2025-08-16 10:48:37.662 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-8:343]2025-08-16 10:48:37.662 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:344]2025-08-16 10:48:37.668 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:345]2025-08-16 10:48:37.668 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-5:353]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-6:354]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:348]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-1:349]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-2:350]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-3:351]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-7:355]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-4:352]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:358]2025-08-16 10:48:37.669 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-8:367]2025-08-16 10:48:37.670 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:359]2025-08-16 10:48:37.670 INFO  [AbstractEventVerticle:] - Resources closed successfully
[vert.x-eventloop-thread-0:368]2025-08-16 10:48:37.670 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:47]2025-08-16 10:48:37.670 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
[SpringApplicationShutdownHook:47]2025-08-16 10:48:37.671 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[vert.x-eventloop-thread-3:362]2025-08-16 10:48:37.676 INFO  [AbstractEventVerticle:] - Resources closed successfully
[SpringApplicationShutdownHook:47]2025-08-16 10:48:37.674 INFO  [GracefulShutdown:53] - Commencing graceful shutdown. Waiting for active requests to complete
