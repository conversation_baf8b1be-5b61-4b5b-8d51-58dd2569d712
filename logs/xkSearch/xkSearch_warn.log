[main-SendThread(118.31.184.84:2181):1460]2025-08-16 09:51:02.136 WARN  [ClientCnxn:] - Session 0x100000114063188 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063188, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-118.31.184.84-161:1533]2025-08-16 09:51:02.302 ERROR [GrpcClient:] - [1755285243040_60.191.37.34_37610]Request stream onCompleted, switch server
[nacos-grpc-client-executor-118.31.184.84-188:1534]2025-08-16 09:51:02.681 ERROR [GrpcClient:] - [1755285243218_60.191.37.34_37718]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.974 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 09:51:02.975 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:03.656 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:06.679 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[main-SendThread(118.31.184.84:2181):1460]2025-08-16 09:51:12.982 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063188 has expired
[main-SendThread(118.31.184.84:2181):1460]2025-08-16 09:51:12.982 WARN  [ClientCnxn:] - Session 0x100000114063188 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063188 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:1461]2025-08-16 09:51:12.982 WARN  [ConnectionState:] - Session expired event received
[nacos-grpc-client-executor-118.31.184.84-458:2097]2025-08-16 10:19:09.916 ERROR [GrpcClient:] - [1755309055972_60.191.37.34_48205]Request stream onCompleted, switch server
[main-SendThread(118.31.184.84:2181):1573]2025-08-16 10:19:09.930 WARN  [ClientCnxn:] - Session 0x100000114063189 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x100000114063189, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-118.31.184.84-437:2095]2025-08-16 10:19:09.937 ERROR [GrpcClient:] - [1755309055894_60.191.37.34_48139]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:19:10.027 WARN  [naming:] - mark to redo completed
[main-SendThread(118.31.184.84:2181):1573]2025-08-16 10:19:20.682 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x100000114063189 has expired
[main-EventThread:1574]2025-08-16 10:19:20.682 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(118.31.184.84:2181):1573]2025-08-16 10:19:20.682 WARN  [ClientCnxn:] - Session 0x100000114063189 for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x100000114063189 has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-SendThread(118.31.184.84:2181):2131]2025-08-16 10:32:10.290 WARN  [ClientCnxn:] - Session 0x10000011406318a for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x10000011406318a, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-118.31.184.84-717:2625]2025-08-16 10:32:10.293 ERROR [GrpcClient:] - [1755310743078_60.191.37.34_40762]Request stream onCompleted, switch server
[nacos-grpc-client-executor-118.31.184.84-695:2627]2025-08-16 10:32:10.293 ERROR [GrpcClient:] - [1755310743089_60.191.37.34_40780]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:32:10.372 WARN  [naming:] - mark to redo completed
[main-SendThread(118.31.184.84:2181):2131]2025-08-16 10:32:20.886 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x10000011406318a has expired
[main-EventThread:2132]2025-08-16 10:32:20.886 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(118.31.184.84:2181):2131]2025-08-16 10:32:20.886 WARN  [ClientCnxn:] - Session 0x10000011406318a for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x10000011406318a has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-SendThread(118.31.184.84:2181):2662]2025-08-16 10:48:20.902 WARN  [ClientCnxn:] - Session 0x10000011406318b for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$EndOfStreamException: Unable to read additional data from server sessionid 0x10000011406318b, likely server has closed socket
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:77) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-118.31.184.84-966:3134]2025-08-16 10:48:20.922 ERROR [GrpcClient:] - [1755311523438_60.191.37.34_46457]Request stream onCompleted, switch server
[nacos-grpc-client-executor-118.31.184.84-942:3139]2025-08-16 10:48:20.961 ERROR [GrpcClient:] - [1755311523449_60.191.37.34_46468]Request stream onCompleted, switch server
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:297]2025-08-16 10:48:21.014 WARN  [naming:] - mark to redo completed
[main-SendThread(118.31.184.84:2181):2662]2025-08-16 10:48:31.691 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x10000011406318b has expired
[main-EventThread:2663]2025-08-16 10:48:31.691 WARN  [ConnectionState:] - Session expired event received
[main-SendThread(118.31.184.84:2181):2662]2025-08-16 10:48:31.691 WARN  [ClientCnxn:] - Session 0x10000011406318b for server 118.31.184.84/118.31.184.84:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x10000011406318b has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[Thread-4:55]2025-08-16 10:48:37.570 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Start destroying common HttpClient
[Thread-2:50]2025-08-16 10:48:37.570 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Start destroying ThreadPool
[Thread-4:55]2025-08-16 10:48:37.571 WARN  [HttpClientBeanHolder:] - [HttpClientBeanHolder] Destruction of the end
[Thread-2:50]2025-08-16 10:48:37.572 WARN  [ThreadPoolManager:] - [ThreadPoolManager] Destruction of the end
[Thread-6:61]2025-08-16 10:48:37.573 WARN  [NotifyCenter:] - [NotifyCenter] Start destroying Publisher
[Thread-6:61]2025-08-16 10:48:37.573 WARN  [NotifyCenter:] - [NotifyCenter] Destruction of the end
