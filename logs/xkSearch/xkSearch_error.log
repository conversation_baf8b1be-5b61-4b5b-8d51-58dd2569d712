[nacos-grpc-client-executor-118.31.184.84-161:1533]2025-08-16 09:51:02.302 ERROR [GrpcClient:] - [1755285243040_60.191.37.34_37610]Request stream onCompleted, switch server
[nacos-grpc-client-executor-118.31.184.84-188:1534]2025-08-16 09:51:02.681 ERROR [GrpcClient:] - [1755285243218_60.191.37.34_37718]Request stream onCompleted, switch server
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:03.656 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
[com.alibaba.nacos.client.naming.grpc.redo.0:296]2025-08-16 09:51:06.679 ERROR [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkSearch failed. 
com.alibaba.nacos.api.exception.NacosException: token expired!
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:449) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doRegisterService(NamingGrpcClientProxy.java:250) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.processRegisterRedoType(RedoScheduledTask.java:102) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstance(RedoScheduledTask.java:79) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.redoForInstances(RedoScheduledTask.java:61) ~[nacos-client-2.4.3.jar:?]
	at com.alibaba.nacos.client.naming.remote.gprc.redo.RedoScheduledTask.run(RedoScheduledTask.java:51) ~[nacos-client-2.4.3.jar:?]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[?:?]
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:358) ~[?:?]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.base/java.lang.Thread.run(Thread.java:1583) [?:?]
