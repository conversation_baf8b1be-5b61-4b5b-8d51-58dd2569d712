[main:1]2025-08-21 11:26:29.875 DEBUG [Loggers:] - Using Slf4j logging framework
[main:1]2025-08-21 11:26:29.877 DEBUG [Hooks:] - Enabling stacktrace debugging via onOperatorDebug
[main:1]2025-08-21 11:26:30.588 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 11:26:30.588 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 11:26:30.588 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-21 11:26:30.589 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-21 11:26:30.589 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-21 11:26:30.590 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[background-preinit:44]2025-08-21 11:26:30.603 DEBUG [logging:] - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[background-preinit:44]2025-08-21 11:26:30.608 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[background-preinit:44]2025-08-21 11:26:30.609 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[background-preinit:44]2025-08-21 11:26:30.610 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[background-preinit:44]2025-08-21 11:26:30.610 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[background-preinit:44]2025-08-21 11:26:30.615 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[background-preinit:44]2025-08-21 11:26:30.642 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[background-preinit:44]2025-08-21 11:26:30.703 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[background-preinit:44]2025-08-21 11:26:30.707 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
[background-preinit:44]2025-08-21 11:26:30.707 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[background-preinit:44]2025-08-21 11:26:30.707 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[background-preinit:44]2025-08-21 11:26:30.707 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[background-preinit:44]2025-08-21 11:26:30.707 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 11:26:32.858 INFO  [XkGoodsServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-21 11:26:32.860 DEBUG [SpringApplication:685] - Loading source class com.xk.goods.server.XkGoodsServer,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration
[main:1]2025-08-21 11:26:32.871 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 11:26:32.871 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP] content: 
jobs:
    
[main:1]2025-08-21 11:26:32.871 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-21 11:26:32.871 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkGoods-dev.yml, group=DEFAULT_GROUP] content: 
configuration:
    webClient:
        loadBalanced:
            maxInMemorySize: 5120
            connectTimeoutMillis: 30000
            responseTimeout: 30
            user: admin
            password: 6RirvS
    validation: 
        key: 
            admin: 'Kth1HURxA5mWcGpBYcUg6GgXV0hATl6u'
            corp: 'ykkqhUH6b6WAdm4ipfusTAEZ0cnuBBtv'
            ios: 'XtPp4XG9NanpAGGp5WIaMYv0lmVMjrXb'
            android: 'HCgN0RwmXKVFlIKaDGi3tAfS4moSqURb'
            harmony: 'ngZ1E0yVv1gyZLuw7D6XiiupOzN1nInL'
        expiresTime: 60
        status: false
    settings:
        test:
            sig1: "RPBHwTNdRvymgnC5kEWS1EDHE7x06BaC"
        session:
            timeout: 28800
        user:
            nickname: "用户"
        os.bucket.cos.10:
            id: 1331099099
            name: "haoshang-test-1331099099"
            domain: "http://files.xmjihaoyun.cn"
            region: "ap-shanghai"
        os.bucket.oss.10:
            id: 1098742611924356
            name: "xka-test"
            domain: "https://files.xmjihaoyun.cn"
            region: "cn-hangzhou"
    queue:
        disruptor:
            maxDrainAttemptsBeforeShutdown: 200
            sleepMillisBetweenDrainAttempts: 50 
            ringBufferSize: 1024 
            timeout: 5000
            strategy: "TIMEOUT"
            sleepTimeNs: 10
            retries: 200
            waitTimeout: 10
            timeUnit: MILLISECONDS
            notifyProgressThreshold: 2048
    vertx:
        vertx:
            #eventLoopPoolSize: 8
            maxEventLoopExecuteTime: 3000000000
        eventbus:
        deployment:
                threadingModel: VIRTUAL_THREAD
                #blockPoolSize: 8
                instances: 1
    ncs:
        zookeeper: 
            connectionString: "*************:2181"
    scheduling:
        quartz:
            startupDelay:5
    os:
        cos:
            secretId: IKIDujOsAFH6tTr21oQq46vcItL2fBXkojU6
            secretKey: a1MXuGTWTK3c4LSNAk9MPIBxYyAY9yhH
            appId: 1331099099
            regionStr: ap-shanghai
        oss:
            secretId: LTAI5tEpzBC6KSKdcSTtHY2R
            secretKey: ******************************
            appId: 1098742611924356
            roleArn: acs:ram::1098742611924356:role/ramossdev1
            regionStr: cn-hangzhou
    jms: 
        rocketmq-producer:
            namesrvAddr: *************:9876
        rocketmq-consumer:
            namesrvAddr: *************:9876
        zmq-producer:
            nameServer: "MYDATA_IM_1:127.0.0.1:18100,MYDATA_IM_2:127.0.0.1:18200"
            zmqConnectUrl: "tcp://%s:%d"
            zmqRequestFormat: "requ|%s|%s"
            sendTimeout: 1000
            receiveTimeout: 1000
            reload: false
    http:
        defaultHttpClient: 
            connectTimeout: 5000 
            socketTimeout: 5000 
            connTimeToLive: 3
            retry: 2
            busiRetry: 2
    redis:
        zmqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2
            jmxEnabled: false
            maxTotal: 10000
        seqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3
            jmxEnabled: false
            maxTotal: 10000
        tableRedisClient: 
            connectionString: redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379,redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
        busiRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379
            jmxEnabled: false
            maxTotal: 10000
    jdbc:
        xk_config: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: **********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_goods: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_auth: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
        xk_stock: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
            initialSize: 1
            asyncInit: true
            # 异步初始化变量
            initVariants: true
            # 异步初始化全局变量
            initGlobalVariants: true
    sharding:
        datasource.mapping:
            xk_log: 
                module: log
                dataSourceKeys: xk_log
                startId: 0
            xk_config: 
                module: config
                dataSourceKeys: xk_config
                startId: 0
            xk_goods: 
                module: goods
                dataSourceKeys: xk_goods
                startId: 0
            xk_auth: 
                module: auth
                dataSourceKeys: xk_auth
                startId: 0
            xk_stock:
                module: stock
                dataSourceKeys: xk_stock
                startId: 0    
        module.mapping:
            log: 
                tableRule: c_.*,log_.*
            config: 
                tableRule: t_.*
            goods: 
                tableRule: g_.*
            auth: 
                tableRule: auth_.*
            stock:
                tableRule: s_.*
[main:1]2025-08-21 11:26:32.872 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:674] - Refreshing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@152e7703
[main:1]2025-08-21 11:26:34.023 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.024 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorDeleteEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.024 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.color.ColorUpdateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.025 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.corp.CorpCreateEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.025 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.026 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportDeleteListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.026 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.gift.GiftReportUpdateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.027 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.CreateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.027 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.DeleteGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.027 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.GoodsStockEmptyListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.028 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownEventListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.028 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.028 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductDownListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.030 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductFirstListingListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.030 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.MerchantProductRemainRandomListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.031 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateGoodsListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.031 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateRemainStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.032 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.goods.UpdateStockListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.033 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.order.OrderPaidListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.033 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScoreListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.034 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.QueryScorePagerListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.036 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.score.UpdateScoreRuleListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.037 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.CreateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.037 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.DeleteSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.037 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.series.UpdateSeriesCateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.038 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.CreateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.038 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.DeleteTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.039 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.goods.server.listener.team.UpdateTeamMemberListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-21 11:26:34.640 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 11:26:34.655 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:34.655 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:34.656 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-21 11:26:34.706 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 11:26:34.706 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.goods.gateway.config.XkGoodsServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/goods/gateway/config/XkGoodsServiceConfig.class]]
[main:1]2025-08-21 11:26:34.711 DEBUG [EncryptResourceManager:] - Registering class: com.xk.goods.infrastructure.data.po.random.GDistributionItemPO
[main:1]2025-08-21 11:26:34.711 DEBUG [EncryptScannerRegistrar:] - Registered specified classes: [com.xk.goods.infrastructure.data.po.random.GDistributionItemPO]
[main:1]2025-08-21 11:26:34.725 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-21 11:26:34.725 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-21 11:26:34.726 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 11:26:34.730 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 11:26:35.251 DEBUG [LogFactory:] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[main:1]2025-08-21 11:26:35.260 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-21 11:26:35.283 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.283 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.283 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-21 11:26:35.283 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.283 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.283 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.298 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.321 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],goods-routing: [xk_goods, 0 - 9223372036854775807],auth-routing: [xk_auth, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807],stock-routing: [xk_stock, 0 - 9223372036854775807] }
[main:1]2025-08-21 11:26:35.321 DEBUG [ShardingNodeExecutor:] - Routing-Config Holder initialization is complete.
[main:1]2025-08-21 11:26:35.321 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-21 11:26:35.470 INFO  [GenericScope:] - BeanFactory id=2e62ea10-36c6-3108-956c-d9a46c3a22d6
[main:1]2025-08-21 11:26:35.890 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:35.894 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-goods\xk-goods-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250818.091612-116.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250819.023657-99.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250819.023657-90.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250819.023657-80.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250819.023657-92.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250819.023657-93.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250819.023657-86.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250819.023657-93.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250819.023657-89.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250819.023657-87.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.1138\tencentcloud-sdk-java-common-3.1.1138.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\commons-logging\commons-logging\1.3.4\commons-logging-1.3.4.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250819.023657-87.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250819.023657-86.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250818.091612-115.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250818.091612-127.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250818.091612-122.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250818.091612-129.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250818.091612-118.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250818.091612-121.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-goods\xk-goods-application\target\classes;D:\maven\repository\com\myco\mydata\config\mydata-config-application\0.0.1-SNAPSHOT\mydata-config-application-0.0.1-20250719.075944-7.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-interfaces\0.0.1-SNAPSHOT\mydata-config-interfaces-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250819.023657-88.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250819.023657-87.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-core\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-event\target\classes;D:\code\xk\xk-goods\xk-goods-interfaces\target\classes;D:\code\xk\xk-goods\xk-goods-domain\xk-goods-domain-enum\target\classes;D:\code\xk\xk-goods\xk-goods-gateway\target\classes;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250819.084230-25.jar;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250818.092336-74.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\config\xk-config-interfaces\0.0.1-SNAPSHOT\xk-config-interfaces-0.0.1-20250805.030408-16.jar;D:\maven\repository\com\xk\config\xk-config-domain-enum\0.0.1-SNAPSHOT\xk-config-domain-enum-0.0.1-20250805.030408-16.jar;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\maven\repository\com\xk\search\xk-search-interfaces\0.0.1-SNAPSHOT\xk-search-interfaces-0.0.1-20250818.123046-199.jar;D:\maven\repository\com\xk\search\xk-search-domain-enum\0.0.1-SNAPSHOT\xk-search-domain-enum-0.0.1-20250818.123046-208.jar;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-event\target\classes;D:\code\xk\xk-order\xk-order-domain\xk-order-domain-enum\target\classes;D:\maven\repository\com\xk\corp\xk-corp-domain-event\0.0.1-SNAPSHOT\xk-corp-domain-event-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\promotion\xk-promotion-interfaces\0.0.1-SNAPSHOT\xk-promotion-interfaces-0.0.1-20250804.073817-18.jar;D:\maven\repository\com\xk\promotion\xk-promotion-domain-enum\0.0.1-SNAPSHOT\xk-promotion-domain-enum-0.0.1-20250804.073817-16.jar;D:\code\xk\xk-goods\xk-goods-infrastructure\target\classes;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.memory.free=93MB
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-21 11:26:35.932 INFO  [ZooKeeper:] - Client environment:os.memory.total=180MB
[main:1]2025-08-21 11:26:35.953 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-21 11:26:35.954 DEBUG [CuratorZookeeperClient:] - Starting
[main:1]2025-08-21 11:26:35.954 DEBUG [ConnectionState:] - Starting
[main:1]2025-08-21 11:26:35.954 DEBUG [ConnectionState:] - reset
[main:1]2025-08-21 11:26:35.955 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@55abf66a
[main:1]2025-08-21 11:26:35.958 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-21 11:26:35.961 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-21 11:26:35.965 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-21 11:26:35.972 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-21 11:26:35.972 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-21 11:26:35.972 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:35.979 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:35.990 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.012 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.030 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.036 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.128 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.195 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.ncs.zookeeper.lockTimeout.user' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type Integer
[main:1]2025-08-21 11:26:36.201 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.227 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-producer.rmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:36.230 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.236 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.271 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.307 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.313 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.318 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'liveObjectDao' of type [com.xk.infrastructure.cache.dao.object.LiveObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.323 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.326 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.331 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.338 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.347 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.348 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.readonlyRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:36.349 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.requiredRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [create*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [revise*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [sync*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [add*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [update*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [handle] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [incr*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [amend*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [save*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [persist*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [remove*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [terminate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [delete*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [insert*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [commit*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [merge*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [apply*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [initiate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [alter*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [cancel*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [mod*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [retire*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.350 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [store*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.351 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.355 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:36.356 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.358 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.358 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-21 11:26:36.358 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.359 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:36.359 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-21 11:26:36.675 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.level: simple
[main:1]2025-08-21 11:26:36.675 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.targetRecords: 4
[main:1]2025-08-21 11:26:36.746 DEBUG [GlobalEventExecutor:] - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[main:1]2025-08-21 11:26:37.940 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.zmq-producer.zmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:38.629 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.redis.tableRedisClientRef' in PropertySource 'Config resource 'class path resource [application-data.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:41.446 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:41.447 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:41.447 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:41.448 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:41.448 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:42.217 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.217 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.225 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.225 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.233 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.233 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.241 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.241 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.249 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.249 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.257 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.257 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.264 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.265 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.273 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.273 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.283 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.283 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.291 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.291 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.300 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.300 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.309 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.309 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.318 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.318 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.326 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.326 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.338 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.338 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.348 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.348 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.472 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.472 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.482 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.482 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.491 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.491 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.500 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.500 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.510 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.510 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.522 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.522 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.535 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.535 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.546 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.546 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.556 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.556 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.566 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.566 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.575 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.575 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.586 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.586 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-21 11:26:42.950 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:42.950 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:42.950 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:43.707 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-21 11:26:43.757 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[main:1]2025-08-21 11:26:43.759 DEBUG [AbstractConfigurationImpl:] - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
[main:1]2025-08-21 11:26:43.759 DEBUG [AbstractConfigurationImpl:] - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
[main:1]2025-08-21 11:26:43.761 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[main:1]2025-08-21 11:26:43.761 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via user class loader
[main:1]2025-08-21 11:26:43.761 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[main:1]2025-08-21 11:26:43.761 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[main:1]2025-08-21 11:26:43.762 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[main:1]2025-08-21 11:26:43.762 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[main:1]2025-08-21 11:26:43.763 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-21 11:26:44.734 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main-SendThread(*************:2181):86]2025-08-21 11:26:45.023 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):86]2025-08-21 11:26:45.023 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):86]2025-08-21 11:26:45.034 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:60903, server: *************/*************:2181
[main-SendThread(*************:2181):86]2025-08-21 11:26:45.047 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x10000011406337e, negotiated timeout = 40000
[main-EventThread:87]2025-08-21 11:26:45.049 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:87]2025-08-21 11:26:45.051 INFO  [ConnectionStateManager:] - State change: CONNECTED
[main-EventThread:87]2025-08-21 11:26:45.051 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 10 operations
[Curator-ConnectionStateManager-0:85]2025-08-21 11:26:45.051 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:87]2025-08-21 11:26:45.065 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 11:26:45.065 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main-EventThread:87]2025-08-21 11:26:45.065 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:87]2025-08-21 11:26:45.065 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main:1]2025-08-21 11:26:46.716 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorCreateEvent
[main:1]2025-08-21 11:26:46.758 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorDeleteEvent
[main:1]2025-08-21 11:26:46.764 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-ColorUpdateEvent
[main:1]2025-08-21 11:26:46.770 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_CORP-CORP-CorpCreateEvent
[main:1]2025-08-21 11:26:46.776 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportCreateEvent
[main:1]2025-08-21 11:26:46.781 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportDeleteEvent
[main:1]2025-08-21 11:26:46.787 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GiftReportUpdateEvent
[main:1]2025-08-21 11:26:46.793 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateGoodsEvent
[main:1]2025-08-21 11:26:46.799 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteGoodsEvent
[main:1]2025-08-21 11:26:46.807 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-GoodsStockEmptyEvent
[main:1]2025-08-21 11:26:46.813 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 11:26:46.820 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownJobEvent
[main:1]2025-08-21 11:26:46.826 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductDownEvent
[main:1]2025-08-21 11:26:46.833 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductFirstListingEvent
[main:1]2025-08-21 11:26:46.839 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-MerchantProductRemainRandomEvent
[main:1]2025-08-21 11:26:46.845 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateGoodsEvent
[main:1]2025-08-21 11:26:46.851 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateRemainStockEvent
[main:1]2025-08-21 11:26:46.856 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateStockEvent
[main:1]2025-08-21 11:26:46.862 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_ORDER-ORDER-OrderPaidEvent
[main:1]2025-08-21 11:26:46.869 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScoreEvent
[main:1]2025-08-21 11:26:46.874 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-QueryScorePagerEvent
[main:1]2025-08-21 11:26:46.879 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateScoreRuleEvent
[main:1]2025-08-21 11:26:46.886 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateSeriesCateEvent
[main:1]2025-08-21 11:26:46.892 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteSeriesCateEvent
[main:1]2025-08-21 11:26:46.898 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateSeriesCateEvent
[main:1]2025-08-21 11:26:46.904 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-CreateTeamMemberEvent
[main:1]2025-08-21 11:26:46.910 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-DeleteTeamMemberEvent
[main:1]2025-08-21 11:26:46.918 INFO  [DefaultMqConsumer:] - Consumer-Group-Name: xkGoods_YD_GOODS-GOODS-UpdateTeamMemberEvent
[main:1]2025-08-21 11:26:46.960 DEBUG [AutoConfigurationPackages:213] - @EnableAutoConfiguration was declared on a class in the package 'com.myco.mydata.server'. Automatic @Repository and @Entity scanning is enabled.
[main:1]2025-08-21 11:26:47.109 DEBUG [Mappings:] - 
	c.x.g.a.q.g.MerchantProductAppQueryServiceImpl:
	{POST /goods/merchant-product/app/query/goodsRule}: goodsRule(Mono)
	{POST /goods/merchant-product/app/query/pick/category}: searchPickCategory(Mono)
	{POST /goods/merchant-product/app/query/pick/stock}: searchPickStock(Mono)
	{POST /goods/merchant-product/app/query/buyer/detail}: searchBuyerDetail(Mono)
	{POST /goods/merchant-product/app/query/promotion/detail}: searchPromotionDetail(Mono)
[main:1]2025-08-21 11:26:47.117 DEBUG [RequestMappingHandlerMapping:164] - 5 mappings in 'requestMappingHandlerMapping'
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/color/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x000001af3cdbc890@2fb8d338
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorQueryServiceRoutingConfig$$Lambda/0x000001af3cdbd4e0@1c20e539
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/color => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000001af3cdbde28@********
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000001af3cdbe040@29b8af89
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.color.ColorServiceRoutingConfig$$Lambda/0x000001af3cdbe258@********
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/gift/report/query => {
 ((POST && /sync) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbe470@5be2a48f
 ((POST && /pre/query/current) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbe688@4cf1c5d8
 ((POST && /pre/query) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbe8a0@20d9ef28
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbeab8@64297f96
 ((POST && /search/update/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbecd0@2aa17940
 ((POST && /inner/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbeee8@68bcb6c9
 ((POST && /inner/search) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftQueryRoutingConfig$$Lambda/0x000001af3cdbf100@773b459
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/gift/report => {
 ((POST && /multi/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbf318@2809aeee
 ((POST && /lock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbf530@9c3a9d6
 ((POST && /unlock) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbf748@4445cd9
 ((POST && /next) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbf960@1e0fe174
 ((POST && /pre/create) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbfb78@7b944285
 ((POST && /multi/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdbfd90@6f0dc938
 ((POST && /single/execute) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdc0000@4909e294
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdc0218@286cae80
 ((POST && /count/reset) && Accept: application/json) -> com.xk.goods.server.endpoints.gift.GiftServiceRoutingConfig$$Lambda/0x000001af3cdc0430@71b54162
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/product-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc0648@********
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/product-category/app/query => {
 ((POST && /search/parent) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc0860@766b43bd
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/query => {
 ((POST && /material/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc0a78@3b3eeb5e
 ((POST && /mall/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc0c90@605b130a
 ((POST && /collectible/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc0ea8@1048564e
 ((POST && /merchant/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc10c0@3c928951
 ((POST && /merchant/ewd/id) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc12d8@70bae186
 ((POST && /specification/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc14f0@48c17fd8
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/materials-product/query => {
 ((POST && /app/search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc1708@1a69aaa8
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc1920@329970b5
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc1b38@150811b1
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/mall-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc1d50@********
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc1f68@11f4ffb
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/collectible-card/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc2180@78fa8bb5
 ((POST && /search/show/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc2398@2d2a8882
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc25b0@5f37597b
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/merchant-product/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc27c8@24aca5f1
 ((POST && /search/corp/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc29e0@82735f2
 ((POST && /detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc2bf8@71b0d2ec
 ((POST && /corp/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc2e10@14a9a120
 ((POST && /inner/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3028@55eb1d69
 ((POST && /status/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3240@64d23a83
 ((POST && /status/corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3458@1302b347
 ((POST && /corp/count) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3670@4a7bb0a6
 ((POST && /show/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3888@4776fcd5
 ((POST && /pay/price) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3aa0@3205c09a
 ((POST && /inner/spec/serial) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3cb8@349bc516
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/merchant-product/app/query => {
 ((POST && /promotion/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc3ed0@2372ada3
 ((POST && /buyer/detail) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc40e8@53927d4c
 ((POST && /pick/category) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc4300@4f0f76b4
 ((POST && /pick/stock) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc4518@25d6b936
 ((POST && /goodsRule) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc4730@13ff179a
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/live/query => {
 ((POST && /replay) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsQueryRoutingConfig$$Lambda/0x000001af3cdc4948@53c770b4
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/product-category => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc4b60@674916ae
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc4d78@77c60c78
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc4f90@1c6e96dd
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/materials-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc51a8@********
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc53c0@5b979374
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc55d8@1cf5f1b2
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc57f0@7445448f
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc5a08@206c8094
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/mall-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc5c20@4787abde
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc5e38@74ccbafd
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6050@2a73e9d7
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6268@6edfb42d
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6480@147c3b73
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6698@5767053
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/collectible-card => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc68b0@307e1c7b
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6ac8@2393b885
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6ce0@56c1a827
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc6ef8@6083be44
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7110@20defbeb
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7328@1eb738e5
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/merchant-product => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7540@456c2b84
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7758@38e0ccf3
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7970@6f3679a7
 ((POST && /update/status) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7b88@48e114b4
 ((POST && /update/show) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc7da0@6cf53627
 ((POST && /update/listing) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8000@52ccf81e
 ((POST && /update/listing/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8218@eeb48a1
 ((POST && /audit/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8430@4e6fa97c
 ((POST && /recycle/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8648@b6eea98
 ((POST && /copy/batch) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8860@3e57efe3
 ((POST && /update/publicity) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8a78@6a2d06c8
 ((POST && /update/remain/random) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8c90@593a76b8
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/merchant/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.goods.GoodsServiceRoutingConfig$$Lambda/0x000001af3cdc8ea8@257c7d2d
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/object => {
 ((POST && /getGoodsObject) && Accept: application/json) -> com.xk.goods.server.endpoints.object.ObjectQueryRoutingConfig$$Lambda/0x000001af3cdc90c0@24d5b696
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/random-distribution => {
 ((POST && /get-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x000001af3cdc92d8@264cef82
 ((POST && /callback-item) && Accept: application/json) -> com.xk.goods.server.endpoints.random.RandomDistributionServiceRoutingConfig$$Lambda/0x000001af3cdc94f0@2cc9c245
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/score/rule/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x000001af3cdc9708@6a1a57e4
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/score/query => {
 ((POST && /search/pager) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreQueryRoutingConfig$$Lambda/0x000001af3cdc9920@63f22d37
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/score/rule => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x000001af3cdc9b38@2203ee34
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/score => {
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.score.ScoreServiceRoutingConfig$$Lambda/0x000001af3cdc9d50@403acbdb
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/group-category/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdc9f68@78a7114f
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/group/query => {
 ((POST && /search) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdca180@ace10f9
 ((POST && /searchQuick) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdca398@37013dda
 ((POST && /searchOriginalDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdca5b0@21f34cf2
 ((POST && /searchSpecialDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdca7c8@619e1cea
 ((POST && /app/searchGroupTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdca9e0@1b2d91f8
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /app/searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcabf8@25fc90aa
 ((POST && /app/searchGiftItem) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcae10@1e53dc3b
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/item/query => {
 ((POST && /searchOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcb028@f3bea57
 ((POST && /searchOriginalTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcb240@78ee1819
 ((POST && /searchSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcb458@3956ae6e
 ((POST && /searchTeam) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcb670@39640f7b
 ((POST && /searchNum) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcb888@69dd3352
 ((POST && /downloadTemplate) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcbaa0@61fbd0a0
 ((POST && /downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcbcb8@6dd2372f
 ((POST && /corp/downloadOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcbed0@74d4e239
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/template/query => {
 ((POST && /getList) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcc0e8@********
 ((POST && /getDetail) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialQueryRoutingConfig$$Lambda/0x000001af3cdcc300@4bd2a8b0
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/group-category => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcc518@37c595aa
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcc730@64536c41
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcc948@86ea911
}
[main:1]2025-08-21 11:26:47.190 DEBUG [Mappings:] - Mapped /goods/serial/group => {
 ((POST && /saveOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
 ((POST && /updateOriginal) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccd78@4d54b03f
 ((POST && /updateForbidden) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccf90@321aecb0
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcd1a8@4fc1edc3
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcd3c0@21b44898
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/serial/item => {
 ((POST && /saveSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcd5d8@4989d1d9
 ((POST && /updateSpecial) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcd7f0@2c13db34
 ((POST && /updateSpecialShow) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcda08@6c7c8480
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/serial/template => {
 ((POST && /save) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcdc20@6f0fe11f
 ((POST && /update) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdcde38@688ff23e
 ((POST && /delete) && Accept: application/json) -> com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdce050@5af75e60
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/query/series => {
 ((POST && /category/tree) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesQueryRoutingConfig$$Lambda/0x000001af3cdce268@52a7893a
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/series => {
 ((POST && /category/save) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000001af3cdce480@42b09d68
 ((POST && /category/update) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000001af3cdce698@********
 ((POST && /category/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.series.SeriesServiceRoutingConfig$$Lambda/0x000001af3cdce8b0@35ddc99b
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/stock => {
 ((POST && /create) && Accept: application/json) -> com.xk.goods.server.endpoints.stock.StockServiceRoutingConfig$$Lambda/0x000001af3cdceac8@5b5d7843
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/query/team => {
 ((POST && /member/searchPager) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamQueryRoutingConfig$$Lambda/0x000001af3cdcece0@1dc0f59d
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /goods/team => {
 ((POST && /member/save) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000001af3cdceef8@4a80ebe
 ((POST && /member/update) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000001af3cdcf110@5fe29ea1
 ((POST && /member/delete) && Accept: application/json) -> com.xk.goods.server.endpoints.team.TeamServiceRoutingConfig$$Lambda/0x000001af3cdcf328@6fcd6a7e
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped (GET && /favicon.ico) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000001af3cde22a8@753b0607
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /server => {
 (GET && /check) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x000001af3cde24b8@31222be0
}
[main:1]2025-08-21 11:26:47.191 DEBUG [Mappings:] - Mapped /tag => {
 ((POST && /save) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001af3cde26c8@7a7ebcf3
 ((POST && /update) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001af3cde28e0@7e64a758
 ((POST && /remove) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x000001af3cde2af8@53c29dad
}
[main:1]2025-08-21 11:26:47.217 DEBUG [Mappings:] - 'resourceHandlerMapping' {/webjars/**=ResourceWebHandler [classpath [META-INF/resources/webjars/]], /**=ResourceWebHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/]]}
[main:1]2025-08-21 11:26:47.631 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 11:26:47.644 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 11:26:47.644 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 11:26:47.645 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 11:26:47.646 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 11:26:47.646 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-21 11:26:47.646 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 11:26:47.646 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@663a2b13
[main:1]2025-08-21 11:26:47.841 DEBUG [InternalLoggerFactory:] - Using SLF4J as the default logging framework
[main:1]2025-08-21 11:26:48.061 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-21 11:26:48.078 DEBUG [WebFluxEndpointHandlerMapping:164] - 34 mappings in 'webEndpointReactiveHandlerMapping'
[main:1]2025-08-21 11:26:48.137 DEBUG [ControllerMethodResolver:289] - ControllerAdvice beans: none
[main:1]2025-08-21 11:26:48.255 DEBUG [HttpWebHandlerAdapter:267] - enableLoggingRequestDetails='false': form data and headers will be masked to prevent unsafe logging of potentially sensitive data
[main:1]2025-08-21 11:26:48.411 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:48.411 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:48.411 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:48.411 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:48.411 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:48.425 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.log.errLogDataSourceName' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:48.474 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-21 11:26:48.488 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.language.exception.baseNames' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-21 11:26:48.585 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-21 11:26:48.586 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-21 11:26:48.586 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-21 11:26:48.586 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-21 11:26:48.586 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-21 11:26:48.586 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-21 11:26:48.586 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-21 11:26:48.587 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-21 11:26:48.624 INFO  [JobDetailBuilder:] - 没有服务器[192.168.13.28],需要执行的任务
[main:1]2025-08-21 11:26:48.624 INFO  [QuartzSchedulerManager:] - 共获取到【0】个需要处理的Jobs!
[main:1]2025-08-21 11:26:48.999 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-21 11:26:49.259 DEBUG [SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin:131] - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[main:1]2025-08-21 11:26:50.098 INFO  [NettyWebServer:126] - Netty started on port 11006 (http)
[main:1]2025-08-21 11:26:50.106 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-21 11:26:50.107 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-21 11:26:50.107 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-21 11:26:50.107 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-21 11:26:50.113 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-21 11:26:50.117 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-21 11:26:50.117 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-21 11:26:50.284 INFO  [client:] - [RpcClientFactory] create a new rpc client of 4c29db4f-85b1-454f-b60e-d968a024b0ce
[main:1]2025-08-21 11:26:50.285 INFO  [naming:] - Create naming rpc client for uuid->4c29db4f-85b1-454f-b60e-d968a024b0ce
[main:1]2025-08-21 11:26:50.285 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-21 11:26:50.285 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-21 11:26:50.285 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-21 11:26:50.285 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-21 11:26:50.285 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-21 11:26:50.330 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Success to connect to server [*************:8848] on start up, connectionId = 1755746809372_221.12.20.178_33070
[main:1]2025-08-21 11:26:50.330 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-21 11:26:50.330 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x000001af3c5dbba0
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:26:50.330 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:26:50.330 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-21 11:26:50.332 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkGoods with instance Instance{instanceId='null', ip='*************', port=11006, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkGoods', serviceName='null', metadata={preserved.heart.beat.timeout=20000, preserved.ip.delete.timeout=60000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=10000}}
[main:1]2025-08-21 11:26:50.373 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkGoods *************:11006 register finished
[main:1]2025-08-21 11:26:50.386 DEBUG [LoggerFactory:] - Using io.vertx.core.logging.SLF4JLogDelegateFactory
[main:1]2025-08-21 11:26:50.484 INFO  [VertxEventBusManager:] - Event bus 'CONFIG' started.
[main:1]2025-08-21 11:26:50.488 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.488 INFO  [VertxEventBusManager:] - Event queue 'CONFIG' started.
[main:1]2025-08-21 11:26:50.516 INFO  [VertxEventBusManager:] - Event bus 'ORDER' started.
[main:1]2025-08-21 11:26:50.516 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.516 INFO  [VertxEventBusManager:] - Event queue 'ORDER' started.
[main:1]2025-08-21 11:26:50.545 INFO  [VertxEventBusManager:] - Event bus 'CORP' started.
[main:1]2025-08-21 11:26:50.545 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.545 INFO  [VertxEventBusManager:] - Event queue 'CORP' started.
[main:1]2025-08-21 11:26:50.584 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-21 11:26:50.585 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.585 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-21 11:26:50.617 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-21 11:26:50.617 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.617 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-21 11:26:50.646 INFO  [VertxEventBusManager:] - Event bus 'USER' started.
[main:1]2025-08-21 11:26:50.646 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=1024, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-21 11:26:50.646 INFO  [VertxEventBusManager:] - Event queue 'USER' started.
[vert.x-virtual-thread-0:288]2025-08-21 11:26:50.667 INFO  [AbstractEventVerticle:] - Deploying 'DeleteItemDefineEventHandler-0'...
[vert.x-virtual-thread-0:288]2025-08-21 11:26:50.669 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteItemDefineEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 11:26:50.669 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler
[main:1]2025-08-21 11:26:50.669 INFO  [VertxEventBusManager:] - register event bus:CONFIG, handler:com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler
[vert.x-virtual-thread-1:294]2025-08-21 11:26:50.669 INFO  [AbstractEventVerticle:] - Deploying 'UpdateItemDefineIdEventHandler-1'...
[vert.x-virtual-thread-1:294]2025-08-21 11:26:50.669 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateItemDefineIdEvent[CONFIG[YD_CONFIG]]'
[main:1]2025-08-21 11:26:50.670 INFO  [VertxEventBusManager:] - register event bus:ORDER, handler:com.xk.goods.application.handler.event.order.OrderPaidEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:295]2025-08-21 11:26:50.670 INFO  [AbstractEventVerticle:] - Deploying 'OrderPaidEventHandler-2'...
[vert.x-virtual-thread-0:295]2025-08-21 11:26:50.670 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'OrderPaidEvent[ORDER[YD_ORDER]]'
[main:1]2025-08-21 11:26:50.670 INFO  [VertxEventBusManager:] - register event bus:CORP, handler:com.xk.goods.application.handler.event.corp.CorpCreateEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:296]2025-08-21 11:26:50.670 INFO  [AbstractEventVerticle:] - Deploying 'CorpCreateEventHandler-3'...
[main:1]2025-08-21 11:26:50.670 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-0:298]2025-08-21 11:26:50.670 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-4'...
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.color.ColorUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.goods.UpdateStockEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-2:304]2025-08-21 11:26:50.671 INFO  [AbstractEventVerticle:] - Deploying 'ColorUpdateEventHandler-5'...
[main:1]2025-08-21 11:26:50.671 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler$$SpringCGLIB$$0
[vert.x-virtual-thread-3:305]2025-08-21 11:26:50.671 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportCreateEventHandler-6'...
[vert.x-virtual-thread-0:299]2025-08-21 11:26:50.671 INFO  [AbstractEventVerticle:] - Deploying 'ColorCreateEventHandler-7'...
[vert.x-virtual-thread-1:303]2025-08-21 11:26:50.671 INFO  [AbstractEventVerticle:] - Deploying 'ColorDeleteEventHandler-8'...
[vert.x-virtual-thread-4:307]2025-08-21 11:26:50.671 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportDeleteEventHandler-9'...
[vert.x-virtual-thread-5:308]2025-08-21 11:26:50.672 INFO  [AbstractEventVerticle:] - Deploying 'GiftReportUpdateEventHandler-10'...
[vert.x-virtual-thread-6:309]2025-08-21 11:26:50.672 INFO  [AbstractEventVerticle:] - Deploying 'CreateGoodsEventHandler-11'...
[vert.x-virtual-thread-7:311]2025-08-21 11:26:50.672 INFO  [AbstractEventVerticle:] - Deploying 'DeleteGoodsEventHandler-12'...
[vert.x-virtual-thread-8:314]2025-08-21 11:26:50.672 INFO  [AbstractEventVerticle:] - Deploying 'GoodsStockEmptyEventHandler-13'...
[vert.x-virtual-thread-8:314]2025-08-21 11:26:50.672 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GoodsStockEmptyEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:298]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[vert.x-virtual-thread-7:311]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:296]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CorpCreateEvent[CORP[YD_CORP]]'
[vert.x-virtual-thread-2:304]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-5:308]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportUpdateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-6:309]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-4:307]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:303]2025-08-21 11:26:50.676 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorDeleteEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:299]2025-08-21 11:26:50.677 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ColorCreateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-3:305]2025-08-21 11:26:50.677 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'GiftReportCreateEvent[GOODS[YD_GOODS]]'
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScoreEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler$$SpringCGLIB$$0
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserCreateEventHandler
[main:1]2025-08-21 11:26:50.677 INFO  [VertxEventBusManager:] - register event bus:USER, handler:com.myco.mydata.config.application.event.user.UserDeleteEventHandler
[main:1]2025-08-21 11:26:50.677 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[main:1]2025-08-21 11:26:50.679 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-21 11:26:50.679 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:26:50.679 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[vert.x-virtual-thread-14:321]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductFirstListingEventHandler-14'...
[vert.x-virtual-thread-10:317]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateRemainStockEventHandler-17'...
[vert.x-virtual-thread-15:322]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductRemainRandomEventHandler-16'...
[vert.x-virtual-thread-12:319]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownEventHandler-15'...
[vert.x-virtual-thread-13:320]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'MerchantProductDownJobEventHandler-18'...
[vert.x-virtual-thread-11:318]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateStockEventHandler-19'...
[vert.x-virtual-thread-9:316]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateGoodsEventHandler-20'...
[vert.x-virtual-thread-16:323]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'QueryScoreEventHandler-21'...
[vert.x-virtual-thread-14:321]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductFirstListingEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-11:318]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-15:322]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductRemainRandomEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-9:316]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateGoodsEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-13:320]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownJobEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-17:324]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'QueryScorePagerEventHandler-22'...
[vert.x-virtual-thread-17:324]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScorePagerEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-18:325]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateScoreRuleEventHandler-23'...
[vert.x-virtual-thread-18:325]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateScoreRuleEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-16:323]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'QueryScoreEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:327]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'DeleteSeriesCateEventHandler-24'...
[vert.x-virtual-thread-21:328]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateSeriesCateEventHandler-25'...
[vert.x-virtual-thread-12:319]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'MerchantProductDownEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-23:330]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'DeleteTeamMemberEventHandler-27'...
[vert.x-virtual-thread-23:330]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-19:326]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'CreateSeriesCateEventHandler-26'...
[vert.x-virtual-thread-24:331]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UpdateTeamMemberEventHandler-28'...
[vert.x-virtual-thread-22:329]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'CreateTeamMemberEventHandler-29'...
[vert.x-virtual-thread-0:332]2025-08-21 11:26:50.687 INFO  [AbstractEventVerticle:] - Deploying 'UserCreateEventHandler-30'...
[vert.x-virtual-thread-19:326]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-22:329]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-0:332]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserCreateEvent[USER[YD_USER]]'
[vert.x-virtual-thread-10:317]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateRemainStockEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-24:331]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateTeamMemberEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-1:333]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deploying 'UserDeleteEventHandler-31'...
[vert.x-virtual-thread-1:333]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UserDeleteEvent[USER[YD_USER]]'
[vert.x-virtual-thread-21:328]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-virtual-thread-20:327]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeleteSeriesCateEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:336]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.UpdateItemDefineIdEventHandler' with ID: 2
[vert.x-eventloop-thread-0:336]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.dict.item.DeleteItemDefineEventHandler' with ID: 1
[vert.x-eventloop-thread-0:337]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.corp.CorpCreateEventHandler' with ID: 4
[vert.x-eventloop-thread-0:339]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.order.OrderPaidEventHandler' with ID: 3
[vert.x-eventloop-thread-0:338]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 5
[vert.x-eventloop-thread-0:340]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserCreateEventHandler' with ID: 31
[vert.x-eventloop-thread-0:340]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.myco.mydata.config.application.event.user.UserDeleteEventHandler' with ID: 32
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorDeleteEventHandler' with ID: 7
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorCreateEventHandler' with ID: 6
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportUpdateEventHandler' with ID: 11
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.color.ColorUpdateEventHandler' with ID: 8
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.DeleteGoodsEventHandler' with ID: 13
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportCreateEventHandler' with ID: 9
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.GoodsStockEmptyEventHandler' with ID: 14
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductRemainRandomEventHandler' with ID: 21
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductFirstListingEventHandler' with ID: 20
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateGoodsEventHandler' with ID: 15
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownJobEventHandler' with ID: 19
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScorePagerEventHandler' with ID: 23
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.688 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.UpdateScoreRuleEventHandler' with ID: 24
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.score.QueryScoreEventHandler' with ID: 22
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.merchant.MerchantProductDownEventHandler' with ID: 18
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.DeleteTeamMemberEventHandler' with ID: 29
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.CreateTeamMemberEventHandler' with ID: 28
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateRemainStockEventHandler' with ID: 16
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.CreateSeriesCateEventHandler' with ID: 25
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.team.UpdateTeamMemberEventHandler' with ID: 30
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.UpdateStockEventHandler' with ID: 17
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.gift.GiftReportDeleteEventHandler' with ID: 10
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.goods.CreateGoodsEventHandler' with ID: 12
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.UpdateSeriesCateEventHandler' with ID: 27
[vert.x-eventloop-thread-0:335]2025-08-21 11:26:50.690 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.goods.application.handler.event.series.DeleteSeriesCateEventHandler' with ID: 26
[main:1]2025-08-21 11:26:50.698 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:50.698 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-21 11:26:50.698 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-21 11:26:50.709 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 11:26:50.709 INFO  [ServiceApplicationListener:] -  The xkGoods:dev has been started.
[main:1]2025-08-21 11:26:50.709 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-21 11:26:50.710 INFO  [XkGoodsServer:] - Started XkGoodsServer in 20.492 seconds (process running for 21.885)
[main:1]2025-08-21 11:26:50.711 DEBUG [ApplicationAvailabilityBean:77] - Application availability state LivenessState changed to CORRECT
[main:1]2025-08-21 11:26:50.722 WARN  [DefaultStdSchedulerFactoryBean:] - 没有可用的Jobs
[main:1]2025-08-21 11:26:50.722 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-21 11:26:50.725 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkGoods-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-21 11:26:50.725 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-21 11:26:50.725 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 11:26:50.725 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkGoods-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-21 11:26:50.725 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkGoods-dev.yml, group=DEFAULT_GROUP
[main:1]2025-08-21 11:26:50.725 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
[Thread-14:121]2025-08-21 11:26:51.796 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorCreateEvent' queue.
[Thread-15:122]2025-08-21 11:26:51.831 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorDeleteEvent' queue.
[Thread-17:124]2025-08-21 11:26:51.834 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_CORP-CORP-CorpCreateEvent' queue.
[Thread-16:123]2025-08-21 11:26:51.834 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-ColorUpdateEvent' queue.
[Thread-18:125]2025-08-21 11:26:51.839 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportCreateEvent' queue.
[Thread-19:126]2025-08-21 11:26:51.850 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportDeleteEvent' queue.
[Thread-20:127]2025-08-21 11:26:51.864 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GiftReportUpdateEvent' queue.
[Thread-22:129]2025-08-21 11:26:51.877 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteGoodsEvent' queue.
[Thread-21:128]2025-08-21 11:26:51.877 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateGoodsEvent' queue.
[Thread-23:130]2025-08-21 11:26:51.893 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-GoodsStockEmptyEvent' queue.
[Thread-24:131]2025-08-21 11:26:51.893 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-26:133]2025-08-21 11:26:51.894 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownEvent' queue.
[Thread-25:132]2025-08-21 11:26:51.895 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductDownJobEvent' queue.
[Thread-27:134]2025-08-21 11:26:51.901 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductFirstListingEvent' queue.
[Thread-28:135]2025-08-21 11:26:51.919 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-MerchantProductRemainRandomEvent' queue.
[Thread-29:136]2025-08-21 11:26:51.921 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateGoodsEvent' queue.
[Thread-30:137]2025-08-21 11:26:51.921 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateRemainStockEvent' queue.
[Thread-33:140]2025-08-21 11:26:51.934 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScoreEvent' queue.
[Thread-31:138]2025-08-21 11:26:51.938 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateStockEvent' queue.
[Thread-34:141]2025-08-21 11:26:51.950 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-QueryScorePagerEvent' queue.
[Thread-32:139]2025-08-21 11:26:51.950 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_ORDER-ORDER-OrderPaidEvent' queue.
[Thread-36:143]2025-08-21 11:26:51.958 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateSeriesCateEvent' queue.
[Thread-37:144]2025-08-21 11:26:51.964 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteSeriesCateEvent' queue.
[Thread-35:142]2025-08-21 11:26:51.968 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateScoreRuleEvent' queue.
[Thread-38:145]2025-08-21 11:26:51.978 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateSeriesCateEvent' queue.
[Thread-39:146]2025-08-21 11:26:51.979 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-CreateTeamMemberEvent' queue.
[Thread-40:147]2025-08-21 11:26:51.987 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-DeleteTeamMemberEvent' queue.
[Thread-41:148]2025-08-21 11:26:51.989 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_GOODS-GOODS-UpdateTeamMemberEvent' queue.
[DefaultQuartzScheduler:341]2025-08-21 11:26:55.725 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:341]2025-08-21 11:26:55.725 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:26:55.725 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[reactor-http-nio-2:689]2025-08-21 11:27:02.574 DEBUG [HttpWebHandlerAdapter:120] - [c8e5943d-1] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-2:689]2025-08-21 11:27:02.685 DEBUG [RouterFunctionMapping:189] - [c8e5943d-1] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
[loomBoundedElastic-1:690]2025-08-21 11:27:02.728 DEBUG [Jackson2JsonDecoder:127] - [c8e5943d-1] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-1:691]2025-08-21 11:27:02.750 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-1:691]2025-08-21 11:27:02.863 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-1:691]2025-08-21 11:27:03.269 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-1:691]2025-08-21 11:27:03.304 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:691]2025-08-21 11:27:03.327 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-1:691]2025-08-21 11:27:03.438 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-1:691]2025-08-21 11:27:03.439 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:1101
[service-1:691]2025-08-21 11:27:03.510 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-1:691]2025-08-21 11:27:03.706 DEBUG [SheetUtils:] - The first is read by default.
[service-1:691]2025-08-21 11:27:03.707 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-1:691]2025-08-21 11:27:03.707 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@cd4fdd28
[service-1:691]2025-08-21 11:27:03.886 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:03.905 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:03.934 INFO  [DruidDataSource:] - {dataSource-1} inited
[service-1:691]2025-08-21 11:27:04.294 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-1:691]2025-08-21 11:27:04.309 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-1:691]2025-08-21 11:27:04.335 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-1:691]2025-08-21 11:27:04.336 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.356 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.375 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.375 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:04.376 DEBUG [insertSelective:] - ==> Parameters: 236(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 12(Long), 283(Long), 283(Long)
[service-1:691]2025-08-21 11:27:04.399 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:04.401 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.463 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.467 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.490 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.490 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "篮网队伍" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:04.491 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:04.502 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.502 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.510 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-1:691]2025-08-21 11:27:04.533 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.534 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.534 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:04.534 DEBUG [insertSelective:] - ==> Parameters: 4145(Long), 3(Integer), 236(Long), 0(Integer), 0(Long), 0(Long)
[service-1:691]2025-08-21 11:27:04.564 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:04.564 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.641 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.642 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.642 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:04.642 DEBUG [insertSelective:] - ==> Parameters: 4145(Long), 236(Long), 1(Integer), 篮网队伍(String), TEAM(String)
[service-1:691]2025-08-21 11:27:04.664 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:04.664 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.687 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.687 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.700 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.700 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "76人队伍" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:04.700 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:04.711 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.711 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.711 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-1:691]2025-08-21 11:27:04.722 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.722 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.722 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:04.723 DEBUG [insertSelective:] - ==> Parameters: 4146(Long), 3(Integer), 236(Long), 0(Integer), 0(Long), 0(Long)
[service-1:691]2025-08-21 11:27:04.744 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:04.752 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.753 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.753 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:04.753 DEBUG [insertSelective:] - ==> Parameters: 4146(Long), 236(Long), 1(Integer), 76人队伍(String), TEAM(String)
[service-1:691]2025-08-21 11:27:04.774 DEBUG [insertSelective:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:04.793 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.793 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.793 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.794 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.806 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.806 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:04.826 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.826 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.826 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.826 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.837 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.848 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.849 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.849 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.849 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.859 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.871 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.871 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.872 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.872 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.883 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.894 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.894 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.894 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.894 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.904 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.916 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.916 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.916 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.917 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.928 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.942 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.942 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.942 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.942 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.956 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.967 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.967 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.967 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.967 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:04.980 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:04.992 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:04.992 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:04.992 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:04.992 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:05.004 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.015 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.015 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.015 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-1:691]2025-08-21 11:27:05.015 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-1:691]2025-08-21 11:27:05.028 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.040 INFO  [SaveSerialOriginalItemHandler:] - Processing 10 serial original items
[service-1:691]2025-08-21 11:27:05.041 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.042 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.053 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.053 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿尔佩伦 申京" OR member_en_name = "Alperen Sengun" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.054 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.064 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.064 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.079 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.081 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.081 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.081 DEBUG [batchInsert:] - ==> Parameters: 4152(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.113 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.113 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.114 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.114 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.114 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.115 DEBUG [batchInsert:] - ==> Parameters: 4152(Long), 236(Long), 0(Long), 阿尔佩伦 申京(String), Alperen Sengun(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.137 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.137 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.137 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.137 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.137 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿尔佩伦 申京|Alperen Sengun|1 with 1 items
[service-1:691]2025-08-21 11:27:05.138 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.138 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.151 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.151 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "维金斯" OR member_en_name = "Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.152 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.163 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.163 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.163 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.164 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.164 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.164 DEBUG [batchInsert:] - ==> Parameters: 4156(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.185 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.186 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.186 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.186 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.186 DEBUG [batchInsert:] - ==> Parameters: 4156(Long), 236(Long), 0(Long), 维金斯(String), Wiggins(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.208 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.208 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.208 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.208 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 维金斯|Wiggins|1 with 1 items
[service-1:691]2025-08-21 11:27:05.208 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.208 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.220 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.220 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿隆 戈登" OR member_en_name = "Aaron Gordon" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.221 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.234 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.234 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.234 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.235 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.235 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.235 DEBUG [batchInsert:] - ==> Parameters: 4147(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.256 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.257 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.257 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.257 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.257 DEBUG [batchInsert:] - ==> Parameters: 4147(Long), 236(Long), 0(Long), 阿隆 戈登(String), Aaron Gordon(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.281 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.281 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.281 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.281 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿隆 戈登|Aaron Gordon|1 with 1 items
[service-1:691]2025-08-21 11:27:05.282 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.282 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.293 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.293 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "AJ·约翰逊" OR member_en_name = "AJ Johnson（RC）" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.293 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.305 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.305 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.305 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.306 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.306 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.306 DEBUG [batchInsert:] - ==> Parameters: 4149(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.327 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.327 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.327 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.327 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.328 DEBUG [batchInsert:] - ==> Parameters: 4149(Long), 236(Long), 0(Long), AJ·约翰逊(String), AJ Johnson（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.348 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.349 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.349 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.349 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: AJ·约翰逊|AJ Johnson（RC）|1 with 1 items
[service-1:691]2025-08-21 11:27:05.349 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.349 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.361 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.361 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "安德鲁 维金斯" OR member_en_name = "Andrew Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.361 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.372 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.372 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.373 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.373 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.373 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.373 DEBUG [batchInsert:] - ==> Parameters: 4155(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.395 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.395 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.395 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.395 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.395 DEBUG [batchInsert:] - ==> Parameters: 4155(Long), 236(Long), 0(Long), 安德鲁 维金斯(String), Andrew Wiggins(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.417 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.417 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.417 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.417 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 安德鲁 维金斯|Andrew Wiggins|1 with 1 items
[service-1:691]2025-08-21 11:27:05.417 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.417 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.430 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.430 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿德姆·博纳" OR member_en_name = "Adem Bona（RC）" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.430 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.442 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.442 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.443 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.443 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.443 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.443 DEBUG [batchInsert:] - ==> Parameters: 4148(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.464 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.464 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.465 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.465 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.465 DEBUG [batchInsert:] - ==> Parameters: 4148(Long), 236(Long), 0(Long), 阿德姆·博纳(String), Adem Bona（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.488 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.488 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.488 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.488 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿德姆·博纳|Adem Bona（RC）|1 with 1 items
[service-1:691]2025-08-21 11:27:05.488 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.489 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.501 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.501 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "亚历山大·萨尔" OR member_en_name = "Alexandre Sarr（RC）" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.501 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.512 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.512 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.513 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.513 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.513 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.513 DEBUG [batchInsert:] - ==> Parameters: 4151(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.535 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.535 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.536 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.536 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.536 DEBUG [batchInsert:] - ==> Parameters: 4151(Long), 236(Long), 0(Long), 亚历山大·萨尔(String), Alexandre Sarr（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.560 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.560 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.560 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.560 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 亚历山大·萨尔|Alexandre Sarr（RC）|1 with 1 items
[service-1:691]2025-08-21 11:27:05.560 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.560 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.572 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.572 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿门·汤普森" OR member_en_name = "Amen Thompson" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.572 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.583 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.584 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.584 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.584 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.584 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.585 DEBUG [batchInsert:] - ==> Parameters: 4154(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.606 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.607 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.607 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.607 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.607 DEBUG [batchInsert:] - ==> Parameters: 4154(Long), 236(Long), 0(Long), 阿门·汤普森(String), Amen Thompson(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.630 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.630 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.630 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.630 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿门·汤普森|Amen Thompson|1 with 1 items
[service-1:691]2025-08-21 11:27:05.630 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.631 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.642 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.642 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿玛雷 斯塔德迈尔" OR member_en_name = "Amar'e Stoudemire" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.642 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.654 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.654 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.655 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.655 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.655 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.655 DEBUG [batchInsert:] - ==> Parameters: 4153(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.679 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.679 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.679 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.679 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.679 DEBUG [batchInsert:] - ==> Parameters: 4153(Long), 236(Long), 0(Long), 阿玛雷 斯塔德迈尔(String), Amar'e Stoudemire(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.704 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.704 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.704 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.704 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿玛雷 斯塔德迈尔|Amar'e Stoudemire|1 with 1 items
[service-1:691]2025-08-21 11:27:05.704 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.705 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.717 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.717 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿杰伊·米切尔" OR member_en_name = "Ajay Mitchell（RC）" ) order by team_member_id desc LIMIT ?,?
[service-1:691]2025-08-21 11:27:05.718 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-1:691]2025-08-21 11:27:05.728 DEBUG [selectInCondition:] - <==      Total: 0
[service-1:691]2025-08-21 11:27:05.729 DEBUG [PreparedStatementPool:] - stmt enter cache
[service-1:691]2025-08-21 11:27:05.729 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.729 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.729 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.729 DEBUG [batchInsert:] - ==> Parameters: 4150(Long), 1(Integer), 236(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-1:691]2025-08-21 11:27:05.751 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.751 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-1:691]2025-08-21 11:27:05.751 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-1:691]2025-08-21 11:27:05.751 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-1:691]2025-08-21 11:27:05.751 DEBUG [batchInsert:] - ==> Parameters: 4150(Long), 236(Long), 0(Long), 阿杰伊·米切尔(String), Ajay Mitchell（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-1:691]2025-08-21 11:27:05.773 DEBUG [batchInsert:] - <==    Updates: 1
[service-1:691]2025-08-21 11:27:05.773 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-1:691]2025-08-21 11:27:05.773 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-1:691]2025-08-21 11:27:05.773 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿杰伊·米切尔|Ajay Mitchell（RC）|1 with 1 items
[service-1:691]2025-08-21 11:27:05.773 INFO  [SaveSerialOriginalItemHandler:] - Successfully processed all serial original items
[service-1:691]2025-08-21 11:27:05.774 DEBUG [ProxySynchronizationManager:] - Proxy cleaning.
[service-1:691]2025-08-21 11:27:05.844 DEBUG [Jackson2JsonEncoder:127] - [c8e5943d-1] Encoding [Root[code=1, message=success, data=null]]
[service-1:691]2025-08-21 11:27:05.850 DEBUG [HttpWebHandlerAdapter:120] - [c8e5943d-1] Completed 200 OK
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:27:17.726 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:27:21.763 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:27:47.606 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:27:47.755 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:28:10.853 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[reactor-http-nio-3:944]2025-08-21 11:28:11.262 DEBUG [HttpWebHandlerAdapter:120] - [73175f4f-2] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-3:944]2025-08-21 11:28:11.264 DEBUG [RouterFunctionMapping:189] - [73175f4f-2] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
[loomBoundedElastic-2:945]2025-08-21 11:28:11.266 DEBUG [Jackson2JsonDecoder:127] - [73175f4f-2] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-2:946]2025-08-21 11:28:11.266 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-2:946]2025-08-21 11:28:11.266 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-2:946]2025-08-21 11:28:11.389 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-2:946]2025-08-21 11:28:11.390 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-2:946]2025-08-21 11:28:11.390 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-2:946]2025-08-21 11:28:11.393 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-2:946]2025-08-21 11:28:11.394 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:1101
[service-2:946]2025-08-21 11:28:11.395 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-2:946]2025-08-21 11:28:11.400 DEBUG [SheetUtils:] - The first is read by default.
[service-2:946]2025-08-21 11:28:11.401 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-2:946]2025-08-21 11:28:11.401 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@af6d2685
[service-2:946]2025-08-21 11:28:11.415 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.415 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.441 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-2:946]2025-08-21 11:28:11.441 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-2:946]2025-08-21 11:28:11.454 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-2:946]2025-08-21 11:28:11.454 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.455 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.455 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.455 DEBUG [insertSelective:] - ==> Parameters: 237(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 12(Long), 283(Long), 283(Long)
[service-2:946]2025-08-21 11:28:11.477 DEBUG [insertSelective:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:11.478 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.479 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.490 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.490 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "篮网队伍" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:11.490 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:11.501 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.501 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-2:946]2025-08-21 11:28:11.512 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.513 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.513 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.513 DEBUG [insertSelective:] - ==> Parameters: 4157(Long), 3(Integer), 237(Long), 0(Integer), 0(Long), 0(Long)
[service-2:946]2025-08-21 11:28:11.534 DEBUG [insertSelective:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:11.545 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.545 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.545 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.545 DEBUG [insertSelective:] - ==> Parameters: 4157(Long), 237(Long), 1(Integer), 篮网队伍(String), TEAM(String)
[service-2:946]2025-08-21 11:28:11.566 DEBUG [insertSelective:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:11.585 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.586 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.597 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.597 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "76人队伍" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:11.597 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:11.609 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.610 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-2:946]2025-08-21 11:28:11.621 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.622 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.622 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.622 DEBUG [insertSelective:] - ==> Parameters: 4158(Long), 3(Integer), 237(Long), 0(Integer), 0(Long), 0(Long)
[service-2:946]2025-08-21 11:28:11.643 DEBUG [insertSelective:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:11.652 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.653 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.653 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.653 DEBUG [insertSelective:] - ==> Parameters: 4158(Long), 237(Long), 1(Integer), 76人队伍(String), TEAM(String)
[service-2:946]2025-08-21 11:28:11.674 DEBUG [insertSelective:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:11.693 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.693 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.693 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.693 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.710 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.721 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.721 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.721 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.721 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.733 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.743 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.743 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.743 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.743 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.755 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.766 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.766 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.766 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.766 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.780 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.791 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.791 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.791 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.791 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.803 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.814 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.814 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.814 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.815 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.827 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.838 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.838 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.838 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.838 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.850 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.862 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.862 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.862 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.862 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.882 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.894 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.894 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.894 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.895 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.906 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.918 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.918 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.918 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-2:946]2025-08-21 11:28:11.918 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-2:946]2025-08-21 11:28:11.931 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.941 INFO  [SaveSerialOriginalItemHandler:] - Processing 10 serial original items
[service-2:946]2025-08-21 11:28:11.941 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.942 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.954 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.954 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿尔佩伦 申京" OR member_en_name = "Alperen Sengun" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:11.954 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:11.966 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:11.966 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:11.967 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:11.967 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:11.967 DEBUG [batchInsert:] - ==> Parameters: 4164(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:11.989 DEBUG [batchInsert:] - <==    Updates: 1
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:28:18.761 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[service-2:946]2025-08-21 11:28:18.764 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.765 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.765 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.765 DEBUG [batchInsert:] - ==> Parameters: 4164(Long), 237(Long), 0(Long), 阿尔佩伦 申京(String), Alperen Sengun(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:18.788 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.788 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:18.788 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:18.788 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿尔佩伦 申京|Alperen Sengun|1 with 1 items
[service-2:946]2025-08-21 11:28:18.789 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.789 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.802 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.802 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "维金斯" OR member_en_name = "Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:18.802 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:18.813 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:18.814 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.814 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.814 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.815 DEBUG [batchInsert:] - ==> Parameters: 4168(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:18.837 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.837 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.837 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.837 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.838 DEBUG [batchInsert:] - ==> Parameters: 4168(Long), 237(Long), 0(Long), 维金斯(String), Wiggins(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:18.861 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.861 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:18.861 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:18.861 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 维金斯|Wiggins|1 with 1 items
[service-2:946]2025-08-21 11:28:18.861 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.861 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.874 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.874 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿隆 戈登" OR member_en_name = "Aaron Gordon" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:18.874 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:18.886 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:18.886 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.887 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.887 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.887 DEBUG [batchInsert:] - ==> Parameters: 4159(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:18.910 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.910 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.910 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.910 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.910 DEBUG [batchInsert:] - ==> Parameters: 4159(Long), 237(Long), 0(Long), 阿隆 戈登(String), Aaron Gordon(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:18.933 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.933 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:18.933 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:18.933 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿隆 戈登|Aaron Gordon|1 with 1 items
[service-2:946]2025-08-21 11:28:18.933 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.934 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.946 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.946 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "AJ·约翰逊" OR member_en_name = "AJ Johnson（RC）" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:18.947 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:18.966 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:18.967 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.967 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.967 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.967 DEBUG [batchInsert:] - ==> Parameters: 4161(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:18.989 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:18.989 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:18.990 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:18.990 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:18.990 DEBUG [batchInsert:] - ==> Parameters: 4161(Long), 237(Long), 0(Long), AJ·约翰逊(String), AJ Johnson（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.012 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.012 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.012 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.012 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: AJ·约翰逊|AJ Johnson（RC）|1 with 1 items
[service-2:946]2025-08-21 11:28:19.012 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.013 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.024 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.024 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "安德鲁 维金斯" OR member_en_name = "Andrew Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.024 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.037 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.037 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.038 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.038 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.038 DEBUG [batchInsert:] - ==> Parameters: 4167(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.059 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.059 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.059 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.060 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.060 DEBUG [batchInsert:] - ==> Parameters: 4167(Long), 237(Long), 0(Long), 安德鲁 维金斯(String), Andrew Wiggins(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.080 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.080 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.080 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.080 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 安德鲁 维金斯|Andrew Wiggins|1 with 1 items
[service-2:946]2025-08-21 11:28:19.080 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.080 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.092 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.092 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿德姆·博纳" OR member_en_name = "Adem Bona（RC）" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.092 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.103 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.104 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.104 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.104 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.104 DEBUG [batchInsert:] - ==> Parameters: 4160(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.124 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.125 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.125 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.125 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.125 DEBUG [batchInsert:] - ==> Parameters: 4160(Long), 237(Long), 0(Long), 阿德姆·博纳(String), Adem Bona（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.145 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.145 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.145 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.145 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿德姆·博纳|Adem Bona（RC）|1 with 1 items
[service-2:946]2025-08-21 11:28:19.146 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.146 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.157 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.157 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "亚历山大·萨尔" OR member_en_name = "Alexandre Sarr（RC）" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.158 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.168 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.169 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.170 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.170 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.170 DEBUG [batchInsert:] - ==> Parameters: 4163(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.190 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.190 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.192 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.192 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.192 DEBUG [batchInsert:] - ==> Parameters: 4163(Long), 237(Long), 0(Long), 亚历山大·萨尔(String), Alexandre Sarr（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.213 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.213 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.213 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.213 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 亚历山大·萨尔|Alexandre Sarr（RC）|1 with 1 items
[service-2:946]2025-08-21 11:28:19.213 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.214 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.225 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.225 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿门·汤普森" OR member_en_name = "Amen Thompson" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.226 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.236 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.237 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.237 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.237 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.237 DEBUG [batchInsert:] - ==> Parameters: 4166(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.258 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.258 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.259 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.259 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.259 DEBUG [batchInsert:] - ==> Parameters: 4166(Long), 237(Long), 0(Long), 阿门·汤普森(String), Amen Thompson(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.279 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.279 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.279 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.279 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿门·汤普森|Amen Thompson|1 with 1 items
[service-2:946]2025-08-21 11:28:19.279 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.281 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.292 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.292 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿玛雷 斯塔德迈尔" OR member_en_name = "Amar'e Stoudemire" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.292 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.302 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.303 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.303 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.303 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.303 DEBUG [batchInsert:] - ==> Parameters: 4165(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.324 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.324 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.325 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.325 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.325 DEBUG [batchInsert:] - ==> Parameters: 4165(Long), 237(Long), 0(Long), 阿玛雷 斯塔德迈尔(String), Amar'e Stoudemire(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.345 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.346 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.346 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.346 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿玛雷 斯塔德迈尔|Amar'e Stoudemire|1 with 1 items
[service-2:946]2025-08-21 11:28:19.346 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.346 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.358 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.358 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿杰伊·米切尔" OR member_en_name = "Ajay Mitchell（RC）" ) order by team_member_id desc LIMIT ?,?
[service-2:946]2025-08-21 11:28:19.358 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-2:946]2025-08-21 11:28:19.368 DEBUG [selectInCondition:] - <==      Total: 0
[service-2:946]2025-08-21 11:28:19.369 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.369 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.369 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.369 DEBUG [batchInsert:] - ==> Parameters: 4162(Long), 1(Integer), 237(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-2:946]2025-08-21 11:28:19.390 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.390 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-2:946]2025-08-21 11:28:19.390 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-2:946]2025-08-21 11:28:19.390 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-2:946]2025-08-21 11:28:19.390 DEBUG [batchInsert:] - ==> Parameters: 4162(Long), 237(Long), 0(Long), 阿杰伊·米切尔(String), Ajay Mitchell（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-2:946]2025-08-21 11:28:19.412 DEBUG [batchInsert:] - <==    Updates: 1
[service-2:946]2025-08-21 11:28:19.412 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-2:946]2025-08-21 11:28:19.412 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-2:946]2025-08-21 11:28:19.412 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿杰伊·米切尔|Ajay Mitchell（RC）|1 with 1 items
[service-2:946]2025-08-21 11:28:19.412 INFO  [SaveSerialOriginalItemHandler:] - Successfully processed all serial original items
[service-2:946]2025-08-21 11:28:19.413 DEBUG [ProxySynchronizationManager:] - Proxy cleaning.
[service-2:946]2025-08-21 11:28:19.454 DEBUG [Jackson2JsonEncoder:127] - [73175f4f-2] Encoding [Root[code=1, message=success, data=null]]
[service-2:946]2025-08-21 11:28:19.454 DEBUG [HttpWebHandlerAdapter:120] - [73175f4f-2] Completed 200 OK
[reactor-http-nio-4:1015]2025-08-21 11:28:40.232 DEBUG [HttpWebHandlerAdapter:120] - [35e8ffe0-3] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-4:1015]2025-08-21 11:28:40.235 DEBUG [RouterFunctionMapping:189] - [35e8ffe0-3] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
[loomBoundedElastic-3:1016]2025-08-21 11:28:40.236 DEBUG [Jackson2JsonDecoder:127] - [35e8ffe0-3] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-3:1017]2025-08-21 11:28:40.236 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-3:1017]2025-08-21 11:28:40.236 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-3:1017]2025-08-21 11:28:40.355 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-3:1017]2025-08-21 11:28:40.356 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-3:1017]2025-08-21 11:28:40.356 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-3:1017]2025-08-21 11:28:40.359 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-3:1017]2025-08-21 11:28:40.360 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:1101
[service-3:1017]2025-08-21 11:28:40.361 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-3:1017]2025-08-21 11:28:40.365 DEBUG [SheetUtils:] - The first is read by default.
[service-3:1017]2025-08-21 11:28:40.365 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-3:1017]2025-08-21 11:28:40.365 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@569c5001
[service-3:1017]2025-08-21 11:28:40.380 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.380 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.390 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-3:1017]2025-08-21 11:28:40.390 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-3:1017]2025-08-21 11:28:40.401 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-3:1017]2025-08-21 11:28:40.401 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.402 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.402 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:40.402 DEBUG [insertSelective:] - ==> Parameters: 238(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 12(Long), 283(Long), 283(Long)
[service-3:1017]2025-08-21 11:28:40.421 DEBUG [insertSelective:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:40.422 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.423 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.434 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.434 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "篮网队伍" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:40.434 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:40.444 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.444 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-3:1017]2025-08-21 11:28:40.457 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.457 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.457 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:40.458 DEBUG [insertSelective:] - ==> Parameters: 4169(Long), 3(Integer), 238(Long), 0(Integer), 0(Long), 0(Long)
[service-3:1017]2025-08-21 11:28:40.478 DEBUG [insertSelective:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:40.486 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.487 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.487 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:40.487 DEBUG [insertSelective:] - ==> Parameters: 4169(Long), 238(Long), 1(Integer), 篮网队伍(String), TEAM(String)
[service-3:1017]2025-08-21 11:28:40.507 DEBUG [insertSelective:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:40.526 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.526 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.536 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.536 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "76人队伍" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:40.537 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:40.549 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.549 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-3:1017]2025-08-21 11:28:40.559 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.560 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.560 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:40.560 DEBUG [insertSelective:] - ==> Parameters: 4170(Long), 3(Integer), 238(Long), 0(Integer), 0(Long), 0(Long)
[service-3:1017]2025-08-21 11:28:40.580 DEBUG [insertSelective:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:40.589 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.589 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.589 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:40.589 DEBUG [insertSelective:] - ==> Parameters: 4170(Long), 238(Long), 1(Integer), 76人队伍(String), TEAM(String)
[service-3:1017]2025-08-21 11:28:40.610 DEBUG [insertSelective:] - <==    Updates: 1
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:28:40.614 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[service-3:1017]2025-08-21 11:28:40.628 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.628 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.628 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.628 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.638 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.649 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.649 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.649 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.649 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.662 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.672 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.673 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.673 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.673 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.683 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.693 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.693 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.694 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.694 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.703 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.715 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.715 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.715 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.715 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.726 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.738 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.738 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.738 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.738 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.749 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.759 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.760 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.760 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.760 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.771 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.780 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.780 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.780 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.780 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.791 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.801 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.801 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.801 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.801 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.812 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.823 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.824 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.824 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-3:1017]2025-08-21 11:28:40.824 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-3:1017]2025-08-21 11:28:40.835 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:40.845 INFO  [SaveSerialOriginalItemHandler:] - Processing 10 serial original items
[service-3:1017]2025-08-21 11:28:40.845 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:40.845 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.856 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:40.856 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿尔佩伦 申京" OR member_en_name = "Alperen Sengun" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:40.856 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:40.867 DEBUG [selectInCondition:] - <==      Total: 0
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:28:49.029 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[service-3:1017]2025-08-21 11:28:49.032 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.032 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.032 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.033 DEBUG [batchInsert:] - ==> Parameters: 4176(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.052 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.052 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.052 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.053 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.053 DEBUG [batchInsert:] - ==> Parameters: 4176(Long), 238(Long), 0(Long), 阿尔佩伦 申京(String), Alperen Sengun(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.073 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.073 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.073 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.073 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿尔佩伦 申京|Alperen Sengun|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.073 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.073 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.084 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.084 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "维金斯" OR member_en_name = "Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.084 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.096 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.096 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.096 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.096 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.096 DEBUG [batchInsert:] - ==> Parameters: 4180(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.117 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.117 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.117 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.117 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.117 DEBUG [batchInsert:] - ==> Parameters: 4180(Long), 238(Long), 0(Long), 维金斯(String), Wiggins(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.137 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.138 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.138 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.138 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 维金斯|Wiggins|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.138 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.138 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.149 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.149 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿隆 戈登" OR member_en_name = "Aaron Gordon" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.149 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.161 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.161 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.161 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.161 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.162 DEBUG [batchInsert:] - ==> Parameters: 4171(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.182 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.182 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.182 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.182 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.182 DEBUG [batchInsert:] - ==> Parameters: 4171(Long), 238(Long), 0(Long), 阿隆 戈登(String), Aaron Gordon(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.203 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.203 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.203 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.203 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿隆 戈登|Aaron Gordon|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.203 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.203 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.214 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.215 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "AJ·约翰逊" OR member_en_name = "AJ Johnson（RC）" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.215 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.225 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.227 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.227 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.227 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.227 DEBUG [batchInsert:] - ==> Parameters: 4173(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.245 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.245 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.246 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.246 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.246 DEBUG [batchInsert:] - ==> Parameters: 4173(Long), 238(Long), 0(Long), AJ·约翰逊(String), AJ Johnson（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.267 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.267 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.267 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.267 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: AJ·约翰逊|AJ Johnson（RC）|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.268 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.268 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.278 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.278 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "安德鲁 维金斯" OR member_en_name = "Andrew Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.278 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.289 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.289 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.290 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.290 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.290 DEBUG [batchInsert:] - ==> Parameters: 4179(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.310 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.310 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.310 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.310 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.310 DEBUG [batchInsert:] - ==> Parameters: 4179(Long), 238(Long), 0(Long), 安德鲁 维金斯(String), Andrew Wiggins(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.334 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.335 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.335 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.335 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 安德鲁 维金斯|Andrew Wiggins|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.335 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.335 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.347 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.347 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿德姆·博纳" OR member_en_name = "Adem Bona（RC）" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.347 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.358 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.359 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.359 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.359 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.359 DEBUG [batchInsert:] - ==> Parameters: 4172(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.380 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.380 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.381 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.381 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.381 DEBUG [batchInsert:] - ==> Parameters: 4172(Long), 238(Long), 0(Long), 阿德姆·博纳(String), Adem Bona（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.401 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.401 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.401 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.401 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿德姆·博纳|Adem Bona（RC）|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.401 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.401 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.413 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.413 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "亚历山大·萨尔" OR member_en_name = "Alexandre Sarr（RC）" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.413 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.423 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.424 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.424 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.424 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.424 DEBUG [batchInsert:] - ==> Parameters: 4175(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.446 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.446 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.446 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.446 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.446 DEBUG [batchInsert:] - ==> Parameters: 4175(Long), 238(Long), 0(Long), 亚历山大·萨尔(String), Alexandre Sarr（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.468 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.468 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.468 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.468 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 亚历山大·萨尔|Alexandre Sarr（RC）|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.468 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.468 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.479 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.479 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿门·汤普森" OR member_en_name = "Amen Thompson" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.479 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.490 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.490 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.490 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.490 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.490 DEBUG [batchInsert:] - ==> Parameters: 4178(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.511 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.511 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.511 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.511 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.511 DEBUG [batchInsert:] - ==> Parameters: 4178(Long), 238(Long), 0(Long), 阿门·汤普森(String), Amen Thompson(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.533 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.533 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.533 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.533 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿门·汤普森|Amen Thompson|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.533 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.533 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.544 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.544 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿玛雷 斯塔德迈尔" OR member_en_name = "Amar'e Stoudemire" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.544 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.554 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.555 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.555 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.555 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.555 DEBUG [batchInsert:] - ==> Parameters: 4177(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.576 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.576 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.576 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.576 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.577 DEBUG [batchInsert:] - ==> Parameters: 4177(Long), 238(Long), 0(Long), 阿玛雷 斯塔德迈尔(String), Amar'e Stoudemire(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.597 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.597 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.597 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.597 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿玛雷 斯塔德迈尔|Amar'e Stoudemire|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.597 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.597 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.608 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.608 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿杰伊·米切尔" OR member_en_name = "Ajay Mitchell（RC）" ) order by team_member_id desc LIMIT ?,?
[service-3:1017]2025-08-21 11:28:49.608 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-3:1017]2025-08-21 11:28:49.619 DEBUG [selectInCondition:] - <==      Total: 0
[service-3:1017]2025-08-21 11:28:49.619 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.620 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.620 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.620 DEBUG [batchInsert:] - ==> Parameters: 4174(Long), 1(Integer), 238(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-3:1017]2025-08-21 11:28:49.640 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.640 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-3:1017]2025-08-21 11:28:49.640 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-3:1017]2025-08-21 11:28:49.641 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-3:1017]2025-08-21 11:28:49.641 DEBUG [batchInsert:] - ==> Parameters: 4174(Long), 238(Long), 0(Long), 阿杰伊·米切尔(String), Ajay Mitchell（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-3:1017]2025-08-21 11:28:49.662 DEBUG [batchInsert:] - <==    Updates: 1
[service-3:1017]2025-08-21 11:28:49.662 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-3:1017]2025-08-21 11:28:49.662 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-3:1017]2025-08-21 11:28:49.662 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿杰伊·米切尔|Ajay Mitchell（RC）|1 with 1 items
[service-3:1017]2025-08-21 11:28:49.662 INFO  [SaveSerialOriginalItemHandler:] - Successfully processed all serial original items
[service-3:1017]2025-08-21 11:28:49.662 DEBUG [ProxySynchronizationManager:] - Proxy cleaning.
[service-3:1017]2025-08-21 11:28:49.702 DEBUG [Jackson2JsonEncoder:127] - [35e8ffe0-3] Encoding [Root[code=1, message=success, data=null]]
[service-3:1017]2025-08-21 11:28:49.702 DEBUG [HttpWebHandlerAdapter:120] - [35e8ffe0-3] Completed 200 OK
[reactor-http-nio-4:1015]2025-08-21 11:29:01.527 DEBUG [HttpWebHandlerAdapter:120] - [35e8ffe0-4] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-4:1015]2025-08-21 11:29:01.529 DEBUG [RouterFunctionMapping:189] - [35e8ffe0-4] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
[loomBoundedElastic-4:1062]2025-08-21 11:29:01.530 DEBUG [Jackson2JsonDecoder:127] - [35e8ffe0-4] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-4:1063]2025-08-21 11:29:01.530 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-4:1063]2025-08-21 11:29:01.530 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-4:1063]2025-08-21 11:29:01.673 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-4:1063]2025-08-21 11:29:01.674 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-4:1063]2025-08-21 11:29:01.674 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-4:1063]2025-08-21 11:29:01.677 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-4:1063]2025-08-21 11:29:01.678 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:1101
[service-4:1063]2025-08-21 11:29:01.679 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-4:1063]2025-08-21 11:29:01.686 DEBUG [SheetUtils:] - The first is read by default.
[service-4:1063]2025-08-21 11:29:01.686 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-4:1063]2025-08-21 11:29:01.686 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@1a002704
[service-4:1063]2025-08-21 11:29:01.701 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.701 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.712 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-4:1063]2025-08-21 11:29:01.712 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-4:1063]2025-08-21 11:29:01.723 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-4:1063]2025-08-21 11:29:01.723 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.723 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.723 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:01.724 DEBUG [insertSelective:] - ==> Parameters: 239(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 12(Long), 283(Long), 283(Long)
[service-4:1063]2025-08-21 11:29:01.744 DEBUG [insertSelective:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:01.745 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.745 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.756 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.756 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "篮网队伍" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:01.756 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:01.766 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:01.767 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-4:1063]2025-08-21 11:29:01.777 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.777 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.777 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:01.777 DEBUG [insertSelective:] - ==> Parameters: 4181(Long), 3(Integer), 239(Long), 0(Integer), 0(Long), 0(Long)
[service-4:1063]2025-08-21 11:29:01.797 DEBUG [insertSelective:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:01.804 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.806 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.806 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:01.806 DEBUG [insertSelective:] - ==> Parameters: 4181(Long), 239(Long), 1(Integer), 篮网队伍(String), TEAM(String)
[service-4:1063]2025-08-21 11:29:01.826 DEBUG [insertSelective:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:01.842 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.842 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.853 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.853 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "76人队伍" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:01.853 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:01.863 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:01.864 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-4:1063]2025-08-21 11:29:01.874 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.874 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.874 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:01.874 DEBUG [insertSelective:] - ==> Parameters: 4182(Long), 3(Integer), 239(Long), 0(Integer), 0(Long), 0(Long)
[service-4:1063]2025-08-21 11:29:01.893 DEBUG [insertSelective:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:01.901 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.901 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.901 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:01.901 DEBUG [insertSelective:] - ==> Parameters: 4182(Long), 239(Long), 1(Integer), 76人队伍(String), TEAM(String)
[service-4:1063]2025-08-21 11:29:01.921 DEBUG [insertSelective:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:01.937 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.937 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.938 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:01.938 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:01.949 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:01.960 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.960 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.960 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:01.960 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:01.971 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:01.981 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:01.982 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:01.982 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:01.982 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:01.993 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.003 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.004 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.004 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.004 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.015 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.025 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.026 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.026 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.026 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.036 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.046 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.046 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.047 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.047 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.058 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.069 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.069 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.069 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.069 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.079 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.090 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.090 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.090 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.090 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.101 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.112 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.112 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.112 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.112 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.123 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.133 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.134 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.134 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-4:1063]2025-08-21 11:29:02.134 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-4:1063]2025-08-21 11:29:02.144 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:02.154 INFO  [SaveSerialOriginalItemHandler:] - Processing 10 serial original items
[service-4:1063]2025-08-21 11:29:02.154 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:02.155 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.166 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:02.166 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿尔佩伦 申京" OR member_en_name = "Alperen Sengun" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:02.166 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:02.177 DEBUG [selectInCondition:] - <==      Total: 0
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:29:07.999 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[service-4:1063]2025-08-21 11:29:08.002 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.003 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.003 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.003 DEBUG [batchInsert:] - ==> Parameters: 4188(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.023 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.023 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.023 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.023 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.023 DEBUG [batchInsert:] - ==> Parameters: 4188(Long), 239(Long), 0(Long), 阿尔佩伦 申京(String), Alperen Sengun(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.045 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.045 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.045 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.045 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿尔佩伦 申京|Alperen Sengun|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.045 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.045 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.056 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.056 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "维金斯" OR member_en_name = "Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.056 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.066 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.067 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.067 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.067 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.067 DEBUG [batchInsert:] - ==> Parameters: 4192(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.088 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.088 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.088 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.088 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.088 DEBUG [batchInsert:] - ==> Parameters: 4192(Long), 239(Long), 0(Long), 维金斯(String), Wiggins(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.110 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.110 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.110 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.110 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 维金斯|Wiggins|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.110 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.110 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.121 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.121 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿隆 戈登" OR member_en_name = "Aaron Gordon" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.121 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.132 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.133 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.133 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.133 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.133 DEBUG [batchInsert:] - ==> Parameters: 4183(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.153 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.153 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.154 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.154 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.154 DEBUG [batchInsert:] - ==> Parameters: 4183(Long), 239(Long), 0(Long), 阿隆 戈登(String), Aaron Gordon(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.175 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.175 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.175 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.175 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿隆 戈登|Aaron Gordon|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.175 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.175 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.186 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.186 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "AJ·约翰逊" OR member_en_name = "AJ Johnson（RC）" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.186 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.197 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.198 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.198 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.198 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.198 DEBUG [batchInsert:] - ==> Parameters: 4185(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.226 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.226 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.226 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.226 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.226 DEBUG [batchInsert:] - ==> Parameters: 4185(Long), 239(Long), 0(Long), AJ·约翰逊(String), AJ Johnson（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.247 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.247 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.247 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.247 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: AJ·约翰逊|AJ Johnson（RC）|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.247 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.247 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.259 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.259 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "安德鲁 维金斯" OR member_en_name = "Andrew Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.259 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.269 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.270 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.270 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.270 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.270 DEBUG [batchInsert:] - ==> Parameters: 4191(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.292 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.292 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.292 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.292 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.292 DEBUG [batchInsert:] - ==> Parameters: 4191(Long), 239(Long), 0(Long), 安德鲁 维金斯(String), Andrew Wiggins(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.314 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.314 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.314 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.314 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 安德鲁 维金斯|Andrew Wiggins|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.314 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.315 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.326 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.326 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿德姆·博纳" OR member_en_name = "Adem Bona（RC）" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.326 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.336 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.337 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.337 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.337 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.337 DEBUG [batchInsert:] - ==> Parameters: 4184(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.358 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.358 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.358 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.358 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.358 DEBUG [batchInsert:] - ==> Parameters: 4184(Long), 239(Long), 0(Long), 阿德姆·博纳(String), Adem Bona（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.377 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.377 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.377 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.377 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿德姆·博纳|Adem Bona（RC）|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.377 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.378 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.388 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.388 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "亚历山大·萨尔" OR member_en_name = "Alexandre Sarr（RC）" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.389 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.399 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.399 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.400 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.400 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.400 DEBUG [batchInsert:] - ==> Parameters: 4187(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.421 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.421 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.421 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.421 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.421 DEBUG [batchInsert:] - ==> Parameters: 4187(Long), 239(Long), 0(Long), 亚历山大·萨尔(String), Alexandre Sarr（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.441 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.441 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.441 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.441 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 亚历山大·萨尔|Alexandre Sarr（RC）|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.441 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.442 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.454 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.454 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿门·汤普森" OR member_en_name = "Amen Thompson" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.454 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.465 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.466 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.466 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.466 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.466 DEBUG [batchInsert:] - ==> Parameters: 4190(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.488 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.488 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.489 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.489 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.489 DEBUG [batchInsert:] - ==> Parameters: 4190(Long), 239(Long), 0(Long), 阿门·汤普森(String), Amen Thompson(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.509 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.509 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.509 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.509 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿门·汤普森|Amen Thompson|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.509 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.510 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.521 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.521 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿玛雷 斯塔德迈尔" OR member_en_name = "Amar'e Stoudemire" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.521 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.532 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.532 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.532 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.532 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.533 DEBUG [batchInsert:] - ==> Parameters: 4189(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.555 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.556 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.556 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.556 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.556 DEBUG [batchInsert:] - ==> Parameters: 4189(Long), 239(Long), 0(Long), 阿玛雷 斯塔德迈尔(String), Amar'e Stoudemire(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.576 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.576 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.576 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.576 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿玛雷 斯塔德迈尔|Amar'e Stoudemire|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.576 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.577 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.589 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.589 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿杰伊·米切尔" OR member_en_name = "Ajay Mitchell（RC）" ) order by team_member_id desc LIMIT ?,?
[service-4:1063]2025-08-21 11:29:08.589 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-4:1063]2025-08-21 11:29:08.600 DEBUG [selectInCondition:] - <==      Total: 0
[service-4:1063]2025-08-21 11:29:08.601 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.601 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.601 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.601 DEBUG [batchInsert:] - ==> Parameters: 4186(Long), 1(Integer), 239(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-4:1063]2025-08-21 11:29:08.621 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.622 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-4:1063]2025-08-21 11:29:08.622 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-4:1063]2025-08-21 11:29:08.622 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-4:1063]2025-08-21 11:29:08.622 DEBUG [batchInsert:] - ==> Parameters: 4186(Long), 239(Long), 0(Long), 阿杰伊·米切尔(String), Ajay Mitchell（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-4:1063]2025-08-21 11:29:08.643 DEBUG [batchInsert:] - <==    Updates: 1
[service-4:1063]2025-08-21 11:29:08.643 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-4:1063]2025-08-21 11:29:08.644 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-4:1063]2025-08-21 11:29:08.644 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿杰伊·米切尔|Ajay Mitchell（RC）|1 with 1 items
[service-4:1063]2025-08-21 11:29:08.644 INFO  [SaveSerialOriginalItemHandler:] - Successfully processed all serial original items
[service-4:1063]2025-08-21 11:29:08.644 DEBUG [ProxySynchronizationManager:] - Proxy cleaning.
[service-4:1063]2025-08-21 11:29:08.684 DEBUG [Jackson2JsonEncoder:127] - [35e8ffe0-4] Encoding [Root[code=1, message=success, data=null]]
[service-4:1063]2025-08-21 11:29:08.686 DEBUG [HttpWebHandlerAdapter:120] - [35e8ffe0-4] Completed 200 OK
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:29:17.226 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:29:33.647 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:29:42.269 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[reactor-http-nio-5:1099]2025-08-21 11:29:46.553 DEBUG [HttpWebHandlerAdapter:120] - [04f2d0c6-5] HTTP POST "/goods/serial/group/saveOriginal?sessionId&groupType&blockType&serialGroupCategoryId&name&excelBase64&teamType"
[reactor-http-nio-5:1099]2025-08-21 11:29:46.554 DEBUG [RouterFunctionMapping:189] - [04f2d0c6-5] Mapped to com.xk.goods.server.endpoints.serial.SerialServiceRoutingConfig$$Lambda/0x000001af3cdccb60@56a70784
[loomBoundedElastic-5:1100]2025-08-21 11:29:46.555 DEBUG [Jackson2JsonDecoder:127] - [04f2d0c6-5] Decoded [SerialGroupSaveOriginalReqDto(groupType=1, blockType=1, name=批量插入测试, serialGroupCategoryId=29, excel (truncated)...]
[service-5:1101]2025-08-21 11:29:46.556 DEBUG [AOProxyAspect:] - The Service is called proxies!
[service-5:1101]2025-08-21 11:29:46.556 DEBUG [ProxySynchronizationManager:] - Initializing proxy synchronization.
[service-5:1101]2025-08-21 11:29:46.691 DEBUG [SelectorRootServiceImpl:] - userObj<283> cache has been hit.
[service-5:1101]2025-08-21 11:29:46.692 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-5:1101]2025-08-21 11:29:46.692 DEBUG [AnalysisContextImpl:] - Initialization 'AnalysisContextImpl' complete
[service-5:1101]2025-08-21 11:29:46.695 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /xl/_rels/workbook.xml.rels
[service-5:1101]2025-08-21 11:29:46.696 DEBUG [SimpleReadCacheSelector:] - Use map cache.size:1101
[service-5:1101]2025-08-21 11:29:46.696 DEBUG [PackageRelationshipCollection:] - Parsing relationship: /_rels/.rels
[service-5:1101]2025-08-21 11:29:46.701 DEBUG [SheetUtils:] - The first is read by default.
[service-5:1101]2025-08-21 11:29:46.702 DEBUG [ExcelHeadProperty:] - The initialization sheet/table 'ExcelHeadProperty' is complete , head kind is CLASS
[service-5:1101]2025-08-21 11:29:46.702 DEBUG [AnalysisContextImpl:] - Began to read：com.alibaba.excel.read.metadata.holder.xlsx.XlsxReadSheetHolder@58319fe4
[service-5:1101]2025-08-21 11:29:46.715 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.715 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.725 DEBUG [selectByPrimaryKey:] - ==>  Preparing: select serial_group_category_id,name,group_type, block_type,is_show,status, deleted,update_id,create_id, update_time,create_time from g_serial_group_category where serial_group_category_id = ?
[service-5:1101]2025-08-21 11:29:46.725 DEBUG [selectByPrimaryKey:] - ==> Parameters: 29(Long)
[service-5:1101]2025-08-21 11:29:46.737 DEBUG [selectByPrimaryKey:] - <==      Total: 1
[service-5:1101]2025-08-21 11:29:46.737 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.738 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.738 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_group ( serial_group_id, serial_group_category_id, name, group_type, block_type, team_type, category_group_name, serial_item_num, update_id, create_id ) values ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:29:46.738 DEBUG [insertSelective:] - ==> Parameters: 240(Long), 29(Long), 批量插入测试(String), 1(Integer), 1(Integer), 1(Integer), 一级-3;批量插入测试(String), 12(Long), 283(Long), 283(Long)
[service-5:1101]2025-08-21 11:29:46.758 DEBUG [insertSelective:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:29:46.758 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.759 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.770 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.770 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "篮网队伍" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:29:46.770 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:29:46.780 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:46.781 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-5:1101]2025-08-21 11:29:46.792 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.792 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.792 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:29:46.792 DEBUG [insertSelective:] - ==> Parameters: 4193(Long), 3(Integer), 240(Long), 0(Integer), 0(Long), 0(Long)
[service-5:1101]2025-08-21 11:29:46.812 DEBUG [insertSelective:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:29:46.821 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.822 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.822 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:29:46.822 DEBUG [insertSelective:] - ==> Parameters: 4193(Long), 240(Long), 1(Integer), 篮网队伍(String), TEAM(String)
[service-5:1101]2025-08-21 11:29:46.842 DEBUG [insertSelective:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:29:46.860 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.860 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.872 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.872 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "76人队伍" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:29:46.872 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:29:46.883 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:46.883 DEBUG [SaveSerialTeamHandler:] - No teamMemberId found, saving without resource query
[service-5:1101]2025-08-21 11:29:46.893 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.893 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.893 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id ) values ( ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:29:46.893 DEBUG [insertSelective:] - ==> Parameters: 4194(Long), 3(Integer), 240(Long), 0(Integer), 0(Long), 0(Long)
[service-5:1101]2025-08-21 11:29:46.912 DEBUG [insertSelective:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:29:46.920 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.920 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.920 DEBUG [insertSelective:] - ==>  Preparing: insert into g_serial_team ( serial_item_id, serial_group_id, team_type, team_name, color ) values ( ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:29:46.922 DEBUG [insertSelective:] - ==> Parameters: 4194(Long), 240(Long), 1(Integer), 76人队伍(String), TEAM(String)
[service-5:1101]2025-08-21 11:29:46.942 DEBUG [insertSelective:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:29:46.961 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.961 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.961 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:46.961 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:46.972 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:46.983 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:46.983 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:46.983 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:46.983 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:46.993 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.011 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.011 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.011 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.011 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.021 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.030 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.030 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.030 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.030 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.043 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.054 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.054 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.054 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.054 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.065 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.077 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.077 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.077 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.077 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.087 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.097 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.098 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.098 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.098 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.109 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.135 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.135 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.135 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.135 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.145 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.158 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.158 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.158 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.158 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.169 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.179 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:29:47.179 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:29:47.179 DEBUG [selectByCategoryName:] - ==>  Preparing: select series_category_id ,team_type,category_name, parent_id,busi_type,sort, status,create_id,create_time, update_id,update_time from g_series_category where category_name = ?
[service-5:1101]2025-08-21 11:29:47.179 DEBUG [selectByCategoryName:] - ==> Parameters: null
[service-5:1101]2025-08-21 11:29:47.190 DEBUG [selectByCategoryName:] - <==      Total: 0
[service-5:1101]2025-08-21 11:29:47.201 INFO  [SaveSerialOriginalItemHandler:] - Processing 10 serial original items
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:30:13.255 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[main-SendThread(*************:2181):86]2025-08-21 11:30:13.256 WARN  [ClientCnxn:] - Client session timed out, have not heard from server in 38580ms for session id 0x10000011406337e
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:30:13.256 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[main-SendThread(*************:2181):86]2025-08-21 11:30:13.258 WARN  [ClientCnxn:] - Session 0x10000011406337e for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionTimeoutException: Client session timed out, have not heard from server in 38580ms for session id 0x10000011406337e
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1261) ~[zookeeper-3.6.4.jar:3.6.4]
[nacos-grpc-client-executor-*************-70:1103]2025-08-21 11:30:13.260 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Receive server push request, request = ClientDetectionRequest, requestId = 10932
[nacos-grpc-client-executor-*************-86:1105]2025-08-21 11:30:13.260 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 12849
[nacos-grpc-client-executor-*************-70:1103]2025-08-21 11:30:13.260 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Ack server push request, request = ClientDetectionRequest, requestId = 10932
[nacos-grpc-client-executor-*************-86:1105]2025-08-21 11:30:13.260 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 12849
[nacos-grpc-client-executor-*************-86:1105]2025-08-21 11:30:13.262 ERROR [GrpcClient:] - [1755746790970_221.12.20.178_31190]Request stream onCompleted, switch server
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:30:45.051 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:30:45.052 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[main-EventThread:87]2025-08-21 11:30:45.055 INFO  [ConnectionStateManager:] - State change: SUSPENDED
[service-5:1101]2025-08-21 11:30:45.056 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[nacos-grpc-client-executor-*************-71:1111]2025-08-21 11:30:45.056 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Receive server push request, request = ClientDetectionRequest, requestId = 10933
[Curator-ConnectionStateManager-0:85]2025-08-21 11:30:45.056 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: SUSPENDED
[nacos-grpc-client-executor-*************-71:1111]2025-08-21 11:30:45.056 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Ack server push request, request = ClientDetectionRequest, requestId = 10933
[service-5:1101]2025-08-21 11:30:45.056 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[nacos-grpc-client-executor-*************-71:1111]2025-08-21 11:30:45.056 ERROR [GrpcClient:] - [1755746809372_221.12.20.178_33070]Request stream onCompleted, switch server
[NettyClientSelector_1:621]2025-08-21 11:30:45.060 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:98]2025-08-21 11:30:45.060 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:168]2025-08-21 11:30:45.060 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:540]2025-08-21 11:30:45.060 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:360]2025-08-21 11:30:45.060 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:576]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:419]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:438]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:488]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:524]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:488]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:495]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:507]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:458]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:495]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:468]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:558]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:429]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:516]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:476]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:446]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:401]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:446]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:516]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:368]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:352]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:368]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:550]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:591]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:610]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:601]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:410]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:591]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:410]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:601]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:393]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:377]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:393]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:377]2025-08-21 11:30:45.062 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[service-5:1101]2025-08-21 11:30:45.066 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.066 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿尔佩伦 申京" OR member_en_name = "Alperen Sengun" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.066 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[NettyClientSelector_1:352]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:540]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:458]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:429]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:507]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:550]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:610]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:476]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[NettyClientSelector_1:524]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:9876] result: true
[NettyClientSelector_1:360]2025-08-21 11:30:45.070 INFO  [RocketmqRemoting:] - closeChannel: close the connection to remote address[*************:10911] result: true
[service-5:1101]2025-08-21 11:30:45.076 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.076 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.077 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.077 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.077 DEBUG [batchInsert:] - ==> Parameters: 4200(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.094 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Server healthy check fail, currentConnection = 1755746790970_221.12.20.178_31190
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.094 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Server healthy check fail, currentConnection = 1755746809372_221.12.20.178_33070
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.094 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.094 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.094 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.094 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[service-5:1101]2025-08-21 11:30:45.097 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.097 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.098 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.098 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.098 DEBUG [batchInsert:] - ==> Parameters: 4200(Long), 240(Long), 0(Long), 阿尔佩伦 申京(String), Alperen Sengun(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.121 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.121 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.121 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.121 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿尔佩伦 申京|Alperen Sengun|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.121 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.122 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.132 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.132 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "维金斯" OR member_en_name = "Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.132 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.142 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.142 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.143 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.143 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.143 DEBUG [batchInsert:] - ==> Parameters: 4204(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.157 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Success to connect a server [*************:8848], connectionId = 1755747044191_221.12.20.178_53647
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.158 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Abandon prev connection, server is *************:8848, connectionId is 1755746809372_221.12.20.178_33070
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.158 INFO  [client:] - Close current connection 1755746809372_221.12.20.178_33070
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.160 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.160 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.160 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.160 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.161 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.161 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.161 INFO  [naming:] - Grpc connection connect
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.163 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Success to connect a server [*************:8848], connectionId = 1755747044202_221.12.20.178_53649
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.163 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755746790970_221.12.20.178_31190
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.163 INFO  [client:] - Close current connection 1755746790970_221.12.20.178_31190
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.163 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.163 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.163 INFO  [ClientWorker:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.163 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.163 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.163 INFO  [ClientWorker:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Connected,notify listen context...
[service-5:1101]2025-08-21 11:30:45.164 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.165 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.165 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.165 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.165 DEBUG [batchInsert:] - ==> Parameters: 4204(Long), 240(Long), 0(Long), 维金斯(String), Wiggins(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.185 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.185 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.185 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.185 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 维金斯|Wiggins|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.185 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.185 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.195 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.195 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿隆 戈登" OR member_en_name = "Aaron Gordon" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.196 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.204 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.205 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.205 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.205 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.205 DEBUG [batchInsert:] - ==> Parameters: 4195(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.216 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Success to connect a server [*************:8848], connectionId = 1755747044253_221.12.20.178_53675
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.216 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Abandon prev connection, server is *************:8848, connectionId is 1755747044202_221.12.20.178_53649
[com.alibaba.nacos.client.remote.worker.1:58]2025-08-21 11:30:45.216 INFO  [client:] - Close current connection 1755747044202_221.12.20.178_53649
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.216 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.216 INFO  [ClientWorker:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] DisConnected,clear listen context...
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.216 INFO  [client:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:57]2025-08-21 11:30:45.216 INFO  [ClientWorker:] - [e796fca8-c9a5-491b-aa61-5484d3fe6528_config-0] Connected,notify listen context...
[nacos-grpc-client-executor-*************-108:1220]2025-08-21 11:30:45.217 WARN  [GrpcClient:] - [1755747044202_221.12.20.178_53649]Ignore error event,isRunning:true,isAbandon=true
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.220 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Success to connect a server [*************:8848], connectionId = 1755747044253_221.12.20.178_53674
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.220 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Abandon prev connection, server is *************:8848, connectionId is 1755747044191_221.12.20.178_53647
[com.alibaba.nacos.client.remote.worker.1:256]2025-08-21 11:30:45.220 INFO  [client:] - Close current connection 1755747044191_221.12.20.178_53647
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.220 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Notify disconnected event to listeners
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.220 WARN  [naming:] - Grpc connection disconnect, mark to redo
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.220 WARN  [naming:] - mark to redo completed
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.220 INFO  [client:] - [4c29db4f-85b1-454f-b60e-d968a024b0ce] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:255]2025-08-21 11:30:45.220 INFO  [naming:] - Grpc connection connect
[nacos-grpc-client-executor-*************-92:1223]2025-08-21 11:30:45.221 WARN  [GrpcClient:] - [1755747044191_221.12.20.178_53647]Ignore error event,isRunning:true,isAbandon=true
[service-5:1101]2025-08-21 11:30:45.225 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.225 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.225 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.225 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.225 DEBUG [batchInsert:] - ==> Parameters: 4195(Long), 240(Long), 0(Long), 阿隆 戈登(String), Aaron Gordon(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.245 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.246 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.246 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.246 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿隆 戈登|Aaron Gordon|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.246 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.246 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.257 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.257 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "AJ·约翰逊" OR member_en_name = "AJ Johnson（RC）" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.257 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.268 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.268 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.268 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.268 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.269 DEBUG [batchInsert:] - ==> Parameters: 4197(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.289 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.289 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.289 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.289 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.289 DEBUG [batchInsert:] - ==> Parameters: 4197(Long), 240(Long), 0(Long), AJ·约翰逊(String), AJ Johnson（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.310 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.310 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.310 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.310 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: AJ·约翰逊|AJ Johnson（RC）|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.310 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.310 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.321 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.321 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "安德鲁 维金斯" OR member_en_name = "Andrew Wiggins" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.322 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.332 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.333 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.333 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.333 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.333 DEBUG [batchInsert:] - ==> Parameters: 4203(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.354 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.354 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.354 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.354 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.354 DEBUG [batchInsert:] - ==> Parameters: 4203(Long), 240(Long), 0(Long), 安德鲁 维金斯(String), Andrew Wiggins(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.374 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.374 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.374 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.374 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 安德鲁 维金斯|Andrew Wiggins|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.375 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.375 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.385 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.385 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿德姆·博纳" OR member_en_name = "Adem Bona（RC）" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.386 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.396 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.397 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.398 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.398 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.398 DEBUG [batchInsert:] - ==> Parameters: 4196(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.420 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.420 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.420 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.420 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.420 DEBUG [batchInsert:] - ==> Parameters: 4196(Long), 240(Long), 0(Long), 阿德姆·博纳(String), Adem Bona（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.440 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.440 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.440 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.440 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿德姆·博纳|Adem Bona（RC）|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.440 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.440 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.451 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.451 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "亚历山大·萨尔" OR member_en_name = "Alexandre Sarr（RC）" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.452 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.461 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.461 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.462 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.462 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.462 DEBUG [batchInsert:] - ==> Parameters: 4199(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.482 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.482 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.482 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.482 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.482 DEBUG [batchInsert:] - ==> Parameters: 4199(Long), 240(Long), 0(Long), 亚历山大·萨尔(String), Alexandre Sarr（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.504 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.505 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.505 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.505 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 亚历山大·萨尔|Alexandre Sarr（RC）|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.505 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.505 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.516 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.516 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿门·汤普森" OR member_en_name = "Amen Thompson" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.516 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.526 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.527 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.527 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.527 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.527 DEBUG [batchInsert:] - ==> Parameters: 4202(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.547 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.547 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.548 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.548 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.548 DEBUG [batchInsert:] - ==> Parameters: 4202(Long), 240(Long), 0(Long), 阿门·汤普森(String), Amen Thompson(String), null, null, null, 无限编(String), LIGHT(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.568 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.568 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.568 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.568 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿门·汤普森|Amen Thompson|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.569 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.569 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.580 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.580 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿玛雷 斯塔德迈尔" OR member_en_name = "Amar'e Stoudemire" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.580 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.590 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.591 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.591 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.591 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.591 DEBUG [batchInsert:] - ==> Parameters: 4201(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.611 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.611 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.611 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.611 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.612 DEBUG [batchInsert:] - ==> Parameters: 4201(Long), 240(Long), 0(Long), 阿玛雷 斯塔德迈尔(String), Amar'e Stoudemire(String), null, null, null, 无限编(String), null, null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.638 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.638 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.638 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.638 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿玛雷 斯塔德迈尔|Amar'e Stoudemire|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.638 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.639 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.649 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.649 DEBUG [selectInCondition:] - ==>  Preparing: select team_member_id ,member_type,member_cn_name, member_en_name,create_id,create_time, update_time,update_id from g_team_member WHERE member_type = ? AND ( member_cn_name = "阿杰伊·米切尔" OR member_en_name = "Ajay Mitchell（RC）" ) order by team_member_id desc LIMIT ?,?
[service-5:1101]2025-08-21 11:30:45.649 DEBUG [selectInCondition:] - ==> Parameters: 1(Integer), 0(Integer), 20(Integer)
[service-5:1101]2025-08-21 11:30:45.660 DEBUG [selectInCondition:] - <==      Total: 0
[service-5:1101]2025-08-21 11:30:45.661 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.661 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.661 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_item ( serial_item_id, serial_item_type, serial_group_id, status, update_id, create_id, update_time, create_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.661 DEBUG [batchInsert:] - ==> Parameters: 4198(Long), 1(Integer), 240(Long), 0(Integer), 283(Long), 283(Long), null, null
[service-5:1101]2025-08-21 11:30:45.681 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.681 DEBUG [ShardingContextExecutor:] - Didn't find ShardingStrategy.
[service-5:1101]2025-08-21 11:30:45.681 DEBUG [ModulusRoutingDataSource:] - The dataSource is redirected to xk_goods
[service-5:1101]2025-08-21 11:30:45.682 DEBUG [batchInsert:] - ==>  Preparing: INSERT INTO g_serial_original_item ( serial_item_id, serial_group_id, series_category_id, member_cn_name, member_en_name, team_name, card_type, card_type_no, limit_edition, color, team_member_id, member_pic_addr, member_avatar_addr, deleted, update_id, create_id, update_time, create_time, team_type ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
[service-5:1101]2025-08-21 11:30:45.682 DEBUG [batchInsert:] - ==> Parameters: 4198(Long), 240(Long), 0(Long), 阿杰伊·米切尔(String), Ajay Mitchell（RC）(String), null, null, null, 无限编(String), RC(String), null, null, null, null, 283(Long), 283(Long), null, null, 1(Integer)
[service-5:1101]2025-08-21 11:30:45.705 DEBUG [batchInsert:] - <==    Updates: 1
[service-5:1101]2025-08-21 11:30:45.705 DEBUG [SaveSerialOriginalItemHandler:] - Batch saved 1 items
[service-5:1101]2025-08-21 11:30:45.705 INFO  [SaveSerialOriginalItemHandler:] - Successfully saved 1 items
[service-5:1101]2025-08-21 11:30:45.705 DEBUG [SaveSerialOriginalItemHandler:] - Processed group: 阿杰伊·米切尔|Ajay Mitchell（RC）|1 with 1 items
[service-5:1101]2025-08-21 11:30:45.705 INFO  [SaveSerialOriginalItemHandler:] - Successfully processed all serial original items
[service-5:1101]2025-08-21 11:30:45.706 DEBUG [ProxySynchronizationManager:] - Proxy cleaning.
[service-5:1101]2025-08-21 11:30:45.745 DEBUG [Jackson2JsonEncoder:127] - [04f2d0c6-5] Encoding [Root[code=1, message=success, data=null]]
[service-5:1101]2025-08-21 11:30:45.745 DEBUG [HttpWebHandlerAdapter:120] - [04f2d0c6-5] Completed 200 OK
[com.alibaba.nacos.client.naming.grpc.redo.0:254]2025-08-21 11:30:48.054 INFO  [naming:] - Redo instance operation REGISTER for DEFAULT_GROUP@@xkGoods
[main-SendThread(*************:2181):86]2025-08-21 11:30:55.941 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):86]2025-08-21 11:30:55.941 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):86]2025-08-21 11:30:55.950 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:54457, server: *************/*************:2181
[main-SendThread(*************:2181):86]2025-08-21 11:30:55.961 WARN  [ClientCnxn:] - Unable to reconnect to ZooKeeper service, session 0x10000011406337e has expired
[main-EventThread:87]2025-08-21 11:30:55.961 WARN  [ConnectionState:] - Session expired event received
[main-EventThread:87]2025-08-21 11:30:55.961 DEBUG [ConnectionState:] - reset
[main-SendThread(*************:2181):86]2025-08-21 11:30:55.961 WARN  [ClientCnxn:] - Session 0x10000011406337e for server *************/*************:2181, Closing socket connection. Attempting reconnect except it is a SessionExpiredException.
org.apache.zookeeper.ClientCnxn$SessionExpiredException: Unable to reconnect to ZooKeeper service, session 0x10000011406337e has expired
	at org.apache.zookeeper.ClientCnxn$SendThread.onConnected(ClientCnxn.java:1438) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocket.readConnectResult(ClientCnxnSocket.java:154) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doIO(ClientCnxnSocketNIO.java:86) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxnSocketNIO.doTransport(ClientCnxnSocketNIO.java:350) ~[zookeeper-3.6.4.jar:3.6.4]
	at org.apache.zookeeper.ClientCnxn$SendThread.run(ClientCnxn.java:1293) ~[zookeeper-3.6.4.jar:3.6.4]
[main-EventThread:87]2025-08-21 11:30:55.961 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@55abf66a
[main-EventThread:87]2025-08-21 11:30:55.962 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main-EventThread:87]2025-08-21 11:30:55.962 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main-EventThread:87]2025-08-21 11:30:55.962 INFO  [ConnectionStateManager:] - State change: LOST
[Curator-ConnectionStateManager-0:85]2025-08-21 11:30:55.962 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: LOST
[main-EventThread:87]2025-08-21 11:30:55.962 INFO  [ClientCnxn:] - EventThread shut down for session: 0x10000011406337e
[main-SendThread(*************:2181):1232]2025-08-21 11:31:05.014 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):1232]2025-08-21 11:31:05.014 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):1232]2025-08-21 11:31:05.032 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:57598, server: *************/*************:2181
[main-SendThread(*************:2181):1232]2025-08-21 11:31:05.047 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x100000114063381, negotiated timeout = 40000
[main-EventThread:1233]2025-08-21 11:31:05.047 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:1233]2025-08-21 11:31:05.047 INFO  [ConnectionStateManager:] - State change: RECONNECTED
[main-EventThread:1233]2025-08-21 11:31:05.047 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 0 operations
[Curator-ConnectionStateManager-0:85]2025-08-21 11:31:05.047 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: RECONNECTED
[main-EventThread:1233]2025-08-21 11:31:05.061 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:1233]2025-08-21 11:31:05.061 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[quartzScheduler_QuartzSchedulerThread:162]2025-08-21 11:31:10.322 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:180]2025-08-21 11:31:13.713 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
