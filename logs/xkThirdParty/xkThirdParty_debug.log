[main:1]2025-08-18 16:26:23.927 DEBUG [Loggers:] - Using Slf4j logging framework
[main:1]2025-08-18 16:26:23.929 DEBUG [Hooks:] - Enabling stacktrace debugging via onOperatorDebug
[main:1]2025-08-18 16:26:24.570 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-18 16:26:24.571 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-18 16:26:24.571 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
[main:1]2025-08-18 16:26:24.572 WARN  [NacosLogging:] - Build Nacos Logging Adapter failed: ch/qos/logback/classic/spi/LoggerContextListener
[main:1]2025-08-18 16:26:24.572 INFO  [NacosLogging:] - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
[main:1]2025-08-18 16:26:24.572 INFO  [NacosLogging:] - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.log4j2.Log4J2NacosLoggingAdapter match org.apache.logging.slf4j.Log4jLogger success.
[background-preinit:44]2025-08-18 16:26:24.585 DEBUG [logging:] - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
[background-preinit:44]2025-08-18 16:26:24.590 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[background-preinit:44]2025-08-18 16:26:24.591 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[background-preinit:44]2025-08-18 16:26:24.591 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[background-preinit:44]2025-08-18 16:26:24.591 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[background-preinit:44]2025-08-18 16:26:24.594 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[background-preinit:44]2025-08-18 16:26:24.623 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[background-preinit:44]2025-08-18 16:26:24.678 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[background-preinit:44]2025-08-18 16:26:24.681 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator as ValidatorFactory-scoped message interpolator.
[background-preinit:44]2025-08-18 16:26:24.681 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[background-preinit:44]2025-08-18 16:26:24.681 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[background-preinit:44]2025-08-18 16:26:24.681 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[background-preinit:44]2025-08-18 16:26:24.681 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-18 16:26:26.416 INFO  [XkThirdPartyServer:] - The following 10 profiles are active: "commons", "data", "jms", "cache", "http", "schedule", "proxy", "os", "server", "dev"
[main:1]2025-08-18 16:26:26.417 DEBUG [SpringApplication:685] - Loading source class com.xk.tp.server.XkThirdPartyServer,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration
[main:1]2025-08-18 16:26:26.426 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-18 16:26:26.426 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP] content: 
jobs:
    -   description: "系统消息发送"
        jobName: sendSystemMessageJob
        jobType: stateful
        jobBean: sendSystemMessageJob
        jobPool: custom
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0 0 * * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize: 1
        maxPoolSize: 10
        keepAliveTime: 
        cacheQueueCapacity:
        serverIp: ***********
        serverIpBak: ***********
    -   description: "系统消息发送"
        jobName: bufferMessageJob
        jobType: stateful
        jobBean: bufferMessageJob
        jobPool: none
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0/1 * * * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize:
        maxPoolSize:
        keepAliveTime: 
        cacheQueueCapacity:
        serverIp: ***********
        serverIpBak: ***********
    -   description: "对账数据同步"
        jobName: syncReconciledSchedule
        jobType: stateful
        jobBean: syncReconciledSchedule
        jobPool: none
        triggerType: cron
        repeatCount: 
        repeatInterval: 
        cronExpression: "0 0 0 * * ?"
        status: ACTIVE
        priority: 
        delay: 
        minPoolSize:
        maxPoolSize:
        keepAliveTime: 
        cacheQueueCapacity:  
[main:1]2025-08-18 16:26:26.426 INFO  [NacosConfigDataLoader:] - [Nacos Config] Load config[dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP] success
[main:1]2025-08-18 16:26:26.426 DEBUG [NacosConfigDataLoader:] - [Nacos Config] config[dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP] content: 
configuration:
    webClient:
        loadBalanced:
            maxInMemorySize: 5120
            connectTimeoutMillis: 30000
            responseTimeout: 30
            user: admin
            password: 6RirvS
    validation: 
        key: 
            admin: 'Kth1HURxA5mWcGpBYcUg6GgXV0hATl6u'
            corp: 'ykkqhUH6b6WAdm4ipfusTAEZ0cnuBBtv'
            ios: 'XtPp4XG9NanpAGGp5WIaMYv0lmVMjrXb'
            android: 'HCgN0RwmXKVFlIKaDGi3tAfS4moSqURb'
            harmony: 'ngZ1E0yVv1gyZLuw7D6XiiupOzN1nInL'
        expiresTime: 60
        status: false
    settings:
        test:
            sig1: "RPBHwTNdRvymgnC5kEWS1EDHE7x06BaC"
        session:
            timeout: 28800
        user:
            nickname: "用户"
        os.bucket.cos.10:
            id: 1331099099
            name: "haoshang-test-1331099099"
            domain: "http://files.xmjihaoyun.cn"
            region: "ap-shanghai"
        os.bucket.oss.10:
            id: 1098742611924356
            name: "xka-test"
            domain: "https://files.xmjihaoyun.cn"
            region: "cn-hangzhou"
        sms:
            test:
                status: true
    queue:
        disruptor:
            maxDrainAttemptsBeforeShutdown: 200
            sleepMillisBetweenDrainAttempts: 50 
            ringBufferSize: 131072 
            timeout: 5000
            strategy: "TIMEOUT"
            sleepTimeNs: 10
            retries: 200
            waitTimeout: 10
            timeUnit: MILLISECONDS
            notifyProgressThreshold: 2048
    vertx:
        vertx:
            #eventLoopPoolSize: 8
            maxEventLoopExecuteTime: 3000000000
        eventbus:
        deployment:
                threadingModel: EVENT_LOOP
                #blockPoolSize: 8
                instances: 1
    ncs:
        zookeeper: 
            connectionString: "*************:2181"
    scheduling:
        quartz:
            startupDelay:5
    os:
        cos:
            secretId: IKIDujOsAFH6tTr21oQq46vcItL2fBXkojU6
            secretKey: a1MXuGTWTK3c4LSNAk9MPIBxYyAY9yhH
            appId: 1331099099
            regionStr: ap-shanghai
        oss:
            secretId: LTAI5tEpzBC6KSKdcSTtHY2R
            secretKey: ******************************
            appId: 1098742611924356
            roleArn: acs:ram::1098742611924356:role/ramossdev1
            regionStr: cn-hangzhou
    jms: 
        rocketmq-producer:
            namesrvAddr: *************:9876
        rocketmq-consumer:
            namesrvAddr: *************:9876
        zmq-producer:
            nameServer: "MYDATA_IM_1:127.0.0.1:18100,MYDATA_IM_2:127.0.0.1:18200"
            zmqConnectUrl: "tcp://%s:%d"
            zmqRequestFormat: "requ|%s|%s"
            sendTimeout: 1000
            receiveTimeout: 1000
            reload: false
    http:
        defaultHttpClient: 
            connectTimeout: 5000 
            socketTimeout: 5000 
            connTimeToLive: 3
            retry: 2
            busiRetry: 2
    redis:
        zmqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/2
            jmxEnabled: false
        seqRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379/3
            jmxEnabled: false
        tableRedisClient: 
            connectionString: redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379,redis://r-bp1xlg9yl6i5wu6e1y:<EMAIL>:6379
            jmxEnabled: false
        busiRedisClient: 
            connectionString: redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379,redis://r-bp1qx0nv1cxy51ytjl:<EMAIL>:6379
            jmxEnabled: false
    jdbc:
        xk_log: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: *********************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
        xk_config: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ************************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
        xk_tp: 
            driverClassName: com.mysql.cj.jdbc.Driver
            url: ********************************************************************************************************************************************************************************************************
            username: root
            password: A1b@C3d1tR8
    sharding:
        datasource.mapping:
            xk_log: 
                module: log
                dataSourceKeys: xk_log
                startId: 0
            xk_config: 
                module: config
                dataSourceKeys: xk_config
                startId: 0
            xk_tp: 
                module: tp
                dataSourceKeys: xk_tp
                startId: 0
        module.mapping:
            log: 
                tableRule: c_.*,log_.*
            config: 
                tableRule: t_.*
            tp: 
                tableRule: tp_.*
[main:1]2025-08-18 16:26:26.426 DEBUG [AnnotationConfigReactiveWebServerApplicationContext:674] - Refreshing org.springframework.boot.web.reactive.context.AnnotationConfigReactiveWebServerApplicationContext@41bd6a0f
[main:1]2025-08-18 16:26:27.418 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.auth.AuthFinishListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.418 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.auth.SyncNotifyStatusListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.419 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.log.CreateUseLogListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.420 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.message.ShortMessageCreateListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.420 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.pay.PayFinishListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.420 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.push.PushAppMessageListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.421 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.reconciled.HuiFuMerchantConfigJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.421 INFO  [ConsumerListenerRegistrar:] - registry consumer listener bean 'com.xk.tp.server.listener.reconciled.SyncReconciledJobListener#ConsumerListenerRegistrar#DefaultMqConsumer#0'
[main:1]2025-08-18 16:26:27.911 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-18 16:26:27.945 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:27.946 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:27.947 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-sender.aclEnable' in PropertySource 'Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/'' with value of type Boolean
[main:1]2025-08-18 16:26:28.004 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'userObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.tp.gateway.config.XkThirdPartyServiceConfig; factoryMethodName=userObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/tp/gateway/config/XkThirdPartyServiceConfig.class]]
[main:1]2025-08-18 16:26:28.004 INFO  [DefaultListableBeanFactory:] - Overriding bean definition for bean 'corpObjectQueryService' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.gateway.config.XkGatewayConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/gateway/config/XkGatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.xk.tp.gateway.config.XkThirdPartyServiceConfig; factoryMethodName=corpObjectQueryService; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/xk/tp/gateway/config/XkThirdPartyServiceConfig.class]]
[main:1]2025-08-18 16:26:28.016 INFO  [CacheData:] - config listener notify warn timeout millis use default 60000 millis 
[main:1]2025-08-18 16:26:28.016 INFO  [CacheData:] - nacos.cache.data.init.snapshot = true 
[main:1]2025-08-18 16:26:28.017 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkThirdParty-dev.yml+DEFAULT_GROUP+dev
[main:1]2025-08-18 16:26:28.022 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-18 16:26:28.396 DEBUG [LogFactory:] - Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[main:1]2025-08-18 16:26:28.404 WARN  [ClassPathMapperScanner:] - No MyBatis mapper was found in '[com.myco.mydata.infrastructure.data.persistence]' package. Please check your configuration.
[main:1]2025-08-18 16:26:28.424 WARN  [AbstractUnifiedConfigurer:] - Node[webClient] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.424 WARN  [AbstractUnifiedConfigurer:] - Node[validation] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.430 INFO  [SystemParamTableHolder:] - System settings initializing.
[main:1]2025-08-18 16:26:28.430 WARN  [AbstractUnifiedConfigurer:] - Node[settings] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.430 WARN  [AbstractUnifiedConfigurer:] - Node[queue] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.431 WARN  [AbstractUnifiedConfigurer:] - Node[vertx] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.452 WARN  [AbstractUnifiedConfigurer:] - Node[http] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.475 INFO  [RoutingConfigHolder:] - {log-routing: [xk_log, 0 - 9223372036854775807],tp-routing: [xk_tp, 0 - 9223372036854775807],config-routing: [xk_config, 0 - 9223372036854775807] }
[main:1]2025-08-18 16:26:28.475 DEBUG [ShardingNodeExecutor:] - Routing-Config Holder initialization is complete.
[main:1]2025-08-18 16:26:28.475 WARN  [AbstractUnifiedConfigurer:] - Node[sharding] BeanDefinitionHolder is empty!
[main:1]2025-08-18 16:26:28.612 INFO  [GenericScope:] - BeanFactory id=49198c24-9eab-3e0d-a23f-1c14b2c34eaf
[main:1]2025-08-18 16:26:28.964 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.transaction.TransactionConfig' of type [com.myco.framework.support.transaction.TransactionConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:28.968 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.commons.config.CommonsStartConfig' of type [com.myco.mydata.infrastructure.commons.config.CommonsStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [beansOfTypeToMapPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:zookeeper.version=3.6.4--d65253dcf68e9097c6e95a126463fd5fdeb4521c, built on 12/18/2022 18:10 GMT
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:host.name=*************
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.version=21.0.7
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.vendor=Oracle Corporation
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.home=C:\Program Files\Java\jdk-21
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.class.path=D:\code\xk\xk-third-party\xk-third-party-server\target\classes;D:\maven\repository\com\xk\xk-start-server\0.0.1-SNAPSHOT\xk-start-server-0.0.1-20250813.093748-113.jar;D:\maven\repository\com\myco\mydata\mydata-start-server\0.0.1-SNAPSHOT\mydata-start-server-0.0.1-20250807.120835-80.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-core\0.0.1-SNAPSHOT\mydata-start-domain-core-0.0.1-20250807.115026-89.jar;D:\maven\repository\com\myco\mydata\mydata-start-commons\0.0.1-SNAPSHOT\mydata-start-commons-0.0.1-20250814.122549-80.jar;D:\maven\repository\com\myco\myco-framework-6\0.0.1-SNAPSHOT\myco-framework-6-0.0.1-20250807.115026-72.jar;D:\maven\repository\com\alibaba\nacos\nacos-client\2.4.3\nacos-client-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-auth-plugin\2.4.3\nacos-auth-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-encryption-plugin\2.4.3\nacos-encryption-plugin-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-logback-adapter-12\2.4.3\nacos-logback-adapter-12-2.4.3.jar;D:\maven\repository\com\alibaba\nacos\logback-adapter\1.1.3\logback-adapter-1.1.3.jar;D:\maven\repository\com\alibaba\nacos\nacos-log4j2-adapter\2.4.3\nacos-log4j2-adapter-2.4.3.jar;D:\maven\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\maven\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\maven\repository\org\quartz-scheduler\quartz\2.5.0\quartz-2.5.0.jar;D:\maven\repository\org\zeromq\jeromq\0.6.0\jeromq-0.6.0.jar;D:\maven\repository\eu\neilalexander\jnacl\1.0.0\jnacl-1.0.0.jar;D:\maven\repository\org\apache\commons\commons-pool2\2.12.1\commons-pool2-2.12.1.jar;D:\maven\repository\org\aspectj\aspectjrt\1.9.22.1\aspectjrt-1.9.22.1.jar;D:\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;D:\maven\repository\org\springframework\spring-jdbc\6.2.3\spring-jdbc-6.2.3.jar;D:\maven\repository\org\apache\curator\curator-framework\4.3.0\curator-framework-4.3.0.jar;D:\maven\repository\org\apache\curator\curator-client\4.3.0\curator-client-4.3.0.jar;D:\maven\repository\com\google\guava\guava\27.0.1-jre\guava-27.0.1-jre.jar;D:\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\maven\repository\org\checkerframework\checker-qual\2.5.2\checker-qual-2.5.2.jar;D:\maven\repository\com\google\j2objc\j2objc-annotations\1.1\j2objc-annotations-1.1.jar;D:\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.17\animal-sniffer-annotations-1.17.jar;D:\maven\repository\org\apache\curator\curator-recipes\4.3.0\curator-recipes-4.3.0.jar;D:\maven\repository\org\apache\zookeeper\zookeeper\3.6.4\zookeeper-3.6.4.jar;D:\maven\repository\org\apache\zookeeper\zookeeper-jute\3.6.4\zookeeper-jute-3.6.4.jar;D:\maven\repository\org\apache\yetus\audience-annotations\0.13.0\audience-annotations-0.13.0.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final.jar;D:\maven\repository\org\mozilla\rhino\1.8.0\rhino-1.8.0.jar;D:\maven\repository\org\apache\groovy\groovy\4.0.26\groovy-4.0.26.jar;D:\maven\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\maven\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\maven\repository\commons-io\commons-io\2.18.0\commons-io-2.18.0.jar;D:\maven\repository\cglib\cglib-nodep\3.3.0\cglib-nodep-3.3.0.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2\2.0.57\fastjson2-2.0.57.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.18.2\jackson-dataformat-yaml-2.18.2.jar;D:\maven\repository\joda-time\joda-time\2.14.0\joda-time-2.14.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-event\0.0.1-SNAPSHOT\mydata-start-domain-event-0.0.1-20250807.115026-82.jar;D:\maven\repository\com\myco\mydata\mydata-start-domain-enum\0.0.1-SNAPSHOT\mydata-start-domain-enum-0.0.1-20250807.115026-83.jar;D:\maven\repository\org\hibernate\validator\hibernate-validator\9.0.1.Final\hibernate-validator-9.0.1.Final.jar;D:\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\maven\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;D:\maven\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-interfaces\0.0.1-SNAPSHOT\mydata-start-interfaces-0.0.1-20250807.115026-77.jar;D:\maven\repository\org\springframework\spring-webflux\6.2.3\spring-webflux-6.2.3.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-http\1.2.7\reactor-netty-http-1.2.7.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-classes-macos\4.2.2.Final\netty-resolver-dns-classes-macos-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-classes-epoll\4.2.2.Final\netty-transport-classes-epoll-4.2.2.Final.jar;D:\maven\repository\io\projectreactor\netty\reactor-netty-core\1.2.7\reactor-netty-core-1.2.7.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\4.2.0\spring-cloud-starter-loadbalancer-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-loadbalancer\4.2.0\spring-cloud-loadbalancer-4.2.0.jar;D:\maven\repository\io\projectreactor\addons\reactor-extra\3.5.2\reactor-extra-3.5.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-cache\3.4.3\spring-boot-starter-cache-3.4.3.jar;D:\maven\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-joda\2.18.3\jackson-datatype-joda-2.18.3.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;D:\maven\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;D:\maven\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-application\0.0.1-SNAPSHOT\mydata-start-application-0.0.1-20250807.115026-76.jar;D:\maven\repository\com\myco\mydata\mydata-start-gateway\0.0.1-SNAPSHOT\mydata-start-gateway-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-http\0.0.1-SNAPSHOT\mydata-start-infrastructure-http-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-data\0.0.1-SNAPSHOT\mydata-start-infrastructure-data-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-commons\0.0.1-SNAPSHOT\mydata-start-infrastructure-commons-0.0.1-20250807.121047-78.jar;D:\maven\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;D:\maven\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\maven\repository\com\lmax\disruptor\3.4.4\disruptor-3.4.4.jar;D:\maven\repository\com\google\zxing\core\3.5.3\core-3.5.3.jar;D:\maven\repository\net\coobird\thumbnailator\0.4.20\thumbnailator-0.4.20.jar;D:\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.18.3\jackson-dataformat-xml-2.18.3.jar;D:\maven\repository\org\codehaus\woodstox\stax2-api\4.2.2\stax2-api-4.2.2.jar;D:\maven\repository\com\fasterxml\woodstox\woodstox-core\7.0.0\woodstox-core-7.0.0.jar;D:\maven\repository\io\github\jopenlibs\vault-java-driver\6.2.0\vault-java-driver-6.2.0.jar;D:\maven\repository\com\mysql\mysql-connector-j\9.3.0\mysql-connector-j-9.3.0.jar;D:\maven\repository\com\google\protobuf\protobuf-java\4.29.0\protobuf-java-4.29.0.jar;D:\maven\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\maven\repository\org\springframework\spring-context-support\6.2.3\spring-context-support-6.2.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2023.0.1.3\spring-cloud-starter-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2023.0.1.3\spring-cloud-alibaba-commons-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-alibaba-nacos-config\2023.0.1.3\spring-alibaba-nacos-config-2023.0.1.3.jar;D:\maven\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2023.0.1.3\spring-cloud-starter-alibaba-nacos-discovery-2023.0.1.3.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-jms\0.0.1-SNAPSHOT\mydata-start-infrastructure-jms-0.0.1-20250807.115026-77.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-client\4.9.8\rocketmq-client-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-common\4.9.8\rocketmq-common-4.9.8.jar;D:\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-acl\4.9.8\rocketmq-acl-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-remoting\4.9.8\rocketmq-remoting-4.9.8.jar;D:\maven\repository\io\netty\netty-all\4.2.2.Final\netty-all-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec\4.2.2.Final\netty-codec-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-haproxy\4.2.2.Final\netty-codec-haproxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http3\4.2.2.Final\netty-codec-http3-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-memcache\4.2.2.Final\netty-codec-memcache-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-mqtt\4.2.2.Final\netty-codec-mqtt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-redis\4.2.2.Final\netty-codec-redis-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-smtp\4.2.2.Final\netty-codec-smtp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-stomp\4.2.2.Final\netty-codec-stomp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-xml\4.2.2.Final\netty-codec-xml-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-protobuf\4.2.2.Final\netty-codec-protobuf-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-marshalling\4.2.2.Final\netty-codec-marshalling-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-ssl-ocsp\4.2.2.Final\netty-handler-ssl-ocsp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-rxtx\4.2.2.Final\netty-transport-rxtx-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-sctp\4.2.2.Final\netty-transport-sctp-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-udt\4.2.2.Final\netty-transport-udt-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-kqueue\4.2.2.Final\netty-transport-classes-kqueue-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-classes-io_uring\4.2.2.Final\netty-transport-classes-io_uring-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-classes-quic\4.2.2.Final\netty-codec-classes-quic-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-epoll\4.2.2.Final\netty-transport-native-epoll-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-transport-native-io_uring\4.2.2.Final\netty-transport-native-io_uring-4.2.2.Final-linux-riscv64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-transport-native-kqueue\4.2.2.Final\netty-transport-native-kqueue-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-resolver-dns-native-macos\4.2.2.Final\netty-resolver-dns-native-macos-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-linux-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-x86_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-osx-aarch_64.jar;D:\maven\repository\io\netty\netty-codec-native-quic\4.2.2.Final\netty-codec-native-quic-4.2.2.Final-windows-x86_64.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-logging\4.9.8\rocketmq-logging-4.9.8.jar;D:\maven\repository\org\apache\rocketmq\rocketmq-srvutil\4.9.8\rocketmq-srvutil-4.9.8.jar;D:\maven\repository\commons-cli\commons-cli\1.2\commons-cli-1.2.jar;D:\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;D:\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;D:\maven\repository\org\springframework\kafka\spring-kafka\3.3.7\spring-kafka-3.3.7.jar;D:\maven\repository\org\springframework\spring-messaging\6.2.3\spring-messaging-6.2.3.jar;D:\maven\repository\org\springframework\spring-tx\6.2.3\spring-tx-6.2.3.jar;D:\maven\repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar;D:\maven\repository\org\apache\kafka\kafka-clients\3.9.1\kafka-clients-3.9.1.jar;D:\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;D:\maven\repository\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\maven\repository\io\projectreactor\kafka\reactor-kafka\1.3.23\reactor-kafka-1.3.23.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-cache\0.0.1-SNAPSHOT\mydata-start-infrastructure-cache-0.0.1-20250807.115026-76.jar;D:\maven\repository\redis\clients\jedis\3.10.0\jedis-3.10.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-os\0.0.1-SNAPSHOT\mydata-start-infrastructure-os-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\qcloud\cos_api\5.6.242\cos_api-5.6.242.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-kms\3.1.1138\tencentcloud-sdk-java-kms-3.1.1138.jar;D:\maven\repository\com\thoughtworks\xstream\xstream\1.4.21\xstream-1.4.21.jar;D:\maven\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\maven\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\maven\repository\com\auth0\java-jwt\4.4.0\java-jwt-4.4.0.jar;D:\maven\repository\com\qcloud\cos-sts_api\3.1.1\cos-sts_api-3.1.1.jar;D:\maven\repository\com\aliyun\alibabacloud-sts20150401\1.0.7\alibabacloud-sts20150401-1.0.7.jar;D:\maven\repository\com\aliyun\oss\aliyun-sdk-oss\3.18.2\aliyun-sdk-oss-3.18.2.jar;D:\maven\repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\maven\repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\maven\repository\com\aliyun\java-trace-api\0.2.11-beta\java-trace-api-0.2.11-beta.jar;D:\maven\repository\io\opentelemetry\opentelemetry-api\1.43.0\opentelemetry-api-1.43.0.jar;D:\maven\repository\io\opentelemetry\opentelemetry-context\1.43.0\opentelemetry-context-1.43.0.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-proxy\0.0.1-SNAPSHOT\mydata-start-infrastructure-proxy-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-security\0.0.1-SNAPSHOT\mydata-start-infrastructure-security-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-validation\0.0.1-SNAPSHOT\mydata-start-infrastructure-validation-0.0.1-20250807.115026-76.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.4.3\spring-boot-starter-validation-3.4.3.jar;D:\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.36\tomcat-embed-el-10.1.36.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-webflux\3.4.3\spring-boot-starter-webflux-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-json\3.4.3\spring-boot-starter-json-3.4.3.jar;D:\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar;D:\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-reactor-netty\3.4.3\spring-boot-starter-reactor-netty-3.4.3.jar;D:\maven\repository\org\springframework\spring-web\6.2.3\spring-web-6.2.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\4.2.0\spring-cloud-starter-bootstrap-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter\4.2.0\spring-cloud-starter-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-context\4.2.0\spring-cloud-context-4.2.0.jar;D:\maven\repository\org\springframework\security\spring-security-crypto\6.4.3\spring-security-crypto-6.4.3.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-commons\4.2.0\spring-cloud-commons-4.2.0.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.3\spring-boot-starter-actuator-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.3\spring-boot-actuator-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-actuator\3.4.3\spring-boot-actuator-3.4.3.jar;D:\maven\repository\io\micrometer\micrometer-observation\1.14.4\micrometer-observation-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-commons\1.14.4\micrometer-commons-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-jakarta9\1.14.4\micrometer-jakarta9-1.14.4.jar;D:\maven\repository\io\micrometer\micrometer-core\1.14.4\micrometer-core-1.14.4.jar;D:\maven\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;D:\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\maven\repository\com\xk\xk-start-application\0.0.1-SNAPSHOT\xk-start-application-0.0.1-20250813.093748-112.jar;D:\maven\repository\com\xk\xk-start-domain-core\0.0.1-SNAPSHOT\xk-start-domain-core-0.0.1-20250813.093748-124.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-domain\0.0.1-SNAPSHOT\mydata-config-domain-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\xk\xk-start-domain-event\0.0.1-SNAPSHOT\xk-start-domain-event-0.0.1-20250813.093748-125.jar;D:\maven\repository\com\xk\xk-start-interfaces\0.0.1-SNAPSHOT\xk-start-interfaces-0.0.1-20250813.093748-119.jar;D:\maven\repository\com\xk\xk-start-domain-enum\0.0.1-SNAPSHOT\xk-start-domain-enum-0.0.1-20250813.093748-125.jar;D:\maven\repository\com\xk\xk-start-infrastructure\0.0.1-SNAPSHOT\xk-start-infrastructure-0.0.1-20250813.093748-115.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-infrastructure\0.0.1-SNAPSHOT\mydata-config-infrastructure-0.0.1-20250719.075944-9.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-event\0.0.1-SNAPSHOT\mydata-start-infrastructure-event-0.0.1-20250813.055326-78.jar;D:\maven\repository\com\myco\mydata\mydata-start-infrastructure-schedule\0.0.1-SNAPSHOT\mydata-start-infrastructure-schedule-0.0.1-20250807.115026-77.jar;D:\maven\repository\com\alibaba\easyexcel\4.0.3\easyexcel-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-core\4.0.3\easyexcel-core-4.0.3.jar;D:\maven\repository\com\alibaba\easyexcel-support\3.3.4\easyexcel-support-3.3.4.jar;D:\maven\repository\org\apache\poi\poi\5.2.5\poi-5.2.5.jar;D:\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\maven\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\maven\repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar;D:\maven\repository\org\apache\poi\poi-ooxml\5.2.5\poi-ooxml-5.2.5.jar;D:\maven\repository\org\apache\poi\poi-ooxml-lite\5.2.5\poi-ooxml-lite-5.2.5.jar;D:\maven\repository\org\apache\xmlbeans\xmlbeans\5.2.0\xmlbeans-5.2.0.jar;D:\maven\repository\org\apache\commons\commons-compress\1.25.0\commons-compress-1.25.0.jar;D:\maven\repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar;D:\maven\repository\org\apache\commons\commons-csv\1.11.0\commons-csv-1.11.0.jar;D:\maven\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\maven\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\maven\repository\com\xk\xk-start-gateway\0.0.1-SNAPSHOT\xk-start-gateway-0.0.1-20250813.093748-118.jar;D:\maven\repository\com\myco\mydata\config\mydata-config-gateway\0.0.1-SNAPSHOT\mydata-config-gateway-0.0.1-20250719.075944-9.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-starter-openfeign\4.2.0\spring-cloud-starter-openfeign-4.2.0.jar;D:\maven\repository\org\springframework\cloud\spring-cloud-openfeign-core\4.2.0\spring-cloud-openfeign-core-4.2.0.jar;D:\maven\repository\io\github\openfeign\feign-form-spring\13.5\feign-form-spring-13.5.jar;D:\maven\repository\io\github\openfeign\feign-form\13.5\feign-form-13.5.jar;D:\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\maven\repository\io\github\openfeign\feign-core\13.5\feign-core-13.5.jar;D:\maven\repository\io\github\openfeign\feign-slf4j\13.5\feign-slf4j-13.5.jar;D:\code\xk\xk-third-party\xk-third-party-application\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-core\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-event\target\classes;D:\code\xk\xk-third-party\xk-third-party-interfaces\target\classes;D:\code\xk\xk-third-party\xk-third-party-domain\xk-third-party-domain-enum\target\classes;D:\code\xk\xk-third-party\xk-third-party-gateway\target\classes;D:\maven\repository\com\xk\acct\xk-acct-interfaces\0.0.1-SNAPSHOT\xk-acct-interfaces-0.0.1-20250813.030141-73.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-enum\0.0.1-SNAPSHOT\xk-acct-domain-enum-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\message\xk-message-domain-enum\0.0.1-SNAPSHOT\xk-message-domain-enum-0.0.1-20250813.121003-24.jar;D:\maven\repository\com\xk\goods\xk-goods-interfaces\0.0.1-SNAPSHOT\xk-goods-interfaces-0.0.1-20250806.013259-167.jar;D:\maven\repository\com\xk\goods\xk-goods-domain-enum\0.0.1-SNAPSHOT\xk-goods-domain-enum-0.0.1-20250806.013259-159.jar;D:\maven\repository\com\xk\corp\xk-corp-interfaces\0.0.1-SNAPSHOT\xk-corp-interfaces-0.0.1-20250728.125932-33.jar;D:\maven\repository\com\xk\corp\xk-corp-domain-enum\0.0.1-SNAPSHOT\xk-corp-domain-enum-0.0.1-20250728.125932-38.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-event\0.0.1-SNAPSHOT\xk-auth-domain-event-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\auth\xk-auth-domain-enum\0.0.1-SNAPSHOT\xk-auth-domain-enum-0.0.1-20250805.030415-14.jar;D:\maven\repository\com\xk\acct\xk-acct-domain-event\0.0.1-SNAPSHOT\xk-acct-domain-event-0.0.1-20250813.030141-70.jar;D:\maven\repository\com\xk\message\xk-message-domain-event\0.0.1-SNAPSHOT\xk-message-domain-event-0.0.1-20250813.121003-22.jar;D:\maven\repository\org\apache\httpcomponents\httpclient\4.5.9\httpclient-4.5.9.jar;D:\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\maven\repository\commons-codec\commons-codec\1.18.0\commons-codec-1.18.0.jar;D:\code\xk\xk-third-party\xk-third-party-infrastructure\target\classes;D:\maven\repository\com\alipay\sdk\alipay-sdk-java\4.39.190.ALL\alipay-sdk-java-4.39.190.ALL.jar;D:\maven\repository\com\alibaba\fastjson\2.0.57\fastjson-2.0.57.jar;D:\maven\repository\com\alibaba\fastjson2\fastjson2-extension\2.0.57\fastjson2-extension-2.0.57.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk15on\1.62\bcprov-jdk15on-1.62.jar;D:\maven\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;D:\maven\repository\xml-apis\xml-apis\1.0.b2\xml-apis-1.0.b2.jar;D:\maven\repository\com\squareup\okhttp3\okhttp\3.12.13\okhttp-3.12.13.jar;D:\maven\repository\com\squareup\okio\okio\1.15.0\okio-1.15.0.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-apache-httpclient\0.4.2\wechatpay-apache-httpclient-0.4.2.jar;D:\maven\repository\org\apache\httpcomponents\httpmime\4.5.14\httpmime-4.5.14.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-java\0.2.12\wechatpay-java-0.2.12.jar;D:\maven\repository\com\github\wechatpay-apiv3\wechatpay-java-core\0.2.12\wechatpay-java-core-0.2.12.jar;D:\maven\repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar;D:\maven\repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-intl-en\3.0.988\tencentcloud-sdk-java-intl-en-3.0.988.jar;D:\maven\repository\com\squareup\okhttp3\logging-interceptor\3.12.13\logging-interceptor-3.12.13.jar;D:\maven\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-common\3.1.924\tencentcloud-sdk-java-common-3.1.924.jar;D:\maven\repository\com\tencentcloudapi\tencentcloud-sdk-java-trtc\3.1.1315\tencentcloud-sdk-java-trtc-3.1.1315.jar;D:\maven\repository\com\aliyun\alibabacloud-dysmsapi20170525\3.0.4\alibabacloud-dysmsapi20170525-3.0.4.jar;D:\maven\repository\com\aliyun\aliyun-gateway-pop\0.2.15-beta\aliyun-gateway-pop-0.2.15-beta.jar;D:\maven\repository\com\aliyun\darabonba-java-core\0.2.15-beta\darabonba-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-http-apache\0.2.15-beta\aliyun-http-apache-0.2.15-beta.jar;D:\maven\repository\org\jetbrains\annotations\26.0.2\annotations-26.0.2.jar;D:\maven\repository\com\aliyun\aliyun-java-core\0.2.15-beta\aliyun-java-core-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-auth\0.2.15-beta\aliyun-java-auth-0.2.15-beta.jar;D:\maven\repository\com\aliyun\aliyun-java-sdk-core\4.7.3\aliyun-java-sdk-core-4.7.3.jar;D:\maven\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;D:\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;D:\maven\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;D:\maven\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;D:\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;D:\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\maven\repository\org\bouncycastle\bcprov-jdk18on\1.78.1\bcprov-jdk18on-1.78.1.jar;D:\maven\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\maven\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\maven\repository\com\aliyun\alibabacloud-cloudauth20190307\2.0.6\alibabacloud-cloudauth20190307-2.0.6.jar;D:\maven\repository\com\huifu\bspay\sdk\dg-java-sdk\3.0.27\dg-java-sdk-3.0.27.jar;D:\maven\repository\com\getui\push\restful-sdk\*******\restful-sdk-*******.jar;D:\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\maven\repository\org\projectlombok\lombok\1.18.38\lombok-1.18.38.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-spring-boot-starter\1.4.8\mapstruct-plus-spring-boot-starter-1.4.8.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus\1.4.8\mapstruct-plus-1.4.8.jar;D:\maven\repository\org\mapstruct\mapstruct\1.5.5.Final\mapstruct-1.5.5.Final.jar;D:\maven\repository\io\github\linpeilie\mapstruct-plus-object-convert\1.4.8\mapstruct-plus-object-convert-1.4.8.jar;D:\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.3\spring-boot-autoconfigure-3.4.3.jar;D:\maven\repository\org\springframework\boot\spring-boot\3.4.3\spring-boot-3.4.3.jar;D:\maven\repository\org\springframework\spring-context\6.2.3\spring-context-6.2.3.jar;D:\maven\repository\org\springframework\spring-aop\6.2.3\spring-aop-6.2.3.jar;D:\maven\repository\org\springframework\spring-beans\6.2.3\spring-beans-6.2.3.jar;D:\maven\repository\org\springframework\spring-expression\6.2.3\spring-expression-6.2.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter\3.4.3\spring-boot-starter-3.4.3.jar;D:\maven\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;D:\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;D:\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;D:\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;D:\maven\repository\org\springframework\spring-core\6.2.3\spring-core-6.2.3.jar;D:\maven\repository\org\springframework\spring-jcl\6.2.3\spring-jcl-6.2.3.jar;D:\maven\repository\io\vertx\vertx-core\5.0.1\vertx-core-5.0.1.jar;D:\maven\repository\io\vertx\vertx-core-logging\5.0.1\vertx-core-logging-5.0.1.jar;D:\maven\repository\io\netty\netty-common\4.2.2.Final\netty-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-buffer\4.2.2.Final\netty-buffer-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport\4.2.2.Final\netty-transport-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler\4.2.2.Final\netty-handler-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-transport-native-unix-common\4.2.2.Final\netty-transport-native-unix-common-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-base\4.2.2.Final\netty-codec-base-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-handler-proxy\4.2.2.Final\netty-handler-proxy-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-socks\4.2.2.Final\netty-codec-socks-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http\4.2.2.Final\netty-codec-http-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-compression\4.2.2.Final\netty-codec-compression-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-http2\4.2.2.Final\netty-codec-http2-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver\4.2.2.Final\netty-resolver-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-resolver-dns\4.2.2.Final\netty-resolver-dns-4.2.2.Final.jar;D:\maven\repository\io\netty\netty-codec-dns\4.2.2.Final\netty-codec-dns-4.2.2.Final.jar;D:\maven\repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-log4j2\3.4.3\spring-boot-starter-log4j2-3.4.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-slf4j2-impl\2.24.3\log4j-slf4j2-impl-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-core\2.24.3\log4j-core-2.24.3.jar;D:\maven\repository\org\apache\logging\log4j\log4j-jul\2.24.3\log4j-jul-2.24.3.jar;D:\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.4.3\spring-boot-configuration-processor-3.4.3.jar;D:\maven\repository\io\projectreactor\reactor-core\3.7.7\reactor-core-3.7.7.jar;D:\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\lib\idea_rt.jar
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.library.path=C:\Program Files\Java\jdk-21\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;C:\Program Files\Git\cmd;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Program Files\JetBrains\IntelliJ IDEA 2025.2\bin;C:\Program Files\JetBrains\PyCharm 2025.1.3.1\bin;;.
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:java.compiler=<NA>
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.name=Windows 11
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.arch=amd64
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.version=10.0
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:user.name=ShiJia
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:user.home=C:\Users\<USER>\code\xk
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.memory.free=86MB
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.memory.max=8048MB
[main:1]2025-08-18 16:26:29.009 INFO  [ZooKeeper:] - Client environment:os.memory.total=240MB
[main:1]2025-08-18 16:26:29.029 INFO  [CuratorFrameworkImpl:] - Starting
[main:1]2025-08-18 16:26:29.030 DEBUG [CuratorZookeeperClient:] - Starting
[main:1]2025-08-18 16:26:29.030 DEBUG [ConnectionState:] - Starting
[main:1]2025-08-18 16:26:29.030 DEBUG [ConnectionState:] - reset
[main:1]2025-08-18 16:26:29.031 INFO  [ZooKeeper:] - Initiating client connection, connectString=*************:2181 sessionTimeout=60000 watcher=org.apache.curator.ConnectionState@7f55e1b9
[main:1]2025-08-18 16:26:29.034 INFO  [X509Util:] - Setting -D jdk.tls.rejectClientInitiatedRenegotiation=true to disable client-initiated TLS renegotiation
[main:1]2025-08-18 16:26:29.037 INFO  [ClientCnxnSocket:] - jute.maxbuffer value is 1048575 Bytes
[main:1]2025-08-18 16:26:29.041 INFO  [ClientCnxn:] - zookeeper.request.timeout value is 0. feature enabled=false
[main:1]2025-08-18 16:26:29.046 INFO  [CuratorFrameworkImpl:] - Default schema
[main:1]2025-08-18 16:26:29.046 INFO  [ZookeeperClientFactoryBean:] - ZK connection is successful.
[main:1]2025-08-18 16:26:29.046 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeper' of type [com.myco.framework.support.zookeeper.ZookeeperClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.054 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'zookeeperTemplate' of type [com.myco.mydata.infrastructure.commons.support.OpenZookeeperTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.062 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig' of type [com.myco.mydata.infrastructure.cache.config.BusinessCacheStartConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.084 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [com.myco.framework.support.redis.shard.ShardedJedisClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.102 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'busiRedisClient' of type [redis.clients.jedis.ShardedJedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.109 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'shardedJedisOperation' of type [com.myco.framework.support.redis.shard.ShardedJedisOperation] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.150 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'entityRedisTemplate' of type [com.myco.mydata.infrastructure.cache.adapter.EntityRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.180 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.ncs.zookeeper.lockTimeout.user' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type Integer
[main:1]2025-08-18 16:26:29.184 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'lockRootService' of type [com.myco.mydata.infrastructure.commons.lock.UserLockTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.197 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.rocketmq-producer.rmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:29.198 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'com.myco.framework.support.rocketmq.RocketMQSenderConfig' of type [com.myco.framework.support.rocketmq.RocketMQSenderConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.201 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'rpcHook' of type [org.apache.rocketmq.acl.common.AclClientRPCHook] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.223 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'userObjectDao' of type [com.xk.infrastructure.cache.dao.object.UserObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.251 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'corpObjectDao' of type [com.xk.infrastructure.cache.dao.object.CorpObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.256 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'goodsObjectDao' of type [com.xk.infrastructure.cache.dao.object.GoodsObjectDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.261 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheAdapterServiceImpl' of type [com.xk.infrastructure.adapter.object.TransactionFlushToCacheAdapterServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.264 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionFlushToCacheRootServiceImpl' of type [com.myco.mydata.domain.service.transaction.impl.TransactionFlushToCacheRootServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.269 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'transactionManager' of type [com.myco.mydata.domain.operation.transaction.DistributedLockTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.275 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'requiredTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.283 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.285 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.readonlyRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:29.285 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.requiredRule' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:29.287 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [create*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.287 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [revise*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.287 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [sync*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [add*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [update*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [handle] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [incr*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [amend*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [save*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [persist*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [remove*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [terminate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [delete*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [insert*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [commit*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [merge*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [apply*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [initiate*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [alter*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [cancel*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [mod*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [retire*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [store*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.288 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.292 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.txPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:29.293 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.295 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'readonlyTx' of type [org.springframework.transaction.interceptor.RuleBasedTransactionAttribute] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.295 DEBUG [NameMatchTransactionAttributeSource:] - Adding transactional method [*] with attribute [PROPAGATION_REQUIRED,ISOLATION_DEFAULT,readOnly,-java.lang.Throwable]
[main:1]2025-08-18 16:26:29.296 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceSource' of type [org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.297 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.transaction.queryPointcut' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:29.297 WARN  [PostProcessorRegistrationDelegate$BeanPostProcessorChecker:] - Bean 'queryTxAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [beansOfTypeToMapPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
[main:1]2025-08-18 16:26:29.544 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.level: simple
[main:1]2025-08-18 16:26:29.544 DEBUG [ResourceLeakDetector:] - -Dio.netty.leakDetection.targetRecords: 4
[main:1]2025-08-18 16:26:29.601 DEBUG [GlobalEventExecutor:] - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
[main:1]2025-08-18 16:26:30.401 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.jms.zmq-producer.zmqErrorQueue' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:30.941 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.redis.tableRedisClientRef' in PropertySource 'Config resource 'class path resource [application-data.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:31.754 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:31.754 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:31.754 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:31.755 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:31.755 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:33.085 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.085 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.265 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.265 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.274 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.274 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.283 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.283 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.291 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.291 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.300 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.300 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.308 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.308 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.342 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.342 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.352 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.352 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.388 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.388 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.429 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.429 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.438 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.438 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.447 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.deploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.447 WARN  [CglibAopProxy:] - Unable to proxy interface-implementing method [public final io.vertx.core.Future io.vertx.core.AbstractVerticle.undeploy(io.vertx.core.Context)] because it is marked as final, consider using interface-based JDK proxies instead.
[main:1]2025-08-18 16:26:33.759 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:33.759 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:33.759 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:34.437 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.myco.mydata.domain.service.consumer.ConsumerBusinessService
[main:1]2025-08-18 16:26:34.479 DEBUG [ResourceBundleMessageInterpolator:] - Loaded expression factory via original TCCL
[main:1]2025-08-18 16:26:34.480 DEBUG [AbstractConfigurationImpl:] - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
[main:1]2025-08-18 16:26:34.481 DEBUG [AbstractConfigurationImpl:] - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
[main:1]2025-08-18 16:26:34.482 DEBUG [ValidationXmlParser:] - Trying to load META-INF/validation.xml for XML based Validator configuration.
[main:1]2025-08-18 16:26:34.482 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via user class loader
[main:1]2025-08-18 16:26:34.482 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via TCCL
[main:1]2025-08-18 16:26:34.482 DEBUG [ResourceLoaderHelper:] - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
[main:1]2025-08-18 16:26:34.482 DEBUG [ValidationXmlParser:] - No META-INF/validation.xml found. Using annotation based configuration only.
[main:1]2025-08-18 16:26:34.483 DEBUG [TraversableResolvers:] - Cannot find jakarta.persistence.Persistence on classpath. Assuming non Jakarta Persistence environment. All properties will per default be traversable.
[main:1]2025-08-18 16:26:34.485 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
[main:1]2025-08-18 16:26:34.485 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
[main:1]2025-08-18 16:26:34.486 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.resolver.TraverseAllTraversableResolver as ValidatorFactory-scoped traversable resolver.
[main:1]2025-08-18 16:26:34.486 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
[main:1]2025-08-18 16:26:34.486 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
[main:1]2025-08-18 16:26:34.486 DEBUG [ValidatorFactoryConfigurationHelper:] - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
[main:1]2025-08-18 16:26:35.606 WARN  [BeansOfTypeToMapPostProcessor:] - No beans found of type com.xk.domain.service.tag.TagVerifyService
[main:1]2025-08-18 16:26:37.410 DEBUG [AutoConfigurationPackages:213] - @EnableAutoConfiguration was declared on a class in the package 'com.myco.mydata.server'. Automatic @Repository and @Entity scanning is enabled.
[main:1]2025-08-18 16:26:37.587 DEBUG [Mappings:] - Mapped /tp/query/access => {
 ((POST && /searchAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd108f8@87c6dda
 ((POST && /searchAccessList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11548@423d31b0
 ((POST && /searchAccessListByNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11760@5b2a690f
 ((POST && /searchAccessDetail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11978@380191d7
 ((POST && /searchAccessExtDetail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11b90@537839b0
 ((POST && /searchAccessDetailByNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11da8@786332de
 ((POST && /searchAccessExtList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd11fc0@70895ecc
 ((POST && /searchAccessAccountList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd121d8@4432f623
 ((POST && /queryAccessInterfaceAuthInfoByCorp) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd123f0@5615f2ac
 ((POST && /queryAccessInterfaceAuthInfoByCorpNoLogin) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd12608@3701df72
 ((POST && /searchAccessTpGame) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd12820@d449860
 ((POST && /queryTagRelation) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd12a38@5e14f405
 ((POST && /searchAccessTag) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd12c50@27edfdf9
 ((POST && /queryTagRelationByAccessTpTag) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd12e68@1e2e5c39
 ((POST && /queryAccessGameById) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd13080@********
 ((POST && /queryAccessGameByDdGameId) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd13298@4e4d2e2d
 ((POST && /queryAccessExtInfo) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd134b0@6870028
 ((POST && /searchAccessAndAccountList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd136c8@6aa45b55
 ((POST && /queryAccessInterfaceAuthInfoByAccessId) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd138e0@5f113675
 ((POST && /searchBlackAccessList) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd13af8@788a47fc
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/query/pay => {
 ((POST && /findByParams) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14440@46329e85
 ((POST && /findPayResult) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14658@e840be5
 ((POST && /findPayQuota) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14870@58f036de
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/query/userAuth => {
 ((POST && /findPreUserAuth) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14a88@72ef3f9b
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/query/auth => {
 ((POST && /findAuthNotifyByParams) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14ca0@207b41f5
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/query/thirdCaptcha => {
 ((POST && /checkCaptcha) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd14eb8@e8ebd3c
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/useLog/query => {
 ((POST && /searchUseLog) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd150d0@57913cf5
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/logistics/query => {
 ((POST && /detail) && Accept: application/json) -> com.xk.tp.server.endpoints.QueryRoutingConfig$$Lambda/0x0000023a1bd152e8@5c89c27b
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/access => {
 ((POST && /saveAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15500@260754f6
 ((POST && /updateAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15718@5d1b7a57
 ((POST && /deleteAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15930@3b38395
 ((POST && /saveAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15b48@113b0382
 ((POST && /updateAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15d60@d0745b7
 ((POST && /deleteAccessExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd15f78@bc85f82
 ((POST && /saveAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd16190@14f8ca45
 ((POST && /updateAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd163a8@5d16fc4b
 ((POST && /deleteAccessAccount) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd165c0@6a8b30a3
 ((POST && /saveAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd167d8@2376463f
 ((POST && /updateAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd169f0@710e1af6
 ((POST && /deleteAccessAccountExt) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd16c08@1b904438
 ((POST && /updateAccessStatus) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd16e20@28ad61c1
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/sms => {
 ((POST && /sendSms) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17038@39fb9d55
 ((POST && /sendSmsByAccess) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17250@3823e936
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/pay => {
 ((POST && /createPay) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17468@210bdb8e
 ((POST && /closeOrder) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17680@62eb0507
 ((POST && /refundOrder) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17898@71f3d3a
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/transfer => {
 ((POST && /createPay) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17ab0@20520eb6
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/payNotify => {
 ((POST && /ali/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd17cc8@5f7a7699
 ((POST && /wx/notify/{orderNo}/{accessAccountId}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18000@6503934c
 ((POST && /llian/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18218@1b0b843b
 ((POST && /llianHst/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18430@2b9c0e52
 ((POST && /tlian/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18648@275e7b06
 ((POST && /huifu/notify/{accessAccountId}/{device}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18860@5ebe1552
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/auth => {
 ((POST && /base/auth) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18a78@195368e2
 ((POST && /face/verify) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18c90@52760b04
 ((POST && /face/verify/clear) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd18ea8@624cc07
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/authNotify => {
 ((GET && /ali/face/notify/{accessAccountId}) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd190c0@289e4d14
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/useLog => {
 ((POST && /save) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd192d8@2a88249d
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/share => {
 ((POST && /create/business/config) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd194f0@484424e3
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/live => {
 ((POST && /remove/user) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd19708@68a08d18
 ((POST && /remove/user/str) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd19920@328c9bf5
 ((POST && /dismiss/room) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd19b38@594bedf5
 ((POST && /dismiss/room/str) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd19d50@30c1a903
 ((POST && /sign) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd19f68@5c652a4e
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/recording => {
 ((POST && /create/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1a180@ae04104
 ((POST && /delete/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1a398@c4231e8
 ((POST && /describe/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1a5b0@31f608d2
 ((POST && /modify/cloud) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1a7c8@44b463f8
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tp/im => {
 ((POST && /create/group) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1a9e0@4cd4e20b
 ((POST && /delete/group) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1abf8@********
 ((POST && /online/number) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1ae10@********
 ((POST && /sign) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1b028@3779773f
 ((POST && /send/msg/all) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1b240@5cd61f61
 ((POST && /send/msg) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1b458@2f193ff4
 ((POST && /send/msg/system) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1b670@547cc110
 ((POST && /tencent/callback) && Accept: application/json) -> com.xk.tp.server.endpoints.ServiceRoutingConfig$$Lambda/0x0000023a1bd1b888@28c0942f
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped (GET && /favicon.ico) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x0000023a1bd2a6b0@4481bb41
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /server => {
 (GET && /check) -> com.xk.server.endpoints.check.ServerCheckRoutingConfig$$Lambda/0x0000023a1bd2a8c0@2281daba
}
[main:1]2025-08-18 16:26:37.588 DEBUG [Mappings:] - Mapped /tag => {
 ((POST && /save) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000023a1bd2aad0@10c7ebbb
 ((POST && /update) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000023a1bd2ace8@3c0a6460
 ((POST && /remove) && Accept: application/json) -> com.xk.server.endpoints.tag.TagServiceRoutingConfig$$Lambda/0x0000023a1bd2af00@75989b13
}
[main:1]2025-08-18 16:26:37.613 DEBUG [Mappings:] - 'resourceHandlerMapping' {/webjars/**=ResourceWebHandler [classpath [META-INF/resources/webjars/]], /**=ResourceWebHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/]]}
[main:1]2025-08-18 16:26:37.970 INFO  [StdSchedulerFactory:] - Using default implementation for ThreadExecutor
[main:1]2025-08-18 16:26:37.979 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-18 16:26:37.979 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-18 16:26:37.980 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-18 16:26:37.980 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-18 16:26:37.980 INFO  [StdSchedulerFactory:] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[main:1]2025-08-18 16:26:37.980 INFO  [StdSchedulerFactory:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-18 16:26:37.980 INFO  [QuartzScheduler:] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@48afc94c
[main-SendThread(*************:2181):84]2025-08-18 16:26:38.111 INFO  [ClientCnxn:] - Opening socket connection to server *************/*************:2181.
[main-SendThread(*************:2181):84]2025-08-18 16:26:38.111 INFO  [ClientCnxn:] - SASL config status: Will not attempt to authenticate using SASL (unknown error)
[main-SendThread(*************:2181):84]2025-08-18 16:26:38.125 INFO  [ClientCnxn:] - Socket connection established, initiating session, client: /*************:49200, server: *************/*************:2181
[main-SendThread(*************:2181):84]2025-08-18 16:26:38.139 INFO  [ClientCnxn:] - Session establishment complete on server *************/*************:2181, session id = 0x100000114063238, negotiated timeout = 40000
[main-EventThread:85]2025-08-18 16:26:38.141 DEBUG [ConnectionState:] - Negotiated session timeout: 40000
[main-EventThread:85]2025-08-18 16:26:38.143 INFO  [ConnectionStateManager:] - State change: CONNECTED
[main-EventThread:85]2025-08-18 16:26:38.143 DEBUG [CuratorFrameworkImpl:] - Clearing sleep for 10 operations
[Curator-ConnectionStateManager-0:83]2025-08-18 16:26:38.146 INFO  [LogConnectionStateListener:] - Zookeeper client 状态发生变化: CONNECTED
[main-EventThread:85]2025-08-18 16:26:38.161 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:85]2025-08-18 16:26:38.161 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main-EventThread:85]2025-08-18 16:26:38.161 INFO  [EnsembleTracker:] - New config event received: {}
[main-EventThread:85]2025-08-18 16:26:38.161 DEBUG [EnsembleTracker:] - Ignoring new config as it is empty
[main:1]2025-08-18 16:26:38.170 DEBUG [InternalLoggerFactory:] - Using SLF4J as the default logging framework
[main:1]2025-08-18 16:26:38.371 INFO  [EndpointLinksResolver:60] - Exposing 19 endpoints beneath base path '/actuator'
[main:1]2025-08-18 16:26:38.390 DEBUG [WebFluxEndpointHandlerMapping:164] - 34 mappings in 'webEndpointReactiveHandlerMapping'
[main:1]2025-08-18 16:26:38.450 DEBUG [ControllerMethodResolver:289] - ControllerAdvice beans: none
[main:1]2025-08-18 16:26:38.570 DEBUG [HttpWebHandlerAdapter:267] - enableLoggingRequestDetails='false': form data and headers will be masked to prevent unsafe logging of potentially sensitive data
[main:1]2025-08-18 16:26:38.664 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.maxInMemorySize' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:38.664 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.connectTimeoutMillis' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:38.664 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.responseTimeout' in PropertySource '<EMAIL>' with value of type Integer
[main:1]2025-08-18 16:26:38.664 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:38.665 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:38.679 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.log.errLogDataSourceName' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:38.728 INFO  [JvmCacheConsumerFactoryBean:] - The JVM cache to start listening...
[main:1]2025-08-18 16:26:38.740 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'application.language.exception.baseNames' in PropertySource 'Config resource 'class path resource [application-commons.yml]' via location 'optional:classpath:/'' with value of type String
[main:1]2025-08-18 16:26:38.838 INFO  [DefaultStdSchedulerFactoryBean:] - Using default implementation for ThreadExecutor
[main:1]2025-08-18 16:26:38.838 INFO  [SimpleThreadPool:] - Job execution threads will use class loader of thread: main
[main:1]2025-08-18 16:26:38.838 INFO  [SchedulerSignalerImpl:] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[main:1]2025-08-18 16:26:38.838 INFO  [QuartzScheduler:] - Quartz Scheduler v2.5.0 created.
[main:1]2025-08-18 16:26:38.838 INFO  [RAMJobStore:] - RAMJobStore initialized.
[main:1]2025-08-18 16:26:38.838 INFO  [QuartzScheduler:] - Scheduler meta-data: Quartz Scheduler (v2.5.0) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[main:1]2025-08-18 16:26:38.838 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
[main:1]2025-08-18 16:26:38.838 INFO  [DefaultStdSchedulerFactoryBean:] - Quartz scheduler version: 2.5.0
[main:1]2025-08-18 16:26:38.875 INFO  [JobDetailBuilder:] - 任务[syncReconciledSchedule]已加入执行计划中..
[main:1]2025-08-18 16:26:38.875 INFO  [QuartzSchedulerManager:] - 通过 bean【jobDetailBuilder】共获取到【1】个需要处理的Jobs!
[main:1]2025-08-18 16:26:38.875 INFO  [QuartzSchedulerManager:] - 共获取到【1】个需要处理的Jobs!
[main:1]2025-08-18 16:26:39.215 WARN  [CaffeineCacheMetrics:] - The cache 'CachingServiceInstanceListSupplierCache' is not recording statistics. No meters except 'cache.size' will be registered. Call 'Caffeine#recordStats()' prior to building the cache for metrics to be recorded.
[main:1]2025-08-18 16:26:39.494 DEBUG [SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin:131] - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[main:1]2025-08-18 16:26:40.334 INFO  [NettyWebServer:126] - Netty started on port 11010 (http)
[main:1]2025-08-18 16:26:40.341 INFO  [naming:] - Nacos client key init properties: 
	serverAddr=*************:8848
	namespace=dev
	username=nacos
	password=EQ********3u

[main:1]2025-08-18 16:26:40.342 INFO  [naming:] - initializer namespace from ans.namespace attribute : null
[main:1]2025-08-18 16:26:40.342 INFO  [naming:] - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[main:1]2025-08-18 16:26:40.342 INFO  [naming:] - initializer namespace from namespace attribute :null
[main:1]2025-08-18 16:26:40.346 INFO  [naming:] - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[main:1]2025-08-18 16:26:40.349 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[main:1]2025-08-18 16:26:40.349 INFO  [ClientAuthPluginManager:] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[main:1]2025-08-18 16:26:40.501 INFO  [client:] - [RpcClientFactory] create a new rpc client of 2f4aff85-72a6-456c-a4dc-14e0837b7c94
[main:1]2025-08-18 16:26:40.502 INFO  [naming:] - Create naming rpc client for uuid->2f4aff85-72a6-456c-a4dc-14e0837b7c94
[main:1]2025-08-18 16:26:40.502 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[main:1]2025-08-18 16:26:40.502 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[main:1]2025-08-18 16:26:40.502 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[main:1]2025-08-18 16:26:40.502 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Try to connect to server on start up, server: {serverIp = '*************', server main port = 8848}
[main:1]2025-08-18 16:26:40.502 INFO  [GrpcClient:] - grpc client connection server:************* ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[main:1]2025-08-18 16:26:40.559 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Success to connect to server [*************:8848] on start up, connectionId = 1755505600404_221.12.20.178_27987
[main:1]2025-08-18 16:26:40.559 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[main:1]2025-08-18 16:26:40.559 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda/0x0000023a1b5dbd88
[com.alibaba.nacos.client.remote.worker.0:231]2025-08-18 16:26:40.559 INFO  [client:] - [2f4aff85-72a6-456c-a4dc-14e0837b7c94] Notify connected event to listeners.
[com.alibaba.nacos.client.remote.worker.0:231]2025-08-18 16:26:40.559 INFO  [naming:] - Grpc connection connect
[main:1]2025-08-18 16:26:40.560 INFO  [naming:] - [REGISTER-SERVICE] dev registering service xkThirdParty with instance Instance{instanceId='null', ip='*************', port=11010, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='xkThirdParty', serviceName='null', metadata={preserved.heart.beat.timeout=30000, preserved.ip.delete.timeout=90000, preserved.register.source=SPRING_CLOUD, preserved.heart.beat.interval=30000}}
[main:1]2025-08-18 16:26:40.593 INFO  [NacosServiceRegistry:] - nacos registry, DEFAULT_GROUP xkThirdParty *************:11010 register finished
[main:1]2025-08-18 16:26:40.601 DEBUG [LoggerFactory:] - Using io.vertx.core.logging.SLF4JLogDelegateFactory
[main:1]2025-08-18 16:26:40.684 INFO  [VertxEventBusManager:] - Event bus 'MESSAGE' started.
[main:1]2025-08-18 16:26:40.691 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-18 16:26:40.691 INFO  [VertxEventBusManager:] - Event queue 'MESSAGE' started.
[main:1]2025-08-18 16:26:40.717 INFO  [VertxEventBusManager:] - Event bus 'LOG' started.
[main:1]2025-08-18 16:26:40.719 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-18 16:26:40.719 INFO  [VertxEventBusManager:] - Event queue 'LOG' started.
[main:1]2025-08-18 16:26:40.745 INFO  [VertxEventBusManager:] - Event bus 'AUTH' started.
[main:1]2025-08-18 16:26:40.747 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-18 16:26:40.747 INFO  [VertxEventBusManager:] - Event queue 'AUTH' started.
[main:1]2025-08-18 16:26:40.773 INFO  [VertxEventBusManager:] - Event bus 'GOODS' started.
[main:1]2025-08-18 16:26:40.775 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-18 16:26:40.775 INFO  [VertxEventBusManager:] - Event queue 'GOODS' started.
[main:1]2025-08-18 16:26:40.802 INFO  [VertxEventBusManager:] - Event bus 'THIRD_PARTY' started.
[main:1]2025-08-18 16:26:40.804 DEBUG [EventRootDisruptor:] - Starting EventRootDisruptor disruptor for this configuration with ringBufferSize=131072, waitStrategy=TimeoutBlockingWaitStrategy...
[main:1]2025-08-18 16:26:40.804 INFO  [VertxEventBusManager:] - Event queue 'THIRD_PARTY' started.
[vert.x-eventloop-thread-0:261]2025-08-18 16:26:40.814 INFO  [AbstractEventVerticle:] - Deploying 'ShorMessageCreateEventHandler-0'...
[main:1]2025-08-18 16:26:40.815 INFO  [VertxEventBusManager:] - register event bus:MESSAGE, handler:com.xk.tp.application.handler.event.message.ShorMessageCreateEventHandler$$SpringCGLIB$$0
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:LOG, handler:com.xk.tp.application.handler.event.log.CreateUseLogEventHandler$$SpringCGLIB$$0
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:AUTH, handler:com.xk.application.handler.event.log.LogSecureEventHandler$$SpringCGLIB$$0
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.tp.application.handler.event.access.CreateAccessEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:262]2025-08-18 16:26:40.816 INFO  [AbstractEventVerticle:] - Deploying 'CreateUseLogEventHandler-1'...
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:GOODS, handler:com.xk.tp.application.handler.event.access.DeletedAccessEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:263]2025-08-18 16:26:40.816 INFO  [AbstractEventVerticle:] - Deploying 'LogSecureEventHandler-2'...
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.access.UpdateAccessAccountEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:264]2025-08-18 16:26:40.816 INFO  [AbstractEventVerticle:] - Deploying 'CreateAccessEventHandler-3'...
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.auth.AuthFinishEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-1:265]2025-08-18 16:26:40.816 INFO  [AbstractEventVerticle:] - Deploying 'DeletedAccessEventHandler-4'...
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.auth.SyncNotifyStatusEventHandler$$SpringCGLIB$$0
[main:1]2025-08-18 16:26:40.816 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.pay.PayFinishEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-0:266]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'UpdateAccessAccountEventHandler-5'...
[main:1]2025-08-18 16:26:40.817 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.push.PushAppMessageEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-1:267]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'AuthFinishEventHandler-6'...
[vert.x-eventloop-thread-2:268]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'SyncNotifyStatusEventHandler-7'...
[main:1]2025-08-18 16:26:40.817 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.reconciled.HuiFuMerchantConfigJobEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-3:269]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'PayFinishEventHandler-8'...
[vert.x-eventloop-thread-4:270]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'PushAppMessageEventHandler-9'...
[vert.x-eventloop-thread-5:271]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Deploying 'HuiFuMerchantConfigJobEventHandler-10'...
[vert.x-eventloop-thread-4:270]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'PushAppMessageEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-1:267]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'AuthFinishEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-1:265]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'DeletedAccessEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:266]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'UpdateAccessAccountEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-0:262]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateUseLogEvent[LOG[YD_LOG]]'
[vert.x-eventloop-thread-0:264]2025-08-18 16:26:40.817 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'CreateAccessEvent[GOODS[YD_GOODS]]'
[vert.x-eventloop-thread-0:263]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'LogSecureEvent[AUTH[YD_AUTH]]'
[vert.x-eventloop-thread-2:268]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SyncFaceVerifyStatusEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-0:261]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'ShortMessageCreateEvent[MESSAGE[YD_MESSAGE]]'
[vert.x-eventloop-thread-5:271]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'HuiFuMerchantConfigJobEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-3:269]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'PayFinishEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[main:1]2025-08-18 16:26:40.820 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.reconciled.SyncReconciledJobEventHandler$$SpringCGLIB$$0
[main:1]2025-08-18 16:26:40.820 INFO  [VertxEventBusManager:] - register event bus:THIRD_PARTY, handler:com.xk.tp.application.handler.event.sms.SendSmsEventHandler$$SpringCGLIB$$0
[vert.x-eventloop-thread-6:272]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Deploying 'SyncReconciledJobEventHandler-11'...
[main:1]2025-08-18 16:26:40.820 INFO  [NacosDiscoveryHeartBeatPublisher:] - Start nacos heartBeat task scheduler.
[vert.x-eventloop-thread-6:272]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SyncReconciledJobEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[vert.x-eventloop-thread-7:273]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Deploying 'SendSmsEventHandler-12'...
[vert.x-eventloop-thread-7:273]2025-08-18 16:26:40.820 INFO  [AbstractEventVerticle:] - Registering event bus consumer on route 'SendSmsEvent[THIRD_PARTY[YD_THIRD_PARTY]]'
[main:1]2025-08-18 16:26:40.822 INFO  [SchedulerFactoryBean:] - Starting Quartz Scheduler now
[main:1]2025-08-18 16:26:40.822 INFO  [QuartzScheduler:] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:26:40.822 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[main:1]2025-08-18 16:26:40.824 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.user' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:40.824 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.webClient.loadBalanced.password' in PropertySource '<EMAIL>' with value of type String
[main:1]2025-08-18 16:26:40.825 DEBUG [ConfigurationPropertySourcesPropertyResolver$DefaultResolver:121] - Found key 'configuration.validation.expiresTime' in PropertySource '<EMAIL>' with value of type Integer
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.auth.AuthFinishEventHandler' with ID: 7
[vert.x-eventloop-thread-1:277]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.message.ShorMessageCreateEventHandler' with ID: 1
[vert.x-eventloop-thread-1:276]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.application.handler.event.log.LogSecureEventHandler' with ID: 3
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.reconciled.HuiFuMerchantConfigJobEventHandler' with ID: 11
[vert.x-eventloop-thread-2:278]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.DeletedAccessEventHandler' with ID: 5
[vert.x-eventloop-thread-2:278]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.CreateAccessEventHandler' with ID: 4
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.reconciled.SyncReconciledJobEventHandler' with ID: 12
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.auth.SyncNotifyStatusEventHandler' with ID: 8
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.pay.PayFinishEventHandler' with ID: 9
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.access.UpdateAccessAccountEventHandler' with ID: 6
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.sms.SendSmsEventHandler' with ID: 13
[vert.x-eventloop-thread-8:275]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.push.PushAppMessageEventHandler' with ID: 10
[vert.x-eventloop-thread-1:279]2025-08-18 16:26:40.833 INFO  [AbstractEventVerticle:] - Deployed 1 instances of 'com.xk.tp.application.handler.event.log.CreateUseLogEventHandler' with ID: 2
[main:1]2025-08-18 16:26:40.839 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-18 16:26:40.839 INFO  [ServiceApplicationListener:] -  The xkThirdParty:dev has been started.
[main:1]2025-08-18 16:26:40.839 INFO  [ServiceApplicationListener:] - /----------------------------------------------------/
[main:1]2025-08-18 16:26:40.840 INFO  [XkThirdPartyServer:] - Started XkThirdPartyServer in 16.607 seconds (process running for 17.87)
[main:1]2025-08-18 16:26:40.841 DEBUG [ApplicationAvailabilityBean:77] - Application availability state LivenessState changed to CORRECT
[main:1]2025-08-18 16:26:40.858 INFO  [QuartzSchedulerManager:] - Will start Quartz Scheduler [DefaultQuartzScheduler] in 5 seconds
[main:1]2025-08-18 16:26:40.860 INFO  [ClientWorker:] - [fixed-dev-*************_8848] [subscribe] xkThirdParty-schedule.yml+DEFAULT_GROUP+dev
[main:1]2025-08-18 16:26:40.860 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP, cnt=1
[main:1]2025-08-18 16:26:40.860 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkThirdParty-schedule.yml, group=DEFAULT_GROUP
[main:1]2025-08-18 16:26:40.860 INFO  [CacheData:] - [fixed-dev-*************_8848] [add-listener] ok, tenant=dev, dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP, cnt=2
[main:1]2025-08-18 16:26:40.860 INFO  [NacosContextRefresher:] - [Nacos Config] Listening config: dataId=xkThirdParty-dev.yml, group=DEFAULT_GROUP
[main:1]2025-08-18 16:26:40.861 DEBUG [ApplicationAvailabilityBean:77] - Application availability state ReadinessState changed to ACCEPTING_TRAFFIC
[Thread-14:118]2025-08-18 16:26:42.363 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-AuthFinishEvent' queue.
[Thread-15:119]2025-08-18 16:26:42.402 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-SyncFaceVerifyStatusEvent' queue.
[Thread-16:120]2025-08-18 16:26:42.402 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_LOG-LOG-CreateUseLogEvent' queue.
[Thread-17:121]2025-08-18 16:26:42.409 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_MESSAGE-MESSAGE-ShortMessageCreateEvent' queue.
[Thread-18:122]2025-08-18 16:26:42.409 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-PayFinishEvent' queue.
[Thread-19:123]2025-08-18 16:26:42.421 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-PushAppMessageEvent' queue.
[Thread-20:124]2025-08-18 16:26:42.430 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-HuiFuMerchantConfigJobEvent' queue.
[Thread-21:125]2025-08-18 16:26:42.432 INFO  [DefaultMqConsumer:] - Waiting to receive for 'YD_THIRD_PARTY-THIRD_PARTY-SyncReconciledJobEvent' queue.
[DefaultQuartzScheduler:280]2025-08-18 16:26:45.859 INFO  [QuartzSchedulerManager:] - Starting Quartz Scheduler now
[DefaultQuartzScheduler:280]2025-08-18 16:26:45.859 INFO  [QuartzScheduler:] - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:26:45.859 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:27:08.774 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:27:13.714 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:27:38.598 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:27:39.179 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:28:02.800 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:28:08.016 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:28:27.757 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:28:36.118 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[NettyClientPublicExecutor_6:397]2025-08-18 16:28:47.642 INFO  [AbstractDispatchMessageListener:] - Receives 1 messages from topic YD_THIRD_PARTY-THIRD_PARTY-SyncReconciledJobEvent/1
[NettyClientPublicExecutor_6:397]2025-08-18 16:28:47.642 DEBUG [AbstractDispatchMessageListener:] - The messages body: [{"identifier":297,"payPlatformTypeEnum":"HUIFU","tradeDate":"2025-08-16"}]
[event-1:535]2025-08-18 16:28:47.679 DEBUG [AOProxyAspect:] - The event is called proxies!
[event-1:535]2025-08-18 16:28:48.093 DEBUG [RequestAddCookies:] - CookieSpec selected: default
[event-1:535]2025-08-18 16:28:48.101 DEBUG [RequestAuthCache:] - Auth cache not set in the context
[event-1:535]2025-08-18 16:28:48.113 DEBUG [MainClientExec:] - Opening connection {s}->https://api.huifu.com:443
[event-1:535]2025-08-18 16:28:48.122 DEBUG [DefaultHttpClientConnectionOperator:] - Connecting to api.huifu.com/*************:443
[event-1:535]2025-08-18 16:28:48.122 DEBUG [SSLConnectionSocketFactory:] - Connecting socket to api.huifu.com/*************:443 with timeout 30000
[event-1:535]2025-08-18 16:28:48.151 DEBUG [SSLConnectionSocketFactory:] - Enabled protocols: [TLSv1.3, TLSv1.2]
[event-1:535]2025-08-18 16:28:48.151 DEBUG [SSLConnectionSocketFactory:] - Enabled cipher suites:[TLS_AES_256_GCM_SHA384, TLS_AES_128_GCM_SHA256, TLS_CHACHA20_POLY1305_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256, TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256, TLS_DHE_DSS_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_256_CBC_SHA256, TLS_DHE_DSS_WITH_AES_256_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_256_CBC_SHA, TLS_DHE_DSS_WITH_AES_256_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_256_GCM_SHA384, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
[event-1:535]2025-08-18 16:28:48.151 DEBUG [SSLConnectionSocketFactory:] - Starting handshake
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] - Secure session established
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] -  negotiated protocol: TLSv1.2
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] -  negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] -  peer principal: CN=*.huifu.com, O=上海汇付支付有限公司, ST=上海市, C=CN
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] -  peer alternative names: [*.huifu.com, huifu.com]
[event-1:535]2025-08-18 16:28:48.330 DEBUG [SSLConnectionSocketFactory:] -  issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
[event-1:535]2025-08-18 16:28:48.332 DEBUG [DefaultHttpClientConnectionOperator:] - Connection established *************:49340<->*************:443
[event-1:535]2025-08-18 16:28:48.332 DEBUG [DefaultManagedHttpClientConnection:] - http-outgoing-0: set socket timeout to 30000
[event-1:535]2025-08-18 16:28:48.332 DEBUG [MainClientExec:] - Executing request POST /v2/trade/check/filequery HTTP/1.1
[event-1:535]2025-08-18 16:28:48.332 DEBUG [MainClientExec:] - Target auth state: UNCHALLENGED
[event-1:535]2025-08-18 16:28:48.332 DEBUG [MainClientExec:] - Proxy auth state: UNCHALLENGED
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> POST /v2/trade/check/filequery HTTP/1.1
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> sdk_version: javaSDK_3.0.27
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Content-type: application/json
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Content-Length: 911
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Content-Encoding: UTF-8
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Host: api.huifu.com
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Connection: Keep-Alive
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> User-Agent: Apache-HttpClient/4.5.9 (Java/21.0.7)
[event-1:535]2025-08-18 16:28:48.333 DEBUG [headers:] - http-outgoing-0 >> Accept-Encoding: gzip,deflate
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "POST /v2/trade/check/filequery HTTP/1.1[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "sdk_version: javaSDK_3.0.27[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Content-type: application/json[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Content-Length: 911[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Content-Encoding: UTF-8[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Host: api.huifu.com[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Connection: Keep-Alive[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "User-Agent: Apache-HttpClient/4.5.9 (Java/21.0.7)[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "Accept-Encoding: gzip,deflate[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "[\r][\n]"
[event-1:535]2025-08-18 16:28:48.333 DEBUG [wire:] - http-outgoing-0 >> "{"sys_id":"6666000171871750","data":{"file_date":"20250816","req_seq_id":"dayReconciled20250818162848057","req_date":"20250818","sign":"FwaFxQYQ9nJYYJLYh8wqr7Ez7J3zTZPprreakaS0/pIbGkxAFdchnCgT4SdHMrkgF1Bm5ytOn9wkfwPhyxvglWu9fsPfk1YjgU6MunA+q8wGSvPXoOeoaJCIwAjues38dPNsXmqXR2nIs9r1iT65ipicAS5VY/8UF+PbuAg/NQxH81wo1Taxvg7/XP9teRNalZobGkSZYqfEkiVIJOgDhowblSMTQMd9s2qKw+gGWsRvRA0kJ5e+0Lx/NQUdJJvMP6NuMwZetgQVFwBC1lO3G0E75dIauWbgum9iXb46JqSIt7E53gPKfEB+RjAss6uKy1SoXluDEntncq0Iak2+qQ==","bill_type":"TRADE_BILL","huifu_id":"6666000171871750"},"product_id":"HSK","sign":"DVDyOC70gDiWC94U4syjeKmF6VZO7VhzgeUs0Ue0XgHoSULONjv7FUWAvMOfkV73RFWUlI0YX8TCTSi+TxfgkiX63oAPblVRPHruC6cwxl6dN2OA53XuIKf5Dh/EtTh/kYLNRIecMUn8EnCC/3ZW05YnwPWOxNXy98lv+DQkgsK+DB37si8rFHbRH36e4tU06wRVBPMFYDl71L7/BoEUJj12R5IWYvTyU5Q2/gLFYptVEDLZhaPegGtTRAXy2Allg1q5R9v2gYFQ63o51qbxqYqJMlLcHUpme/p97ecPMDLxytYy9MvDzwRJooZwDdozefKc6d/c+PGNTst3Z8cI/g=="}"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "HTTP/1.1 200 OK[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Date: Mon, 18 Aug 2025 08:28:48 GMT[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Content-Type: application/json[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Transfer-Encoding: chunked[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Connection: keep-alive[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Set-Cookie: aliyungf_tc=0eedcc6964c76e7b0d2f5197a535b88ff6770613d0862c6fb7ff66b0f42d1201; Path=/; HttpOnly[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Set-Cookie: acw_tc=ac11000117555057282381874e6779f51ab496d24b3c6de897fbd424717593;path=/;HttpOnly;Max-Age=1800[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "X-Powered-By: Undertow/1[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "_calHttpCallServerDomain: top-proxy-ser[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "$osp_http_seq$: 0[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Content-Encoding: gzip[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "Strict-Transport-Security: max-age=31536000[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "33b[\r][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "[0x1f][0x8b][0x8][0x0][0x0][0x0][0x0][0x0][0x0][0x0][0xec]SMo[0xab]F[0x14][0xfd]/H[0xef]m[0x12][0xc2][0xcc][0xf0]5D[0xb2]*[0xfc]A[0xe2][0xd8]N[0x8c][0x1]?CU!`[0x6][0x9b][0x18][0x3]ap[0xb0][0x1d]e_u[0xd5][0xf0][0xa4]J][0xf7]W[0xf5][0xa9]?[0xa3]C[0xfa][0xac]F[0xaf][0x9b].[0xbb]([0x12][0x8b]9s[0xef][0x99]{[0xef]9[0xf7]E Q[0x13][0x9][0xd7]/B[0x9a][0xe5]4$[0xb4][0x89][0xb2][0x9c][0x9][0xd7][0xdf][0xbf][0x8]q[0x96][0xe7]as[0xac][0xa8]p-[0xb8][0xb]s8[\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "[0xfb][0xe3][0xe9]T[0xb8][0x14]H[0xd9][0x16]y[0x19][0x91]p_[0xe7][0xfc]n[0xd3]4[0x15][0xbb][0x96][0xa4][0x8e][0xe0]*[0xc9][0xcb]=[0xa9][0x8a][0xfa]*)wRTU"[0xd6]0[0x2][0x11]HE[0x1c]CYTt#[0x16]#E[0xd3][0xc4][0x18]i0Jud[0x0][0xa2]J,j[0xf6]u[0xd1][0x11]H*J[\r][0xaa]P[0xd5][0xc0][0x11]V[0x90]J[0xb1][0x8e][0xa8][0x81][0x15]Y[0x87][0x18]i[0x1c][0x91][0x88][0x1e][0x1][0xaa]#E[0xa4][0x80]`Q[0xd1]RE[0xc4]0[0xa1]"[0x82][0x18]@JdHd[0xe3][0xea][0x94]U[0xdf][0x8d][0xe]UVS[0xd6][0x83]:[0xd6][0x81][0x2]u[0x84]?>8[0x8e][0x99]$[0x94][0xb1][0x9]=[0x8e]Io[0xea][0x9a]c[0xcd]?=[0x19][0xcd][0xd8]wT}[0xf3][0xd1][0xc9][0xd6]EW[\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "[0xed]5[0xae][0xf9]l?[0xae]S[0x16][0xa7][0x1e][0x91]o[0xdd][0xc3]"[0x8][0xc];.? k[0xcc]>[0xc8]C>[0x86][0xbf][0x6][0x16]5[0xdd]|[0x10]@*[0xc0]P;[0xc3][0x19][0xe1] DX[0xd3]4[0xca][7[0x14]$[0xca]TS[0xc4]X[0xc1]HTcLc[0x8d][0x10]EN[0xc9]9[0xa1][0x88]v[0xef]yB[0x9e][0xa8][0x1][0x0] o[[0x87][0xba][\n]"
[event-1:535]2025-08-18 16:28:48.531 DEBUG [wire:] - http-outgoing-0 << "[0xc2][0xb2]&[0xb4][0xee][0xfa]:g|UF[0xe6][0xe7][0xcd]>K[0xf7][0xe1][0xdb][0x9b][0xdf][0xe6][0xf1][0xdb]&[0xdb][0xd1][0xb3][0x8e][0xc2][0xeb][0xf][0x97]BM[0x9f]B[0xae][0xfa][0xbb][0xf7]0[0xf][0xeb]P[0xc6][0xff]7[0x1a][0x12][0x1d][0x17]4)[0x8b][0x84]KB[0xbe]6[0xc7][0xdb]CX[0xc1]@[0xd5][0xdf][0xa2]Y[0x15]&%[0xe9]Hx[0x9d]o[0xdf][0x19]&[0x94]%[0x1c][0xfe][0xf2][0xf9][0xd7]?~[0xfb][0xe5][0xcb][0x8f]?[0xff][0xfe][0xd3][0xe7][0xae][0x8c][0x88]m[0xff][0xa5][0xbf][0xb8]#[0xbf])P[0xe5][0x4][0xff][0xdb][0xee]o[0xdb][0xfd][0xa7][0xfc][0xd5][0x9]K[0xb][0x12]vF{[0xb7][0xb][0x10][0xc8]HEg[0xe5]Y[0x13][0xd5][0xcd]?C[0x10]_[0xcc]w![\r][0xcf]w[0xb8]I_/[0x5][0xc6]w[0x91][0x9f][0xd6][0xe3][0x19][0xb3]Nm[0xa9][0xd4]s+Q[0xa5]S[0xec][0x1f][0x9a][0x19][0x95][0xee]VS[0xdc][0xda][0xf7][0xc5][0xce][0xa8][0x94][0xed][0xa7]Y[0xb5]P[0xa7]e[0xb0][0xb8][0x3][0xd6][0xc0][0x1c][0xb5][0xeb][0xbb][0xc]>[0xed][0x83]uk[0xc3][0xe9]p[0x9]7P[0xcb][0x87][0xa6][0xfe]h[0x96][0x4]o[0xdd]j[0xbe][0xc9]m[0xf7].[0xc8][0x80][0xa2]-[0x86]3}[0xb3]]F[0x8b][0xf4][0xb9][0x1d][0xe4][0xcf]ld]<[0xdc][0xf7][0x8f]Oj[0x1e][0xc0]UvH[\r]8[0xa3][0xea][0xc0][0xc7][0x17]Ki[0x95][0xf8][0x83][0xc5][0xe1][0x93]w[0xbc][0x1f]{[0xf6]M[0x19][0xdb]~[0xbb][0xc9]P[0xea]-u[0xad][0xbc][0x95]M/p[0x8b][0xe0]h[0xa5]v5[0xc4][0x92][0x15][0xf7][0xb3][0x81]|[0xb1]9)R[0xd6][0xbf][0xdf]g}[0xbf]=L[0x2][0xaa][0xce][0x83][0xa3][0xbd][0xf3][0x1e][0x6][0xf2][0x88][0x1c][0xa6]h4[0xf5][0xa0]k[0xdc][0xe8][0xa3][0xa5][0xed][0xd7]C[0xe7][0xd9][0xa9][0xa5]r[0xbd][0xc0][0xe6][0xa8][0xf][0x86][0x16][0xdd][0x90][0x19]17[0xee][0xa3][0xb3][0xd6]s=[0x8f][0xbc][0x9b][0xf9]jR[0xe4][0x9e][0xe7]O"'[0xf3]M[0x87][0x19]l;[0x1]m[0xb5]_%9[0x6][0x81]f[V[0x16][0x9b][0x8e][0xe1][0x4])[0xbb]3V[0xb7][0x9e]b[0x9c][0xca][0xbe][0xb3][0x9e][0xba][0xf1][0xcc][0x1c]=[0xce][0xe3]h[0x1c]L[0xf4]%s[0xe2]|[0xd6][0xf6]z[0xc2][0xeb][0x9f][0x0][0x0][0x0][0xff][0xff][0x3][0x0][0xa0][0xae][0x19][0x6][0xfb][0x5][0x0][0x0][\r][\n]"
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << HTTP/1.1 200 OK
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Date: Mon, 18 Aug 2025 08:28:48 GMT
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Content-Type: application/json
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Transfer-Encoding: chunked
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Connection: keep-alive
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Set-Cookie: aliyungf_tc=0eedcc6964c76e7b0d2f5197a535b88ff6770613d0862c6fb7ff66b0f42d1201; Path=/; HttpOnly
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Set-Cookie: acw_tc=ac11000117555057282381874e6779f51ab496d24b3c6de897fbd424717593;path=/;HttpOnly;Max-Age=1800
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << X-Powered-By: Undertow/1
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << _calHttpCallServerDomain: top-proxy-ser
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << $osp_http_seq$: 0
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Content-Encoding: gzip
[event-1:535]2025-08-18 16:28:48.533 DEBUG [headers:] - http-outgoing-0 << Strict-Transport-Security: max-age=31536000
[event-1:535]2025-08-18 16:28:48.536 DEBUG [MainClientExec:] - Connection can be kept alive indefinitely
[event-1:535]2025-08-18 16:28:48.540 DEBUG [ResponseProcessCookies:] - Cookie accepted [aliyungf_tc="0eedcc6964c76e7b0d2f5197a535b88ff6770613d0862c6fb7ff66b0f42d1201", version:0, domain:api.huifu.com, path:/, expiry:null]
[event-1:535]2025-08-18 16:28:48.540 DEBUG [ResponseProcessCookies:] - Cookie accepted [acw_tc="ac11000117555057282381874e6779f51ab496d24b3c6de897fbd424717593", version:0, domain:api.huifu.com, path:/, expiry:null]
[event-1:535]2025-08-18 16:28:48.543 DEBUG [wire:] - http-outgoing-0 << "0[\r][\n]"
[event-1:535]2025-08-18 16:28:48.543 DEBUG [wire:] - http-outgoing-0 << "[\r][\n]"
[event-1:535]2025-08-18 16:28:48.543 DEBUG [DefaultManagedHttpClientConnection:] - http-outgoing-0: set socket timeout to 0
[event-1:535]2025-08-18 16:28:48.543 DEBUG [DefaultManagedHttpClientConnection:] - http-outgoing-0: Close connection
[NettyClientPublicExecutor_6:397]2025-08-18 16:28:48.725 INFO  [AbstractDispatchMessageListener:] - The total time of processing 1083ms
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:28:54.140 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:29:00.932 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:29:21.038 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:29:24.117 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:29:46.175 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:29:51.316 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:30:14.102 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:30:17.288 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:30:40.560 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:30:45.272 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:31:09.206 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:31:13.210 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:31:35.099 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:31:41.902 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:32:03.112 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:32:05.725 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[quartzScheduler_QuartzSchedulerThread:137]2025-08-18 16:32:29.048 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
[DefaultQuartzScheduler_QuartzSchedulerThread:155]2025-08-18 16:32:34.972 DEBUG [QuartzSchedulerThread:] - batch acquisition of 0 triggers
