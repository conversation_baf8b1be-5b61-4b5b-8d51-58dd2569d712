package com.xk.ewd.infrastructure.data.persistence.logistics;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.logistics.OAppAddr;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsOrder;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsUserView;

/**
 * <AUTHOR>
 * @description 针对表【o_logistics_order(订单表)】的数据库操作Mapper
 * @createDate 2025-07-10 16:25:52
 * @Entity com.xk.ewd.infrastructure.data.po.logistics.DLogisticsOrder
 */
@Repository
@Table(value = "o_logistics_order", ewdAsyncEnabled = false)
public interface OLogisticsOrderMapper {

    int deleteByPrimaryKey(OLogisticsOrder record);

    int insert(OLogisticsOrder record);

    int insertSelective(OLogisticsOrder record);

    OLogisticsOrder selectByPrimaryKey(OLogisticsOrder record);

    int updateByPrimaryKeySelective(OLogisticsOrder record);

    int updateByPrimaryKey(OLogisticsOrder record);

    List<OLogisticsOrder> selectList(Pagination pagination);

    List<OAppAddr> addr(OLogisticsOrder record);

    List<OLogisticsOrder> giftCorpsOrder(Pagination pagination);

    List<OLogisticsUserView> userViewCorp(Pagination pagination);

    Long getWeekTotalRecords();

    List<OLogisticsOrder> getWeekRecords(Pagination pagination);

    int updateByOrderNo(OLogisticsOrder record);
}
