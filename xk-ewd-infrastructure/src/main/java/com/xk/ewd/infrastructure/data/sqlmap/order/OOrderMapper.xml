<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.order.OOrderMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.order.OOrder">
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="freeQuotaDiscountAmount" column="free_quota_discount_amount" jdbcType="BIGINT"/>
        <result property="logisticsOrderId" column="logistics_order_id" jdbcType="BIGINT"/>
        <result property="payNo" column="pay_no" jdbcType="VARCHAR"/>
        <result property="logisticsCorpName" column="logistics_corp_name" jdbcType="VARCHAR"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="shippingFee" column="shipping_fee" jdbcType="BIGINT"/>
        <result property="goodsInfo" column="goods_info" jdbcType="VARCHAR"/>
        <result property="giftInfo" column="gift_info" jdbcType="VARCHAR"/>
        <result property="receivingMobile" column="receiving_mobile" jdbcType="VARCHAR"/>
        <result property="orderTotalBuyCount" column="order_total_buy_count" jdbcType="INTEGER"/>
        <result property="totalAmount" column="total_amount" jdbcType="BIGINT"/>
        <result property="discountAmount" column="discount_amount" jdbcType="BIGINT"/>
        <result property="corpDicountAmount" column="corp_dicount_amount" jdbcType="BIGINT"/>
        <result property="couponAmount" column="coupon_amount" jdbcType="BIGINT"/>
        <result property="otherDicountAmount" column="other_dicount_amount" jdbcType="BIGINT"/>
        <result property="payAmount" column="pay_amount" jdbcType="BIGINT"/>
        <result property="corpId" column="corp_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="consigneeName" column="consignee_name" jdbcType="VARCHAR"/>
        <result property="userNick" column="user_nick" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="userAddressId" column="user_address_id" jdbcType="BIGINT"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR"/>
        <result property="addressSite" column="address_site" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="TINYINT"/>
        <result property="logisticsOrderType" column="logistics_order_type" jdbcType="TINYINT"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="payType" column="pay_type" jdbcType="TINYINT"/>
        <result property="platformType" column="platform_type" jdbcType="TINYINT"/>
        <result property="remindShippingStatus" column="remind_shipping_status" jdbcType="TINYINT"/>
        <result property="orderStatus" column="order_status" jdbcType="TINYINT"/>
        <result property="logisticsOrderStatus" column="logistics_order_status" jdbcType="INTEGER"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="refundStatus" column="refund_status" jdbcType="INTEGER"/>
        <result property="sendGoodsTime" column="send_goods_time" jdbcType="TIMESTAMP"/>
        <result property="shipTotalCount" column="ship_total_count" jdbcType="INTEGER"/>
        <result property="prizeStatus" column="prize_status" jdbcType="INTEGER"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsId" column="goods_id" jdbcType="VARCHAR"/>
        <result property="productType" column="product_type" jdbcType="VARCHAR"/>
        <result property="payStatus" column="pay_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_no
        ,create_time,free_quota_discount_amount,
        logistics_order_id,pay_no,logistics_corp_name,
        logistics_no,shipping_fee,goods_info,
        gift_info,receiving_mobile,order_total_buy_count,
        total_amount,discount_amount,corp_dicount_amount,
        coupon_amount,other_dicount_amount,pay_amount,
        corp_id,user_id,consignee_name,
        user_nick,mobile,user_address_id,
        corp_name,address_site,address_detail,
        order_type,logistics_order_type,deleted,
        pay_type,platform_type,remind_shipping_status,order_status,logistics_order_status,
        pay_time,refund_status,send_goods_time,
        ship_total_count,prize_status,goods_name,
        goods_id,product_type,pay_status
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order
        where
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrder">
        delete
        from o_order
        where
    </delete>
    <insert id="insert">
        insert into o_order
        ( order_no, create_time, free_quota_discount_amount
        , logistics_order_id, pay_no, logistics_corp_name
        , logistics_no, shipping_fee, goods_info
        , gift_info, receiving_mobile, order_total_buy_count
        , total_amount, discount_amount, corp_dicount_amount
        , coupon_amount, other_dicount_amount, pay_amount
        , corp_id, user_id, consignee_name
        , user_nick, mobile, user_address_id
        , corp_name, address_site, address_detail
        , order_type, logistics_order_type, deleted
        , pay_type, platform_type, remind_shipping_status, order_status, logistics_order_status
        , pay_time, refund_status, send_goods_time
        , ship_total_count, prize_status, goods_name
        , goods_id, product_type, pay_status)
        values ( #{orderNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
               , #{freeQuotaDiscountAmount,jdbcType=BIGINT}
               , #{logisticsOrderId,jdbcType=BIGINT}, #{payNo,jdbcType=VARCHAR}, #{logisticsCorpName,jdbcType=VARCHAR}
               , #{logisticsNo,jdbcType=VARCHAR}, #{shippingFee,jdbcType=BIGINT}, #{goodsInfo,jdbcType=VARCHAR}
               , #{giftInfo,jdbcType=VARCHAR}, #{receivingMobile,jdbcType=VARCHAR}
               , #{orderTotalBuyCount,jdbcType=INTEGER}
               , #{totalAmount,jdbcType=BIGINT}, #{discountAmount,jdbcType=BIGINT}, #{corpDicountAmount,jdbcType=BIGINT}
               , #{couponAmount,jdbcType=BIGINT}, #{otherDicountAmount,jdbcType=BIGINT}, #{payAmount,jdbcType=BIGINT}
               , #{corpId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{consigneeName,jdbcType=VARCHAR}
               , #{userNick,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{userAddressId,jdbcType=BIGINT}
               , #{corpName,jdbcType=VARCHAR}, #{addressSite,jdbcType=VARCHAR}, #{addressDetail,jdbcType=VARCHAR}
               , #{orderType,jdbcType=TINYINT}, #{logisticsOrderType,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}
               , #{payType,jdbcType=TINYINT}, #{platformType,jdbcType=TINYINT}, #{remindShippingStatus,jdbcType=TINYINT}
               , #{orderStatus,jdbcType=TINYINT}
               , #{logisticsOrderStatus,jdbcType=INTEGER}
               , #{payTime,jdbcType=TIMESTAMP}, #{refundStatus,jdbcType=INTEGER}, #{sendGoodsTime,jdbcType=TIMESTAMP}
               , #{shipTotalCount,jdbcType=INTEGER}, #{prizeStatus,jdbcType=INTEGER}, #{goodsName,jdbcType=VARCHAR}
               , #{goodsId,jdbcType=VARCHAR}, #{productType,jdbcType=VARCHAR}, #{payStatus,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective">
        insert into o_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="createTime != null">create_time,</if>
            <if test="freeQuotaDiscountAmount != null">free_quota_discount_amount,</if>
            <if test="logisticsOrderId != null">logistics_order_id,</if>
            <if test="payNo != null">pay_no,</if>
            <if test="logisticsCorpName != null">logistics_corp_name,</if>
            <if test="logisticsNo != null">logistics_no,</if>
            <if test="shippingFee != null">shipping_fee,</if>
            <if test="goodsInfo != null">goods_info,</if>
            <if test="giftInfo != null">gift_info,</if>
            <if test="receivingMobile != null">receiving_mobile,</if>
            <if test="orderTotalBuyCount != null">order_total_buy_count,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="discountAmount != null">discount_amount,</if>
            <if test="corpDicountAmount != null">corp_dicount_amount,</if>
            <if test="couponAmount != null">coupon_amount,</if>
            <if test="otherDicountAmount != null">other_dicount_amount,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="corpId != null">corp_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="consigneeName != null">consignee_name,</if>
            <if test="userNick != null">user_nick,</if>
            <if test="mobile != null">mobile,</if>
            <if test="userAddressId != null">user_address_id,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="addressSite != null">address_site,</if>
            <if test="addressDetail != null">address_detail,</if>
            <if test="orderType != null">order_type,</if>
            <if test="logisticsOrderType != null">logistics_order_type,</if>
            <if test="deleted != null">deleted,</if>
            <if test="payType != null">pay_type,</if>
            <if test="platformType != null">platform_type,</if>
            <if test="remindShippingStatus != null">remind_shipping_status,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="logisticsOrderStatus != null">logistics_order_status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="sendGoodsTime != null">send_goods_time,</if>
            <if test="shipTotalCount != null">ship_total_count,</if>
            <if test="prizeStatus != null">prize_status,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="productType != null">product_type,</if>
            <if test="payStatus != null">pay_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="freeQuotaDiscountAmount != null">#{freeQuotaDiscountAmount,jdbcType=BIGINT},</if>
            <if test="logisticsOrderId != null">#{logisticsOrderId,jdbcType=BIGINT},</if>
            <if test="payNo != null">#{payNo,jdbcType=VARCHAR},</if>
            <if test="logisticsCorpName != null">#{logisticsCorpName,jdbcType=VARCHAR},</if>
            <if test="logisticsNo != null">#{logisticsNo,jdbcType=VARCHAR},</if>
            <if test="shippingFee != null">#{shippingFee,jdbcType=BIGINT},</if>
            <if test="goodsInfo != null">#{goodsInfo,jdbcType=VARCHAR},</if>
            <if test="giftInfo != null">#{giftInfo,jdbcType=VARCHAR},</if>
            <if test="receivingMobile != null">#{receivingMobile,jdbcType=VARCHAR},</if>
            <if test="orderTotalBuyCount != null">#{orderTotalBuyCount,jdbcType=INTEGER},</if>
            <if test="totalAmount != null">#{totalAmount,jdbcType=BIGINT},</if>
            <if test="discountAmount != null">#{discountAmount,jdbcType=BIGINT},</if>
            <if test="corpDicountAmount != null">#{corpDicountAmount,jdbcType=BIGINT},</if>
            <if test="couponAmount != null">#{couponAmount,jdbcType=BIGINT},</if>
            <if test="otherDicountAmount != null">#{otherDicountAmount,jdbcType=BIGINT},</if>
            <if test="payAmount != null">#{payAmount,jdbcType=BIGINT},</if>
            <if test="corpId != null">#{corpId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="consigneeName != null">#{consigneeName,jdbcType=VARCHAR},</if>
            <if test="userNick != null">#{userNick,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="userAddressId != null">#{userAddressId,jdbcType=BIGINT},</if>
            <if test="corpName != null">#{corpName,jdbcType=VARCHAR},</if>
            <if test="addressSite != null">#{addressSite,jdbcType=VARCHAR},</if>
            <if test="addressDetail != null">#{addressDetail,jdbcType=VARCHAR},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="logisticsOrderType != null">#{logisticsOrderType,jdbcType=TINYINT},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="platformType != null">#{platformType,jdbcType=TINYINT},</if>
            <if test="remindShippingStatus != null">#{remindShippingStatus,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="logisticsOrderStatus != null">#{logisticsOrderStatus,jdbcType=INTEGER},</if>
            <if test="payTime != null">#{payTime,jdbcType=TIMESTAMP},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=INTEGER},</if>
            <if test="sendGoodsTime != null">#{sendGoodsTime,jdbcType=TIMESTAMP},</if>
            <if test="shipTotalCount != null">#{shipTotalCount,jdbcType=INTEGER},</if>
            <if test="prizeStatus != null">#{prizeStatus,jdbcType=INTEGER},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="productType != null">#{productType,jdbcType=VARCHAR},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=INTEGER},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrder">
        update o_order
        <set>
            <if test="freeQuotaDiscountAmount != null">
                free_quota_discount_amount = #{freeQuotaDiscountAmount,jdbcType=BIGINT},
            </if>
            <if test="logisticsOrderId != null">
                logistics_order_id = #{logisticsOrderId,jdbcType=BIGINT},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCorpName != null">
                logistics_corp_name = #{logisticsCorpName,jdbcType=VARCHAR},
            </if>
            <if test="logisticsNo != null">
                logistics_no = #{logisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="shippingFee != null">
                shipping_fee = #{shippingFee,jdbcType=BIGINT},
            </if>
            <if test="goodsInfo != null">
                goods_info = #{goodsInfo,jdbcType=VARCHAR},
            </if>
            <if test="giftInfo != null">
                gift_info = #{giftInfo,jdbcType=VARCHAR},
            </if>
            <if test="receivingMobile != null">
                receiving_mobile = #{receivingMobile,jdbcType=VARCHAR},
            </if>
            <if test="orderTotalBuyCount != null">
                order_total_buy_count = #{orderTotalBuyCount,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=BIGINT},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount,jdbcType=BIGINT},
            </if>
            <if test="corpDicountAmount != null">
                corp_dicount_amount = #{corpDicountAmount,jdbcType=BIGINT},
            </if>
            <if test="couponAmount != null">
                coupon_amount = #{couponAmount,jdbcType=BIGINT},
            </if>
            <if test="otherDicountAmount != null">
                other_dicount_amount = #{otherDicountAmount,jdbcType=BIGINT},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=BIGINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="consigneeName != null">
                consignee_name = #{consigneeName,jdbcType=VARCHAR},
            </if>
            <if test="userNick != null">
                user_nick = #{userNick,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userAddressId != null">
                user_address_id = #{userAddressId,jdbcType=BIGINT},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="addressSite != null">
                address_site = #{addressSite,jdbcType=VARCHAR},
            </if>
            <if test="addressDetail != null">
                address_detail = #{addressDetail,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=TINYINT},
            </if>
            <if test="logisticsOrderType != null">
                logistics_order_type = #{logisticsOrderType,jdbcType=TINYINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=TINYINT},
            </if>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=TINYINT},
            </if>
            <if test="remindShippingStatus != null">
                remind_shipping_status = #{remindShippingStatus,jdbcType=TINYINT},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=TINYINT},
            </if>
            <if test="logisticsOrderStatus != null">
                logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundStatus != null">
                refund_status = #{refundStatus,jdbcType=INTEGER},
            </if>
            <if test="sendGoodsTime != null">
                send_goods_time = #{sendGoodsTime,jdbcType=TIMESTAMP},
            </if>
            <if test="shipTotalCount != null">
                ship_total_count = #{shipTotalCount,jdbcType=INTEGER},
            </if>
            <if test="prizeStatus != null">
                prize_status = #{prizeStatus,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus,jdbcType=INTEGER},
            </if>
        </set>
        where order_no = #{orderNo,jdbcType=VARCHAR} and create_time = #{createTime,jdbcType=TIMESTAMP}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrder">
        update o_order
        set free_quota_discount_amount = #{freeQuotaDiscountAmount,jdbcType=BIGINT},
            logistics_order_id         = #{logisticsOrderId,jdbcType=BIGINT},
            pay_no                     = #{payNo,jdbcType=VARCHAR},
            logistics_corp_name        = #{logisticsCorpName,jdbcType=VARCHAR},
            logistics_no               = #{logisticsNo,jdbcType=VARCHAR},
            shipping_fee               = #{shippingFee,jdbcType=BIGINT},
            goods_info                 = #{goodsInfo,jdbcType=VARCHAR},
            gift_info                  = #{giftInfo,jdbcType=VARCHAR},
            receiving_mobile           = #{receivingMobile,jdbcType=VARCHAR},
            order_total_buy_count      = #{orderTotalBuyCount,jdbcType=INTEGER},
            total_amount               = #{totalAmount,jdbcType=BIGINT},
            discount_amount            = #{discountAmount,jdbcType=BIGINT},
            corp_dicount_amount        = #{corpDicountAmount,jdbcType=BIGINT},
            coupon_amount              = #{couponAmount,jdbcType=BIGINT},
            other_dicount_amount       = #{otherDicountAmount,jdbcType=BIGINT},
            pay_amount                 = #{payAmount,jdbcType=BIGINT},
            corp_id = #{corpId,jdbcType=BIGINT},
            user_id                    = #{userId,jdbcType=BIGINT},
            consignee_name             = #{consigneeName,jdbcType=VARCHAR},
            user_nick                  = #{userNick,jdbcType=VARCHAR},
            mobile                     = #{mobile,jdbcType=VARCHAR},
            user_address_id            = #{userAddressId,jdbcType=BIGINT},
            corp_name                  = #{corpName,jdbcType=VARCHAR},
            address_site               = #{addressSite,jdbcType=VARCHAR},
            address_detail             = #{addressDetail,jdbcType=VARCHAR},
            order_type                 = #{orderType,jdbcType=TINYINT},
            logistics_order_type       = #{logisticsOrderType,jdbcType=TINYINT},
            deleted                    = #{deleted,jdbcType=TINYINT},
            pay_type                   = #{payType,jdbcType=TINYINT},
            platform_type              = #{platformType,jdbcType=TINYINT},
            remind_shipping_status     = #{remindShippingStatus,jdbcType=TINYINT},
            order_status               = #{orderStatus,jdbcType=TINYINT},
            logistics_order_status     = #{logisticsOrderStatus,jdbcType=INTEGER},
            pay_time                   = #{payTime,jdbcType=TIMESTAMP},
            refund_status              = #{refundStatus,jdbcType=INTEGER},
            send_goods_time            = #{sendGoodsTime,jdbcType=TIMESTAMP},
            ship_total_count           = #{shipTotalCount,jdbcType=INTEGER},
            prize_status               = #{prizeStatus,jdbcType=INTEGER},
            goods_name                 = #{goodsName,jdbcType=VARCHAR},
            goods_id                   = #{goodsId,jdbcType=VARCHAR},
            product_type               = #{productType,jdbcType=VARCHAR},
            pay_status                 = #{payStatus,jdbcType=INTEGER}
        where order_no = #{orderNo,jdbcType=VARCHAR}
          and create_time = #{createTime,jdbcType=TIMESTAMP}
    </update>

    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order
        <where>
            <trim prefixOverrides="AND">
                <if test="logisticsOrderStatus != null">
                    AND logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    AND order_no = #{orderNo,jdbcType=VARCHAR}
                </if>
                <if test="orderStatus != null">
                    AND order_status = #{orderStatus,jdbcType=INTEGER}
                </if>
                <if test="searchOrderNo != null and searchOrderNo != ''">
                    AND (order_no = #{searchOrderNo,jdbcType=VARCHAR} OR logistics_order_id
                    = #{searchOrderNo,jdbcType=BIGINT} )
                </if>
                <if test="corpId != null">
                    AND corp_id = #{corpId,jdbcType=BIGINT}
                </if>
                <if test="corpName != null and corpName != ''">
                    AND corp_name like concat('%', #{corpName,jdbcType=VARCHAR}, '%')
                </if>
                <if test="logisticsOrderType != null">
                    AND logistics_order_type = #{logisticsOrderType,jdbcType=INTEGER}
                </if>
                <if test="userId != null">
                    AND user_id = #{userId,jdbcType=BIGINT}
                </if>
                <if test="userNick != null and userNick != ''">
                    AND user_nick = #{userNick,jdbcType=VARCHAR}
                </if>
                <if test="userName != null and userName != ''">
                    AND user_name = #{userName,jdbcType=VARCHAR}
                </if>
                <if test="mobile != null and mobile != ''">
                    AND mobile = #{mobile,jdbcType=VARCHAR}
                </if>
                <if test="goodsId != null">
                    AND goods_id like concat('%', #{goodsId,jdbcType=VARCHAR}, '%')
                </if>
                <if test="goodsName != null and goodsName != ''">
                    AND goods_name like concat('%', #{goodsName,jdbcType=VARCHAR}, '%')
                </if>
                <if test="productType != null">
                    AND product_type like concat('%', #{productType,jdbcType=VARCHAR}, '%')
                </if>
                <if test="prizeStatus != null">
                    AND prize_status = #{prizeStatus,jdbcType=VARCHAR}
                </if>
                <if test="logisticsCorpId != null">
                    AND logistics_corp_info_id = #{logisticsCorpId,jdbcType=BIGINT}
                </if>
                <if test="logisticsNo != null and logisticsNo != ''">
                    AND logistics_no = #{logisticsNo,jdbcType=VARCHAR}
                </if>
                <if test="orderType != null">
                    AND order_type = #{orderType,jdbcType=VARCHAR}
                </if>
                <if test="payType != null">
                    AND pay_type = #{payType,jdbcType=VARCHAR}
                </if>
                <if test="startTime != null">
                    AND create_time >= #{startTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    AND create_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="refundStatus != null">
                    AND refund_status = #{refundStatus,jdbcType=INTEGER}
                </if>
                <if test="platformType != null">
                    AND platform_type = #{platformType,jdbcType=INTEGER}
                </if>
                <if test="remindShippingStatus != null">
                    AND remind_shipping_status = #{remindShippingStatus,jdbcType=INTEGER}
                </if>
                <if test="deleted == null">
                    AND deleted = 0
                </if>
            </trim>
        </where>
        <if test="sort != null">
            order by ${sort}
            <if test="order == 'ASC'">
                ASC
            </if>
            <if test="order == 'DESC'">
                DESC
            </if>
        </if>
    </select>
    <select id="searchStatistics" resultType="com.xk.ewd.domain.dto.order.OrderStatisticsDto">
        select
        COUNT(CASE WHEN 1 = 1 THEN 1 END) AS totalCount,
        COUNT(CASE WHEN order_status in ('2', '3', '4') THEN 1 END) AS payCount,
        COUNT(CASE WHEN refund_status = '2' THEN 1 END) AS refundCount,
        COUNT(CASE WHEN order_status = '1' THEN 1 END) AS waitPayCount
        from o_order
        <where>
            <trim prefixOverrides="AND">
                <if test="orderType != null">
                    AND order_type = #{orderType,jdbcType=BIGINT}
                </if>
                <if test="corpId != null">
                    AND corp_id = #{corpId,jdbcType=VARCHAR}
                </if>
                <if test="startTime != null">
                    AND create_time >= #{startTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    AND create_time <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="deleted == null">
                    AND deleted = 0
                </if>
            </trim>
        </where>
    </select>
</mapper>
