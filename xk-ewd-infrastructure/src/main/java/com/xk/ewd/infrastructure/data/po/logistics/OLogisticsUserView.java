package com.xk.ewd.infrastructure.data.po.logistics;

import com.xk.ewd.domain.dto.logistics.LogisticsUserViewDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsUserViewDto.class)})
public class OLogisticsUserView {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

    /**
     * 地址id
     */
    private Long userAddressId;

    /**
     * 收货地址 省市区
     */
    private String addressSite;

    /**
     * 收货地址 详细地址
     */
    private String addressDetail;

    /**
     * 待发货数量
     */
    private Integer waitShipNum;

}
