package com.xk.ewd.infrastructure.data.po.payment;

import com.myco.framework.cache.annotations.KeyProperty;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.infrastructure.convertor.payment.PaymentPayTypeEnumConvertor;
import com.xk.ewd.infrastructure.convertor.payment.RefundStatusEnumConvertor;
import com.xk.ewd.infrastructure.convertor.payment.RefundTypeEnumConvertor;
import com.xk.infrastructure.convertor.common.CommonStatusEnumConvertor;
import com.xk.infrastructure.convertor.commons.PlatformTypeEnumConvertor;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({
        @AutoMapper(target = RefundEntity.class,uses = {PlatformTypeEnumConvertor.class, CommonStatusEnumConvertor.class, RefundStatusEnumConvertor.class, PaymentPayTypeEnumConvertor.class, RefundTypeEnumConvertor.class})
})
public class ORefund {
    /**
     * 收付单号
     */
    @KeyProperty
    private Long paymentId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 退款类型
     */
    private Integer refundType;

    /**
     * 退款状态
     */
    private Integer refundStatus;

    /**
     * 发起平台
     */
    private Integer platformType;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 收款完成时间
     */
    private Date payTime;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商户id
     */
    private Long corpId;

    /**
     * 商户名
     */
    private String corpName;

    /**
     * 收款金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;
}
