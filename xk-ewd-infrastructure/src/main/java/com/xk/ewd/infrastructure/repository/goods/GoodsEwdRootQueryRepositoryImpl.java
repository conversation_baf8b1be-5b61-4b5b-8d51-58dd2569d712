package com.xk.ewd.infrastructure.repository.goods;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.goods.entity.GoodsEwdEntity;
import com.xk.ewd.domain.model.goods.valobj.StatusCountValObj;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.goods.GGoodsMapper;
import com.xk.ewd.infrastructure.data.po.goods.GGoods;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

@Repository
@RequiredArgsConstructor
public class GoodsEwdRootQueryRepositoryImpl implements GoodsEwdRootQueryRepository {

    private final GGoodsMapper gGoodsMapper;
    private final Converter converter;

    @Override
    public Flux<GoodsEwdEntity> searchPager(Pagination pagination) {
        return this.search(pagination, gGoodsMapper::selectByPage, GoodsEwdEntity.class,
                converter::convert);
    }

    @Override
    public Flux<StatusCountValObj> statusCount(GoodsEwdEntity ewdEntity) {
        return Flux
                .fromIterable(gGoodsMapper
                        .statusCount(GGoods.builder().productType(ewdEntity.getProductType())
                                .corpInfoId(ewdEntity.getCorpInfoId()).build()))
                .switchIfEmpty(Flux.empty());
    }

    @Override
    public Flux<GoodsEwdEntity> corpMerchantSearch(Pagination pagination) {
        return search(pagination, gGoodsMapper::selectListByCorp, GoodsEwdEntity.class,
                converter::convert);
    }

    @Override
    public Flux<GoodsEwdEntity> corpMerchantGoodsSearch(Pagination pagination) {
        return search(pagination, gGoodsMapper::searchCorpMerchantGoods, GoodsEwdEntity.class,
                converter::convert);
    }
}
