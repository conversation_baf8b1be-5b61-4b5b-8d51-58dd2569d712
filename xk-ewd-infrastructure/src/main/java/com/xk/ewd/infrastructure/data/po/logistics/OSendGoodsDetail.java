package com.xk.ewd.infrastructure.data.po.logistics;

import java.util.Date;

import com.xk.ewd.domain.dto.logistics.LogisticsDetailDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName o_send_goods_detail
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = LogisticsDetailDto.class)})
public class OSendGoodsDetail {
    /**
     * 物流订单id
     */
    private Long logisticsId;

    /**
     * 发货id
     */
    private Long sendGoodsId;

    /**
     * 物流事件时间
     */
    private Date trackTime;

    /**
     * 物流描述
     */
    private String trackDescribe;

    /**
     * 事件类型
     */
    private String trackType;
}
