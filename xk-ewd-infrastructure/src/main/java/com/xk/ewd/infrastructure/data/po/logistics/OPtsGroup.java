package com.xk.ewd.infrastructure.data.po.logistics;

import java.util.Date;

import com.xk.ewd.domain.dto.logistics.PtsGroupDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单表
 *
 * @TableName d_logistics_order
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = PtsGroupDto.class),})
public class OPtsGroup {

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 用户logo
     */
    private String userLogo;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 待发货订单数
     */
    private Long countNum;

    private Date createTime;

}
