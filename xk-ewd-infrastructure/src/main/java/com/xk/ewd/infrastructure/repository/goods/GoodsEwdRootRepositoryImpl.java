package com.xk.ewd.infrastructure.repository.goods;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.goods.GoodsEwdRoot;
import com.xk.ewd.domain.repository.goods.GoodsEwdRootRepository;
import com.xk.ewd.infrastructure.data.persistence.goods.GGoodsMapper;
import com.xk.ewd.infrastructure.data.po.goods.GGoods;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class GoodsEwdRootRepositoryImpl implements GoodsEwdRootRepository {

    private final GGoodsMapper gGoodsMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(GoodsEwdRoot root) {
        return save(root.getGoodsEwdEntity(), GGoods.class, converter::convert,
                gGoodsMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(GoodsEwdRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(GoodsEwdRoot root) {
        return update(root.getGoodsEwdEntity(), GGoods.class, converter::convert,
                gGoodsMapper::insertSelective);
    }

    @Override
    public Mono<Void> remove(GoodsEwdRoot root) {
        return null;
    }
}
