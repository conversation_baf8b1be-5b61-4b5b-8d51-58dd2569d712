<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.order.OOrderLogMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.order.OOrderLog">
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderMsg" column="order_msg" jdbcType="VARCHAR"/>
        <result property="reason" column="reason" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_no
        ,order_msg,reason,
        order_type,create_id,create_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrderLog"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_log
        where
    </select>
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_order_log
        where order_no = #{orderNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrderLog">
        delete
        from o_order_log
        where
    </delete>
    <insert id="insert">
        insert into o_order_log
        ( order_no, order_msg, reason
        , order_type, create_id, create_time)
        values ( #{orderNo,jdbcType=VARCHAR}, #{orderMsg,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}
               , #{orderType,jdbcType=INTEGER}, #{createId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective">
        insert into o_order_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="orderMsg != null">order_msg,</if>
            <if test="reason != null">reason,</if>
            <if test="orderType != null">order_type,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="orderMsg != null">#{orderMsg,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderType != null">#{orderType,jdbcType=INTEGER},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrderLog">
        update o_order_log
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderMsg != null">
                order_msg = #{orderMsg,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.order.OOrderLog">
        update o_order_log
        set order_no    = #{orderNo,jdbcType=VARCHAR},
            order_msg   = #{orderMsg,jdbcType=VARCHAR},
            reason      = #{reason,jdbcType=VARCHAR},
            order_type  = #{orderType,jdbcType=INTEGER},
            create_id   = #{createId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where
    </update>
</mapper>
