package com.xk.ewd.infrastructure.data.persistence.logistics;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateRspDto;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail;

/**
 * <AUTHOR>
 * @description 针对表【o_logistics_import_detail】的数据库操作Mapper
 * @createDate 2025-07-15 14:33:28
 * @Entity com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail
 */
@Repository
@Table(value = "o_logistics_import_detail", ewdAsyncEnabled = false)
public interface OLogisticsImportDetailMapper {

    int deleteByPrimaryKey(OLogisticsImportDetail record);

    int insert(OLogisticsImportDetail record);

    int insertSelective(OLogisticsImportDetail record);

    OLogisticsImportDetail selectByPrimaryKey(OLogisticsImportDetail record);

    int updateByPrimaryKeySelective(OLogisticsImportDetail record);

    int updateByPrimaryKey(OLogisticsImportDetail record);

    List<OLogisticsImportDetail> importDetail(Pagination pagination);

    void generate(OLogisticsImportDetail record);

    List<FileTaskStatusUpdateRspDto> selectCount(OLogisticsImportDetail record);
}
