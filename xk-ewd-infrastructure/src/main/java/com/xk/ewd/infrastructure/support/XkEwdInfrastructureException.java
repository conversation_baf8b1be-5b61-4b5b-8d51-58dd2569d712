package com.xk.ewd.infrastructure.support;

import com.xk.ewd.infrastructure.commons.XkEwdInfrastructureErrorEnum;
import com.myco.mydata.domain.model.commons.SystemLanguageLocale;
import com.myco.mydata.domain.model.exception.wrapper.InfrastructureWrapperThrowable;

/**
 * @author: killer
 **/
public class XkEwdInfrastructureException extends InfrastructureWrapperThrowable {

    public XkEwdInfrastructureException(XkEwdInfrastructureErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkEwdInfrastructureException(XkEwdInfrastructureErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

}
