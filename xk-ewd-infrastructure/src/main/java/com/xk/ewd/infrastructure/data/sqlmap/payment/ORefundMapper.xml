<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.payment.ORefundMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.payment.ORefund">
        <id column="payment_id" property="paymentId" jdbcType="BIGINT"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="pay_no" property="payNo" jdbcType="VARCHAR" />
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="refund_type" property="refundType" jdbcType="INTEGER"/>
        <result column="refund_status" property="refundStatus" jdbcType="INTEGER"/>
        <result column="platform_type" property="platformType" jdbcType="INTEGER"/>
        <result column="pay_type" property="payType" jdbcType="INTEGER"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="refund_time" property="refundTime" jdbcType="TIMESTAMP"/>
        <result column="goods_id" property="goodsId" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="corp_id" property="corpId" jdbcType="BIGINT"/>
        <result column="corp_name" property="corpName" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="create_id" property="createId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
        payment_id,order_no,pay_no,user_id,refund_type,refund_status,username,platform_type,pay_type,pay_time,refund_time,goods_id,goods_name,corp_id,corp_name,amount,remark,deleted,create_id,create_time,update_time,order_type
    </sql>

    <insert id="insertRefund" parameterType="com.xk.ewd.infrastructure.data.po.payment.ORefund">
        insert into o_refund
        <trim prefix="(" suffixOverrides="," suffix=")">
            <if test="refund.paymentId != null">payment_id,</if>
            <if test="refund.orderNo != null">order_no,</if>
            <if test="refund.payNo != null">pay_no,</if>
            <if test="refund.userId != null">user_id,</if>
            <if test="refund.username != null">username,</if>
            <if test="refund.refundType != null">refund_type,</if>
            <if test="refund.refundStatus != null">refund_status,</if>
            <if test="refund.platformType != null">platform_type,</if>
            <if test="refund.payType != null">pay_type,</if>
            <if test="refund.payTime != null">pay_time,</if>
            <if test="refund.refundTime != null">refund_time,</if>
            <if test="refund.goodsId != null">goods_id,</if>
            <if test="refund.goodsName != null">goods_name,</if>
            <if test="refund.corpId != null">corp_id,</if>
            <if test="refund.corpName != null">corp_name,</if>
            <if test="refund.amount != null">amount,</if>
            <if test="refund.remark != null">remark,</if>
            <if test="refund.deleted != null">deleted,</if>
            <if test="refund.createId != null">create_id,</if>
            <if test="refund.createTime != null">create_time,</if>
            <if test="refund.updateTime != null">update_time,</if>
            <if test="refund.orderType != null">order_type,</if>
        </trim>
        <trim prefix="values (" suffixOverrides="," suffix=")">
            <if test="refund.paymentId != null">#{refund.paymentId},</if>
            <if test="refund.orderNo != null">#{refund.orderNo},</if>
            <if test="refund.payNo != null">#{refund.payNo},</if>
            <if test="refund.userId != null">#{refund.userId},</if>
            <if test="refund.username != null">#{refund.username},</if>
            <if test="refund.refundType != null">#{refund.refundType},</if>
            <if test="refund.refundStatus != null">#{refund.refundStatus},</if>
            <if test="refund.platformType != null">#{refund.platformType},</if>
            <if test="refund.payType != null">#{refund.payType},</if>
            <if test="refund.payTime != null">#{refund.payTime},</if>
            <if test="refund.refundTime != null">#{refund.refundTime},</if>
            <if test="refund.goodsId != null">#{refund.goodsId},</if>
            <if test="refund.goodsName != null">#{refund.goodsName},</if>
            <if test="refund.corpId != null">#{refund.corpId},</if>
            <if test="refund.corpName != null">#{refund.corpName},</if>
            <if test="refund.amount != null">#{refund.amount},</if>
            <if test="refund.remark != null">#{refund.remark},</if>
            <if test="refund.deleted != null">#{refund.deleted},</if>
            <if test="refund.createId != null">#{refund.createId},</if>
            <if test="refund.createTime != null">#{refund.createTime},</if>
            <if test="refund.updateTime != null">#{refund.updateTime},</if>
            <if test="refund.orderType != null">#{refund.orderType},</if>
        </trim>
    </insert>

    <update id="updateRefund" parameterType="com.xk.ewd.infrastructure.data.po.payment.ORefund">
        update o_refund
            <set>
                <if test="refund.refundStatus != null">
                    refund_status = #{refund.refundStatus},
                </if>
                <if test="refund.refundTime != null">
                    refund_time = #{refund.refundTime},
                </if>
                <if test="refund.updateTime != null">
                    update_time = #{refund.updateTime},
                </if>
            </set>
        where payment_id = #{refund.paymentId}
    </update>

    <select id="selectByPage" parameterType="com.myco.framework.support.mybatis.Pagination" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from
            o_refund
        where
            deleted = 0
        <if test="startPayTime != null">
            AND pay_time &gt;= #{startPayTime}
        </if>
        <if test="endPayTime != null">
            AND pay_time &lt;= #{endPayTime}
        </if>
        <if test="startRefundTime != null">
            AND refund_time &gt;= #{startRefundTime}
        </if>
        <if test="endRefundTime != null">
            AND refund_time &lt;= #{endRefundTime}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>
        <if test="goodsId != null and goodsId != ''">
            AND goods_id LIKE CONCAT('%', #{goodsId}, '%')
        </if>
        <if test="goodsName != null and goodsName != ''">
            AND goods_name LIKE CONCAT('%', #{goodsName}, '%')
        </if>
        <if test="corpId != null">
            AND corp_id = #{corpId}
        </if>
        <if test="corpName != null and corpName != ''">
            AND corp_name LIKE CONCAT('%', #{corpName}, '%')
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="payType != null">
            AND pay_type = #{payType}
        </if>
        <if test="refundStatus != null">
            AND refund_status = #{refundStatus}
        </if>
        <if test="refundType != null">
            AND refund_type = #{refundType}
        </if>
        <if test="orderType != null">
            AND order_type = #{orderType}
        </if>
    </select>
</mapper>