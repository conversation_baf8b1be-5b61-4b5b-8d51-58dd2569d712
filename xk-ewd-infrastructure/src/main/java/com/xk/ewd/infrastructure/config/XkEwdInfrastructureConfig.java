package com.xk.ewd.infrastructure.config;

import com.myco.mydata.infrastructure.data.annotation.Repository;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author: killer
 **/
@Configuration
@MapperScan(basePackages = "com.xk.ewd.infrastructure.data.persistence", annotationClass = Repository.class, sqlSessionFactoryRef = "defaultSqlSessionFactory")
@ComponentScan({"com.xk.ewd.infrastructure.convertor"
        , "com.xk.ewd.infrastructure.cache.dao"
        , "com.xk.ewd.infrastructure.repository"
        , "com.xk.ewd.infrastructure.service"
        , "com.xk.ewd.infrastructure.adapter"})
public class XkEwdInfrastructureConfig {
}
