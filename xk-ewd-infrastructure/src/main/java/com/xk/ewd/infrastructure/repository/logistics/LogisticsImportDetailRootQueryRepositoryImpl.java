package com.xk.ewd.infrastructure.repository.logistics;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateDto;
import com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateRspDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsImportDetailMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsImportDetailRootQueryRepositoryImpl
        implements LogisticsImportDetailRootQueryRepository {

    private final Converter converter;
    private final OLogisticsImportDetailMapper oLogisticsImportDetailMapper;


    @Override
    public Flux<LogisticsOrderRoot> importDetail(Pagination pagination) {
        return search(pagination, oLogisticsImportDetailMapper::importDetail,
                LogisticsOrderRoot.class, converter::convert);
    }

    @Override
    public Mono<List<FileTaskStatusUpdateRspDto>> selectCount(FileTaskStatusUpdateDto dto) {
        return Mono.just(oLogisticsImportDetailMapper
                .selectCount(OLogisticsImportDetail.builder().taskId(dto.getTaskId()).build()));
    }
}
