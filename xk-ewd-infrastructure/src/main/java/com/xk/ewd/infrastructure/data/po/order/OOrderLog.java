package com.xk.ewd.infrastructure.data.po.order;

import java.util.Date;

import com.xk.ewd.domain.model.order.entity.OrderLogEntity;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单日志表
 * 
 * @TableName o_order_log
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = OrderLogEntity.class)})
public class OOrderLog {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单消息
     */
    private String orderMsg;

    /**
     * 事件原因
     */
    private String reason;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;
}
