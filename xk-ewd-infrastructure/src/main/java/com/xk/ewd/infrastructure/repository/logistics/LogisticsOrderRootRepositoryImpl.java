package com.xk.ewd.infrastructure.repository.logistics;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsOrderMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsOrder;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsOrderRootRepositoryImpl implements LogisticsOrderRootRepository {

    private final Converter converter;
    private final OLogisticsOrderMapper oLogisticsOrderMapper;


    @Override
    public Mono<Void> save(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> save(order, OLogisticsOrder.class,
                (v, clazz) -> order, oLogisticsOrderMapper::insertSelective));
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(LogisticsOrderRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> update(order, OLogisticsOrder.class,
                (v, clazz) -> v, oLogisticsOrderMapper::updateByPrimaryKeySelective));
    }

    @Override
    public Mono<Void> remove(LogisticsOrderRoot root) {
        return null;
    }

    private @NotNull Mono<OLogisticsOrder> rootToOOrder(LogisticsOrderRoot root) {
        OLogisticsOrder order = new OLogisticsOrder();
        if (root.getCorpEntity() != null) {
            converter.convert(root.getCorpEntity(), order);
        }
        if (root.getOrderAddrEntity() != null) {
            converter.convert(root.getOrderAddrEntity(), order);
        }
        if (root.getOrderEntity() != null) {
            converter.convert(root.getOrderEntity(), order);
        }
        if (root.getUserEntity() != null) {
            converter.convert(root.getUserEntity(), order);
        }
        if (root.getGoodsValueObject() != null) {
            converter.convert(root.getGoodsValueObject(), order);
        }
        if (root.getLogisticsOrderEntity() != null) {
            converter.convert(root.getLogisticsOrderEntity(), order);
        }
        return Mono.just(order);
    }

    @Override
    public Mono<Void> updateAddr(LogisticsOrderRoot root) {
        return Mono
                .just(OLogisticsOrder.builder()
                        .logisticsOrderId(root.getLogisticsOrderEntity().getLogisticsOrderId())
                        .receivingMobile(root.getOrderAddrEntity().getReceivingMobile())
                        .addressDetail(root.getOrderAddrEntity().getAddressDetail())
                        .userAddressId(root.getOrderAddrEntity().getUserAddressId())
                        .consigneeName(root.getOrderAddrEntity().getConsigneeName())
                        .addressSite(root.getOrderAddrEntity().getAddressSite()).build())
                .flatMap(order -> update(order, OLogisticsOrder.class, (v, clazz) -> v,
                        oLogisticsOrderMapper::updateByPrimaryKeySelective))
                .then();
    }

    @Override
    public Mono<Void> updateByOrderNo(LogisticsOrderRoot root) {
        return Mono
                .just(OLogisticsOrder.builder().orderNo(root.getOrderEntity().getOrderNo())
                        .logisticsOrderStatus(
                                root.getLogisticsOrderEntity().getLogisticsOrderStatus())
                        .build())
                .flatMap(order -> update(order, OLogisticsOrder.class, (v, clazz) -> v,
                        oLogisticsOrderMapper::updateByOrderNo))
                .then();
    }
}
