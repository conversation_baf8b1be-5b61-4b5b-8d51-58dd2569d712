package com.xk.ewd.infrastructure.repository.order;

import org.springframework.stereotype.Repository;

import com.xk.ewd.domain.model.order.entity.OrderLogEntity;
import com.xk.ewd.domain.model.order.id.OrderIdentifier;
import com.xk.ewd.domain.repository.order.OrderLogRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.order.OOrderLogMapper;
import com.xk.ewd.infrastructure.data.po.order.OOrderLog;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;

@Repository
@RequiredArgsConstructor
public class OrderLogRootQueryRepositoryImpl implements OrderLogRootQueryRepository {

    private final OOrderLogMapper oOrderLogMapper;
    private final Converter converter;

    @Override
    public Flux<OrderLogEntity> searchByOrderNo(OrderIdentifier identifier) {
        return find(identifier,
                id -> oOrderLogMapper
                        .selectByOrderNo(OOrderLog.builder().orderNo(id.getOrderNo()).build()),
                OrderLogEntity.class, converter::convert);
    }
}
