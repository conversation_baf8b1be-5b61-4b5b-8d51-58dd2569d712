package com.xk.ewd.infrastructure.repository.payment;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.payment.PaymentRoot;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.repository.payment.PaymentRootRepository;
import com.xk.ewd.infrastructure.data.persistence.payment.ORefundMapper;
import com.xk.ewd.infrastructure.data.po.payment.ORefund;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PaymentRootRepositoryImpl implements PaymentRootRepository {

    private final ORefundMapper refundMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(PaymentRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(PaymentRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(PaymentRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(PaymentRoot root) {
        return null;
    }

    @Override
    public Mono<Void> insertRefund(RefundEntity refundEntity) {
        return this.save(refundEntity, ORefund.class,converter::convert,refundMapper::insertRefund);
    }

    @Override
    public Mono<Void> updateRefund(RefundEntity refundEntity) {
        return this.update(refundEntity,ORefund.class,converter::convert,refundMapper::updateRefund);
    }
}
