package com.xk.ewd.infrastructure.data.po;

import java.util.Date;

import com.alibaba.fastjson2.annotation.JSONField;
import com.myco.mydata.domain.model.password.PasswordType;
import com.myco.mydata.domain.model.secure.SecureIdentifier;
import com.myco.mydata.infrastructure.commons.annotation.EncryptField;
import lombok.Data;

@Data
public class ExampleTbl2 {
    /**
     * 品类ID
     */
    @JSONField(name = "commodity_id")
    private String commodityId;

    /**
     * 顾客姓名
     */
    @EncryptField(encryptionType = SecureIdentifier.AES, passwordType = PasswordType.DB)
    @JSONField(name = "customer_name")
    private String customerName;

    /**
     * 顾客国籍
     */
    private String country;

    /**
     * 支付时间
     */
    @JSONField(name = "pay_time")
    private Long payTime;

    /**
     * 支付日期
     */
    @JSONField(name = "pay_dt")
    private Date payDt;

    /**
     * 支付金额
     */
    private Double price;
}
