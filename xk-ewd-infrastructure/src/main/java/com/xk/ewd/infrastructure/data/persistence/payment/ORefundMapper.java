package com.xk.ewd.infrastructure.data.persistence.payment;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.payment.ORefund;

@Repository
@Table(value = "o_refund", ewdAsyncEnabled = false)
public interface ORefundMapper {

    /**
     * 添加退货单
     * @param oRefund oRefund
     */
    void insertRefund(@Param("refund") ORefund oRefund);

    /**
     * 修改退货单
     * @param oRefund oRefund
     */
    void updateRefund(@Param("refund") ORefund oRefund);

    /**
     * 分页查询退货单
     * @param pagination pagination
     * @return List<ORefund>
     */
    List<ORefund> selectByPage(Pagination pagination);
}
