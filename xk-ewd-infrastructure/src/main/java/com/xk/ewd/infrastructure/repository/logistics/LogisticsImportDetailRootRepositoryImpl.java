package com.xk.ewd.infrastructure.repository.logistics;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsImportDetailRootRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsImportDetailMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsImportDetailRootRepositoryImpl
        implements LogisticsImportDetailRootRepository {

    private final Converter converter;
    private final OLogisticsImportDetailMapper oLogisticsImportDetailMapper;


    @Override
    public Mono<Void> save(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> {
            if (order.getOrderNo() == null) {
                return Mono.empty();
            }
            return save(order, OLogisticsImportDetail.class, (v, clazz) -> order,
                    oLogisticsImportDetailMapper::insertSelective);
        });
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(LogisticsOrderRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> {
            if (order.getOrderNo() == null) {
                return Mono.empty();
            }
            return update(order, OLogisticsImportDetail.class, (v, clazz) -> v,
                    oLogisticsImportDetailMapper::updateByPrimaryKeySelective);
        });
    }

    @Override
    public Mono<Void> remove(LogisticsOrderRoot root) {
        return null;
    }

    private @NotNull Mono<OLogisticsImportDetail> rootToOOrder(LogisticsOrderRoot root) {
        OLogisticsImportDetail order = new OLogisticsImportDetail();
        if (root.getLogisticsOrderEntity() != null) {
            converter.convert(root.getLogisticsOrderEntity(), order);
        }
        if (root.getOrderEntity() != null) {
            converter.convert(root.getOrderEntity(), order);
        }
        if (root.getGoodsValueObject() != null) {
            converter.convert(root.getGoodsValueObject(), order);
        }
        return Mono.just(order);
    }


    @Override
    public Mono<Void> generate(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> {
            if (order == null) {
                return Mono.empty();
            }
            return update(order, OLogisticsImportDetail.class, (v, clazz) -> v,
                    oLogisticsImportDetailMapper::generate);
        });
    }

}
