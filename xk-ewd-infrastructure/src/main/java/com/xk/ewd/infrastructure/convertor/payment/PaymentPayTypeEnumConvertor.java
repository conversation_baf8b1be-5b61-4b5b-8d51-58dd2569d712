package com.xk.ewd.infrastructure.convertor.payment;

import com.xk.order.enums.payment.PaymentPayTypeEnum;

public class PaymentPayTypeEnumConvertor {
    private PaymentPayTypeEnumConvertor() {}

    public static PaymentPayTypeEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return PaymentPayTypeEnum.getByCode(code);
    }

    public static Integer map(PaymentPayTypeEnum enumObj) {
        if (enumObj == null) {
            return null;
        }
        return enumObj.getCode();
    }
}
