package com.xk.ewd.infrastructure.data.persistence.goods;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.domain.model.goods.valobj.StatusCountValObj;
import com.xk.ewd.infrastructure.data.po.goods.GGoods;

/**
 * <AUTHOR>
 * @description 针对表【g_goods】的数据库操作Mapper
 * @createDate 2025-08-02 11:15:28
 * @Entity com.xk.ewd.infrastructure.data.po.goods.GGoods
 */
@Repository
@Table("g_goods")
public interface GGoodsMapper {

    int deleteByPrimaryKey(GGoods record);

    int insert(GGoods record);

    int insertSelective(GGoods record);

    GGoods selectByPrimaryKey(GGoods record);

    int updateByPrimaryKeySelective(GGoods record);

    int updateByPrimaryKey(GGoods record);

    List<GGoods> selectByPage(Pagination pagination);

    List<StatusCountValObj> statusCount(GGoods record);

    List<GGoods> selectListByCorp(Pagination pagination);

    List<GGoods> searchCorpMerchantGoods(Pagination pagination);
}
