package com.xk.ewd.infrastructure.repository.logistics;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.xk.ewd.domain.dto.logistics.LogisticsDetailDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsDetailRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OSendGoodsDetailMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsDetailRootQueryRepositoryImpl implements LogisticsDetailRootQueryRepository {

    private final Converter converter;
    private final OSendGoodsDetailMapper oSendGoodsDetailMapper;

    @Override
    public Flux<LogisticsDetailDto> selectById(LogisticsOrderRoot root) {
        List<OSendGoodsDetail> oSendGoodsDetails =
                oSendGoodsDetailMapper.selectById(OSendGoodsDetail.builder()
                        .logisticsId(root.getIdentifier().getLogisticsOrderId()).build());

        if (oSendGoodsDetails.isEmpty()) {
            return Flux.empty();
        }

        return Flux
                .fromIterable(oSendGoodsDetails)
                .flatMap(detail -> Mono.just(converter.convert(detail, LogisticsDetailDto.class)));
    }

    @Override
    public Mono<Date> selectMaxTimeById(LogisticsOrderRoot root) {
        Date date = oSendGoodsDetailMapper.selectMaxTimeById(OSendGoodsDetail.builder()
                .logisticsId(root.getIdentifier().getLogisticsOrderId()).build());
        return Mono.justOrEmpty(date);
    }
}
