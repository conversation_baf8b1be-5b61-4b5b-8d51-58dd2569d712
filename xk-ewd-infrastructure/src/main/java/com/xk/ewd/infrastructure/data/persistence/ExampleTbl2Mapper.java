package com.xk.ewd.infrastructure.data.persistence;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.ExampleTbl2;

@Repository
@Table(value = "example_tbl2", dataEncryptionEnabled = true)
public interface ExampleTbl2Mapper {

    ExampleTbl2 selectByCommodityId(ExampleTbl2 record);

    int insert(ExampleTbl2 record);

    int insertSelective(ExampleTbl2 record);
}
