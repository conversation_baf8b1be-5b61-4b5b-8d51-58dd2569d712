package com.xk.ewd.infrastructure.repository.order;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.order.OrderRoot;
import com.xk.ewd.domain.repository.order.OrderRootRepository;
import com.xk.ewd.infrastructure.data.persistence.order.OOrderMapper;
import com.xk.ewd.infrastructure.data.po.order.OOrder;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderRootRepositoryImpl implements OrderRootRepository {

    private final OOrderMapper orderMapper;

    private final Converter converter;

    @Override
    public Mono<Void> save(OrderRoot root) {
        return rootToOOrder(root).flatMap(order -> {
            if (order.getOrderNo() == null) {
                return Mono.empty();
            }
            return save(order, OOrder.class, (v, clazz) -> order, orderMapper::insertSelective);
        });
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(OrderRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(OrderRoot root) {
        return rootToOOrder(root).flatMap(order -> {
            if (order.getOrderNo() == null) {
                return Mono.empty();
            }
            return update(order, OOrder.class, (v, clazz) -> v,
                    orderMapper::updateByPrimaryKeySelective);
        });
    }

    @Override
    public Mono<Void> remove(OrderRoot root) {
        return null;
    }

    private @NotNull Mono<OOrder> rootToOOrder(OrderRoot root) {
        OOrder order = new OOrder();
        if (root.getCorpEntity() != null) {
            converter.convert(root.getCorpEntity(), order);
        }
        if (root.getLogisticsOrderEntity() != null) {
            converter.convert(root.getLogisticsOrderEntity(), order);
        }
        if (root.getOrderAddrEntity() != null) {
            converter.convert(root.getOrderAddrEntity(), order);
        }
        if (root.getOrderEntity() != null) {
            converter.convert(root.getOrderEntity(), order);
        }
        if (root.getUserEntity() != null) {
            converter.convert(root.getUserEntity(), order);
        }
        if (root.getGoodsValueObject() != null) {
            converter.convert(root.getGoodsValueObject(), order);
        }
        return Mono.just(order);
    }
}
