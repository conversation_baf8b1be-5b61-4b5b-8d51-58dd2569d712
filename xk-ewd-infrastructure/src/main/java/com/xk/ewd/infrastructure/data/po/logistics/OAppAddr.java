package com.xk.ewd.infrastructure.data.po.logistics;

import com.xk.ewd.domain.dto.logistics.AppAddrDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = AppAddrDto.class),})
public class OAppAddr {

    /**
     * 收货地址ids
     */
    private Long userAddressId;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 手机号
     */
    private String receivingMobile;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;


}
