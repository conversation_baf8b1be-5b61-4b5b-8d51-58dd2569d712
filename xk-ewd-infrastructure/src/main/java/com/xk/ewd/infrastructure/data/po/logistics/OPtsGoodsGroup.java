package com.xk.ewd.infrastructure.data.po.logistics;

import java.util.Date;

import com.xk.ewd.domain.dto.logistics.PtsGoodsGroupDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单表
 *
 * @TableName d_logistics_order
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({@AutoMapper(target = PtsGoodsGroupDto.class),})
public class OPtsGoodsGroup {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 直播状态
     */
    private Integer liveStatus;
}
