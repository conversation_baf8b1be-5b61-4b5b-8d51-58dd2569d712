package com.xk.ewd.infrastructure.repository.logistics;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.alibaba.fastjson2.JSONArray;
import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.dto.logistics.OrderGoodsInfoDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.domain.repository.logistics.LogisticsGoodsRootRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsGoodsMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsGoodsRootRepositoryImpl implements LogisticsGoodsRootRepository {

    private final Converter converter;
    private final OLogisticsGoodsMapper oLogisticsGoodsMapper;


    @Override
    public Mono<Void> save(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> save(order, OLogisticsGoods.class,
                (v, clazz) -> order, oLogisticsGoodsMapper::insertSelective)).then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(LogisticsOrderRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> update(order, OLogisticsGoods.class,
                (v, clazz) -> v, oLogisticsGoodsMapper::updateByPrimaryKeySelective)).then();
    }

    @Override
    public Mono<Void> giftDelete(LogisticsOrderRoot root) {
        return update(
                OLogisticsGoods.builder().deleted(root.getLogisticsOrderEntity().getDeleted())
                        .logisticsOrderId(root.getLogisticsOrderEntity().getLogisticsOrderId())
                        .goodsId(root.getLogisticsOrderEntity().getGiftReportId())
                        .createTime(root.getLogisticsOrderEntity().getCreateTime()).build(),
                OLogisticsGoods.class, (v, clazz) -> v,
                oLogisticsGoodsMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> remove(LogisticsOrderRoot root) {
        return null;
    }

    private @NotNull Flux<OLogisticsGoods> rootToOOrder(LogisticsOrderRoot root) {
        ArrayList<OLogisticsGoods> list = new ArrayList<>();
        GoodsValueObject goodsValueObject = root.getGoodsValueObject();
        OrderEntity orderEntity = root.getOrderEntity();
        List<OrderGoodsInfoDto> orderGoodsInfoDtos =
                JSONArray.parseArray(goodsValueObject.getGoodsInfo(), OrderGoodsInfoDto.class);
        Integer logisticsOrderType = root.getLogisticsOrderEntity().getLogisticsOrderType();
        if (LogisticsOrderTypeEnum.GIFT.getCode().equals(logisticsOrderType)) {
            OLogisticsGoods order = new OLogisticsGoods();
            if (root.getCorpEntity() != null) {
                converter.convert(root.getCorpEntity(), order);
            }
            if (root.getOrderAddrEntity() != null) {
                converter.convert(root.getOrderAddrEntity(), order);
            }
            if (root.getOrderEntity() != null) {
                converter.convert(root.getOrderEntity(), order);
            }
            if (root.getUserEntity() != null) {
                converter.convert(root.getUserEntity(), order);
            }
            if (root.getLogisticsOrderEntity() != null) {
                converter.convert(root.getLogisticsOrderEntity(), order);
            }
            order.setLiveStatus(goodsValueObject.getLiveStatus());
            order.setGoodsId(orderEntity.getGiftReportId());
            order.setGoodsName(orderEntity.getGiftName());
            order.setGoodsImages(CollectionUtils.isEmpty(orderEntity.getGiftAddr()) ? null
                    : orderEntity.getGiftAddr().getFirst());
            order.setShipCount(1);
            list.add(order);
        } else {
            if (CollectionUtils.isNotEmpty(orderGoodsInfoDtos)) {
                for (OrderGoodsInfoDto orderGoodsInfoDto : orderGoodsInfoDtos) {
                    OLogisticsGoods order = new OLogisticsGoods();
                    if (root.getCorpEntity() != null) {
                        converter.convert(root.getCorpEntity(), order);
                    }
                    if (root.getOrderAddrEntity() != null) {
                        converter.convert(root.getOrderAddrEntity(), order);
                    }
                    if (root.getOrderEntity() != null) {
                        converter.convert(root.getOrderEntity(), order);
                    }
                    if (root.getUserEntity() != null) {
                        converter.convert(root.getUserEntity(), order);
                    }
                    if (root.getLogisticsOrderEntity() != null) {
                        converter.convert(root.getLogisticsOrderEntity(), order);
                    }
                    order.setLiveStatus(goodsValueObject.getLiveStatus());
                    order.setGoodsId(orderGoodsInfoDto.getGoodsId());
                    order.setGoodsName(orderGoodsInfoDto.getGoodsName());
                    order.setGoodsImages(orderGoodsInfoDto.getGoodsImages());
                    order.setShipCount(orderGoodsInfoDto.getShipCount());
                    order.setUnitPrice(orderGoodsInfoDto.getUnitPrice());
                    order.setUnitType(orderGoodsInfoDto.getUnitType());
                    order.setProductType(orderGoodsInfoDto.getProductType());
                    list.add(order);
                }
            }
        }

        return CollectionUtils.isEmpty(list) ? Flux.empty() : Flux.fromIterable(list);
    }

    @Override
    public Mono<Void> export(LogisticsOrderRoot root) {
        OLogisticsGoods goods =
                OLogisticsGoods.builder().orderType(root.getOrderEntity().getOrderType())
                        .startTime(root.getOrderEntity().getStartTime())
                        .endTime(root.getOrderEntity().getEndTime())
                        .corpId(root.getCorpEntity().getCorpId())
                        .filePath(root.getOrderEntity().getFilePath()).build();
        return update(goods, OLogisticsGoods.class, (v, clazz) -> v, oLogisticsGoodsMapper::export);
    }



    @Override
    public Mono<Void> updateLiveStatus(LogisticsOrderRoot root) {
        return update(
                OLogisticsGoods.builder()
                        .goodsId(Long.valueOf(root.getGoodsValueObject().getGoodsId()))
                        .liveStatus(root.getGoodsValueObject().getLiveStatus()).build(),
                OLogisticsGoods.class, (v, clazz) -> v,
                oLogisticsGoodsMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> updateAddr(LogisticsOrderRoot root) {
        return Mono
                .just(OLogisticsGoods.builder()
                        .logisticsOrderId(root.getLogisticsOrderEntity().getLogisticsOrderId())
                        .receivingMobile(root.getOrderAddrEntity().getReceivingMobile())
                        .addressDetail(root.getOrderAddrEntity().getAddressDetail())
                        .userAddressId(root.getOrderAddrEntity().getUserAddressId())
                        .consigneeName(root.getOrderAddrEntity().getConsigneeName())
                        .addressSite(root.getOrderAddrEntity().getAddressSite()).build())
                .flatMap(order -> update(order, OLogisticsGoods.class, (v, clazz) -> v,
                        oLogisticsGoodsMapper::updateByPrimaryKeySelective))
                .then();
    }

    @Override
    public Mono<Void> updateByOrderNo(LogisticsOrderRoot root) {
        return Mono
                .just(OLogisticsGoods.builder().orderNo(root.getOrderEntity().getOrderNo())
                        .logisticsOrderStatus(
                                root.getLogisticsOrderEntity().getLogisticsOrderStatus())
                        .build())
                .flatMap(order -> update(order, OLogisticsGoods.class, (v, clazz) -> v,
                        oLogisticsGoodsMapper::updateByOrderNo))
                .then();
    }
}
