package com.xk.ewd.infrastructure.data.persistence.logistics;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.domain.dto.logistics.SendGoodsDto;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods;
import com.xk.ewd.infrastructure.data.po.logistics.OPtsGoodsGroup;
import com.xk.ewd.infrastructure.data.po.logistics.OPtsGroup;
import com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDto;

/**
 * <AUTHOR>
 * @description 针对表【o_logistics_goods】的数据库操作Mapper
 * @createDate 2025-07-20 14:52:16
 * @Entity com.xk.ewd.infrastructure.data.po.logistics.OLogisticsGoods
 */
@Repository
@Table(value = "o_logistics_goods", ewdAsyncEnabled = false)
public interface OLogisticsGoodsMapper {

    int deleteByPrimaryKey(OLogisticsGoods record);

    int insert(OLogisticsGoods record);

    int insertSelective(OLogisticsGoods record);

    OLogisticsGoods selectByPrimaryKey(OLogisticsGoods record);

    int updateByPrimaryKeySelective(OLogisticsGoods record);

    int updateByPrimaryKey(OLogisticsGoods record);

    List<OPtsGoodsGroup> ptsGoodsGroup(Pagination pagination);

    List<OPtsGroup> ptsGroup(Pagination pagination);

    List<OSendGoodsDto> sendGoodsQuery(SendGoodsDto sendGoodsDto);

    void export(OLogisticsGoods record);

    List<OPtsGoodsGroup> ptsGoodsLiveGroup(Pagination pagination);

    void updateByOrderNo(OLogisticsGoods record);
}
