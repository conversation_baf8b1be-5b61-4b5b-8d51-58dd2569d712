package com.xk.ewd.infrastructure.data.persistence.order;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.domain.dto.order.OrderStatisticsDto;
import com.xk.ewd.infrastructure.data.po.order.OOrder;

/**
 * <AUTHOR>
 * @description 针对表【o_order(订单表)】的数据库操作Mapper
 * @createDate 2025-07-10 14:36:30
 * @Entity com.xk.ewd.infrastructure.data.po.DOrder
 */
@Repository
@Table(value = "o_order", ewdAsyncEnabled = false)
public interface OOrderMapper {

    int insert(OOrder record);

    int insertSelective(OOrder record);

    OOrder selectByPrimaryKey(OOrder record);

    int updateByPrimaryKeySelective(OOrder record);

    int updateByPrimaryKey(OOrder record);

    List<OOrder> selectByPage(Pagination pagination);

    OrderStatisticsDto searchStatistics(OOrder record);
}
