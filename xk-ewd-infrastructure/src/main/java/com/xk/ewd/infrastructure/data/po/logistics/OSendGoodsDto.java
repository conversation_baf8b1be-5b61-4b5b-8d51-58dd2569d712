package com.xk.ewd.infrastructure.data.po.logistics;

import java.util.Date;

import com.xk.ewd.domain.dto.logistics.AppSendGoodsDto;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商城订单列表
 * 
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({@AutoMapper(target = AppSendGoodsDto.class)})
public class OSendGoodsDto {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商品图片
     */
    private String goodsImages;

    /**
     * 商品数量
     */
    private Integer countNum;

}
