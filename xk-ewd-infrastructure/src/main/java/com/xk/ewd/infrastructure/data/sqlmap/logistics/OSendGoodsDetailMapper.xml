<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.logistics.OSendGoodsDetailMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail">
        <result property="logisticsId" column="logistics_id" jdbcType="BIGINT"/>
        <result property="sendGoodsId" column="send_goods_id" jdbcType="BIGINT"/>
        <result property="trackTime" column="track_time" jdbcType="TIMESTAMP"/>
        <result property="trackDescribe" column="track_describe" jdbcType="VARCHAR"/>
        <result property="trackType" column="track_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        logistics_id
        ,send_goods_id,track_time,
        track_describe,track_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_send_goods_detail
        where
    </select>
    <select id="selectById" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_send_goods_detail
        where logistics_id = #{logisticsId,jdbcType=BIGINT}
        order by track_time desc
    </select>
    <select id="selectMaxTimeById" resultType="java.util.Date">
        select track_time as trackTime
        from o_send_goods_detail
        where logistics_id = #{logisticsId,jdbcType=BIGINT}
        order by track_time desc limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail">
        delete
        from o_send_goods_detail
        where
    </delete>
    <insert id="insert">
        insert into o_send_goods_detail
        ( logistics_id, send_goods_id, track_time
        , track_describe, track_type)
        values ( #{logisticsId,jdbcType=BIGINT}, #{sendGoodsId,jdbcType=BIGINT}, #{trackTime,jdbcType=TIMESTAMP}
               , #{trackDescribe,jdbcType=VARCHAR}, #{trackType,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective">
        insert into o_send_goods_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logisticsId != null">logistics_id,</if>
            <if test="sendGoodsId != null">send_goods_id,</if>
            <if test="trackTime != null">track_time,</if>
            <if test="trackDescribe != null">track_describe,</if>
            <if test="trackType != null">track_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logisticsId != null">#{logisticsId,jdbcType=BIGINT},</if>
            <if test="sendGoodsId != null">#{sendGoodsId,jdbcType=BIGINT},</if>
            <if test="trackTime != null">#{trackTime,jdbcType=TIMESTAMP},</if>
            <if test="trackDescribe != null">#{trackDescribe,jdbcType=VARCHAR},</if>
            <if test="trackType != null">#{trackType,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail">
        update o_send_goods_detail
        <set>
            <if test="logisticsId != null">
                logistics_id = #{logisticsId,jdbcType=BIGINT},
            </if>
            <if test="sendGoodsId != null">
                send_goods_id = #{sendGoodsId,jdbcType=BIGINT},
            </if>
            <if test="trackTime != null">
                track_time = #{trackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="trackDescribe != null">
                track_describe = #{trackDescribe,jdbcType=VARCHAR},
            </if>
            <if test="trackType != null">
                track_type = #{trackType,jdbcType=VARCHAR},
            </if>
        </set>
        where logistics_id = #{logisticsId,jdbcType=BIGINT} and track_time = #{trackTime,jdbcType=TIMESTAMP}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail">
        update o_send_goods_detail
        set logistics_id   = #{logisticsId,jdbcType=BIGINT},
            send_goods_id  = #{sendGoodsId,jdbcType=BIGINT},
            track_time     = #{trackTime,jdbcType=TIMESTAMP},
            track_describe = #{trackDescribe,jdbcType=VARCHAR},
            track_type     = #{trackType,jdbcType=VARCHAR}
        where logistics_id = #{logisticsId,jdbcType=BIGINT}
          and track_time = #{trackTime,jdbcType=TIMESTAMP}
    </update>
</mapper>
