package com.xk.ewd.infrastructure.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.LogisticsOrderEntity;
import com.xk.ewd.domain.model.logistics.entity.OrderEntity;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail;

import io.github.linpeilie.BaseMapper;

/**
 * OrderRoot到SendGoodsEntity的转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OLogisticsImportDetailToLogisticsOrderRootMapper
        implements BaseMapper<OLogisticsImportDetail, LogisticsOrderRoot> {

    @Override
    public LogisticsOrderRoot convert(OLogisticsImportDetail source) {
        return convert(source,
                LogisticsOrderRoot.builder()
                        .identifier(
                                LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                        .build());
    }

    @Override
    public LogisticsOrderRoot convert(OLogisticsImportDetail source, LogisticsOrderRoot target) {
        if (source == null) {
            return null;
        }

        return LogisticsOrderRoot.builder()
                .identifier(LogisticsOrderIdentifier.builder()
                        .logisticsOrderId(source.getLogisticsOrderId()).build())
                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                        .logisticsOrderId(source.getLogisticsOrderId())
                        .logisticsCorpName(source.getLogisticsCorpName())
                        .logisticsNo(source.getLogisticsNo())
                        .logisticsOrderStatus(source.getLogisticsOrderStatus())
                        .errRemark(source.getErrRemark()).taskId(source.getTaskId()).build())
                .orderEntity(OrderEntity.builder().orderNo(source.getOrderNo()).build())
                .goodsValueObject(GoodsValueObject.builder().goodsName(source.getGoodsName())
                        .goodsCount(source.getGoodsCount()).build())
                .build();
    }
}
