package com.xk.ewd.infrastructure.data.persistence.order;

import java.util.List;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.ewd.infrastructure.data.po.order.OOrderLog;

/**
 * <AUTHOR>
 * @description 针对表【o_order_log(订单日志表)】的数据库操作Mapper
 * @createDate 2025-07-17 21:18:45
 * @Entity com.xk.ewd.infrastructure.data.po.order.OOrderLog
 */
@Repository
@Table(value = "o_order_log", ewdAsyncEnabled = false)
public interface OOrderLogMapper {

    int deleteByPrimaryKey(OOrderLog record);

    int insert(OOrderLog record);

    int insertSelective(OOrderLog record);

    OOrderLog selectByPrimaryKey(OOrderLog record);

    int updateByPrimaryKeySelective(OOrderLog record);

    int updateByPrimaryKey(OOrderLog record);

    List<OOrderLog> selectByOrderNo(OOrderLog record);
}
