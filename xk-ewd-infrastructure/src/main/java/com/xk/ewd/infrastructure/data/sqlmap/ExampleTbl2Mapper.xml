<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.ExampleTbl2Mapper">
  <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.ExampleTbl2">
    <!--@mbg.generated-->
    <!--@Table example_tbl2-->
    <result column="commodity_id" jdbcType="VARCHAR" property="commodityId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="pay_time" jdbcType="BIGINT" property="payTime" />
    <result column="pay_dt" jdbcType="DATE" property="payDt" />
    <result column="price" jdbcType="DOUBLE" property="price" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    commodity_id, customer_name, country, pay_time, pay_dt, price
  </sql>
  <insert id="insert" parameterType="com.xk.ewd.infrastructure.data.po.ExampleTbl2">
    <!--@mbg.generated-->
    insert into example_tbl2 (commodity_id, customer_name, country, 
      pay_time, pay_dt, price)
    values (#{commodityId,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, 
      #{payTime,jdbcType=BIGINT}, #{payDt,jdbcType=DATE}, #{price,jdbcType=DOUBLE})
  </insert>
  <insert id="insertSelective" parameterType="com.xk.ewd.infrastructure.data.po.ExampleTbl2">
    <!--@mbg.generated-->
    insert into example_tbl2
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="commodityId != null">
        commodity_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="payDt != null">
        pay_dt,
      </if>
      <if test="price != null">
        price,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="commodityId != null">
        #{commodityId,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=BIGINT},
      </if>
      <if test="payDt != null">
        #{payDt,jdbcType=DATE},
      </if>
      <if test="price != null">
        #{price,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>

  <select id="selectByCommodityId" resultMap="BaseResultMap" parameterType="com.xk.ewd.infrastructure.data.po.ExampleTbl2">
    select
    <include refid="Base_Column_List" />
    from example_tbl2
    where commodity_id LIKE "%2kafka%" LIMIT 500000, 1
  </select>
</mapper>