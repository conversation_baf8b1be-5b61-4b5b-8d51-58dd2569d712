<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.goods.GGoodsMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.goods.GGoods">
        <id property="goodsId" column="goods_id" jdbcType="BIGINT"/>
        <id property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsType" column="goods_type" jdbcType="TINYINT"/>
        <result property="currencyType" column="currency_type" jdbcType="TINYINT"/>
        <result property="unitPrice" column="unit_price" jdbcType="BIGINT"/>
        <result property="costPrice" column="cost_price" jdbcType="BIGINT"/>
        <result property="salePrice" column="sale_price" jdbcType="BIGINT"/>
        <result property="showStatus" column="show_status" jdbcType="TINYINT"/>
        <result property="listingStatus" column="listing_status" jdbcType="TINYINT"/>
        <result property="listingDate" column="listing_date" jdbcType="INTEGER"/>
        <result property="autoListingStatus" column="auto_listing_status" jdbcType="TINYINT"/>
        <result property="highestPrice" column="highest_price" jdbcType="BIGINT"/>
        <result property="lowestPrice" column="lowest_price" jdbcType="BIGINT"/>
        <result property="planUpTime" column="plan_up_time" jdbcType="TIMESTAMP"/>
        <result property="planDownTime" column="plan_down_time" jdbcType="TIMESTAMP"/>
        <result property="actualUpTime" column="actual_up_time" jdbcType="TIMESTAMP"/>
        <result property="actualDownTime" column="actual_down_time" jdbcType="TIMESTAMP"/>
        <result property="goodsDescribe" column="goods_describe" jdbcType="VARCHAR"/>
        <result property="remainStock" column="remain_stock" jdbcType="BIGINT"/>
        <result property="totalStock" column="total_stock" jdbcType="BIGINT"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="blockType" column="block_type" jdbcType="TINYINT"/>
        <result property="seriesName" column="series_name" jdbcType="VARCHAR"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
        <result property="corpInfoId" column="corp_info_id" jdbcType="BIGINT"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR"/>
        <result property="corpLogo" column="corp_logo" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="goodsResId" column="goods_res_id" jdbcType="BIGINT"/>
        <result property="goodsImage" column="goods_image" jdbcType="VARCHAR"/>
        <result property="collectibleCardId" column="collectible_card_id" jdbcType="BIGINT"/>
        <result property="collectibleImage" column="collectible_image" jdbcType="VARCHAR"/>
        <result property="collectibleCardName" column="collectible_card_name" jdbcType="VARCHAR"/>
        <result property="collectibleCardUnitPrice" column="collectible_card_unit_price" jdbcType="BIGINT"/>
        <result property="productType" column="product_type" jdbcType="TINYINT"/>
        <result property="soldStatus" column="sold_status" jdbcType="TINYINT"/>
        <result property="soldTime" column="sold_time" jdbcType="TIMESTAMP"/>
        <result property="soldOutStatus" column="sold_out_status" jdbcType="TINYINT"/>
        <result property="soldOutTime" column="sold_out_time" jdbcType="TIMESTAMP"/>
        <result property="groupStatus" column="group_status" jdbcType="TINYINT"/>
        <result property="groupTime" column="group_time" jdbcType="TIMESTAMP"/>
        <result property="publicityStatus" column="publicity_status" jdbcType="TINYINT"/>
        <result property="publicityTime" column="publicity_time" jdbcType="TIMESTAMP"/>
        <result property="reportStatus" column="report_status" jdbcType="TINYINT"/>
        <result property="reportTime" column="report_time" jdbcType="TIMESTAMP"/>
        <result property="finishStatus" column="finish_status" jdbcType="TINYINT"/>
        <result property="finishTime" column="finish_time" jdbcType="TIMESTAMP"/>
        <result property="refundStatus" column="refund_status" jdbcType="TINYINT"/>
        <result property="refundTime" column="refund_time" jdbcType="TIMESTAMP"/>
        <result property="serialGroupId" column="serial_group_id" jdbcType="BIGINT"/>
        <result property="randomType" column="random_type" jdbcType="TINYINT"/>
        <result property="auditStatus" column="audit_status" jdbcType="TINYINT"/>
        <result property="recycleStatus" column="recycle_status" jdbcType="TINYINT"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="refuseRemark" column="refuse_remark" jdbcType="VARCHAR"/>
        <result property="auditUserId" column="audit_user_id" jdbcType="BIGINT"/>
        <result property="firstBuyDiscountStatus" column="first_buy_discount_status" jdbcType="TINYINT"/>
        <result property="firstBuyDiscountAmount" column="first_buy_discount_amount" jdbcType="BIGINT"/>
        <result property="remainRandomStatus" column="remain_random_status" jdbcType="TINYINT"/>
        <result property="remainRandomAmount" column="remain_random_amount" jdbcType="BIGINT"/>
        <result property="unitType" column="unit_type" jdbcType="VARCHAR"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="updateId" column="update_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="StatusResultMap" type="com.xk.ewd.domain.model.goods.valobj.StatusCountValObj">
        <result property="corpInfoId" column="corp_info_id" jdbcType="BIGINT"/>
        <result property="productType" column="product_type" jdbcType="TINYINT"/>
        <result property="auditedCount" column="audited_count" jdbcType="INTEGER"/>
        <result property="auditingCount" column="auditing_count" jdbcType="INTEGER"/>
        <result property="unPassCount" column="un_pass_count" jdbcType="INTEGER"/>
        <result property="finishCount" column="finish_count" jdbcType="INTEGER"/>
        <result property="soldOutCount" column="sold_out_count" jdbcType="INTEGER"/>
        <result property="notSoldOutOrRefundCount" column="not_sold_or_refund_count" jdbcType="INTEGER"/>
        <result property="recycleCount" column="recycle_count" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        goods_id
        ,create_time,goods_name,
        goods_type,currency_type,unit_price,
        cost_price,sale_price,show_status,
        listing_status,listing_date,auto_listing_status,
        highest_price,lowest_price,plan_up_time,
        plan_down_time,actual_up_time,actual_down_time,
        goods_describe,remain_stock,total_stock,
        sort,block_type,series_name,
        category_id,category_name,corp_info_id,
        corp_name,corp_logo,status,
        goods_res_id,goods_image,collectible_card_id,
        collectible_image,collectible_card_name,collectible_card_unit_price,
        product_type,sold_status,sold_time,
        sold_out_status,sold_out_time,group_status,
        group_time,publicity_status,publicity_time,
        report_status,report_time,finish_status,
        finish_time,refund_status,refund_time,
        serial_group_id,random_type,audit_status,
        recycle_status,audit_time,refuse_remark,
        audit_user_id,first_buy_discount_status,first_buy_discount_amount,
        remain_random_status,remain_random_amount,unit_type,
        create_id,update_id,update_time,
        deleted
    </sql>

    <!-- 分页查询（包含所有字段条件） -->
    <select id="selectByPage" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM g_goods
        <where>
            <!-- 商品基础信息 -->
            <if test="goodsId != null">
                AND goods_id = #{goodsId}
            </if>
            <if test="goodsName != null and goodsName != ''">
                AND goods_name LIKE CONCAT('%', #{goodsName}, '%')
            </if>
            <if test="goodsType != null">
                AND goods_type = #{goodsType}
            </if>
            <if test="productType != null">
                AND product_type = #{productType}
            </if>
            <if test="actualUpStartTime != null">
                AND actual_up_time <![CDATA[>=]]> #{actualUpStartTime}
            </if>
            <if test="actualUpEndTime != null">
                AND actual_up_time <![CDATA[<=]]> #{actualUpEndTime}
            </if>
            <if test="actualDownStartTime != null">
                AND actual_down_time <![CDATA[>=]]> #{actualDownStartTime}
            </if>
            <if test="actualDownEndTime != null">
                AND actual_down_time <![CDATA[<=]]> #{actualDownEndTime}
            </if>
            <if test="blockType != null">
                AND block_type = #{blockType}
            </if>
            <!-- 状态相关 -->
            <if test="listingStatus != null">
                AND listing_status = #{listingStatus}
            </if>
            <if test="soldStatus != null">
                AND sold_status = #{soldStatus}
            </if>
            <if test="groupStatus != null">
                AND group_status = #{groupStatus}
            </if>
            <if test="publicityStatus != null">
                AND publicity_status = #{publicityStatus}
            </if>
            <if test="reportStatus != null">
                AND report_status = #{reportStatus}
            </if>
            <if test="notSoldOutOrRefund != null and notSoldOutOrRefund == 1">
                AND (sold_out_status = 1 AND group_status = 0)
            </if>
            <if test="showStatus != null">
                AND show_status = #{showStatus}
            </if>
            <if test="auditStatus != null">
                AND audit_status = #{auditStatus}
            </if>
            <if test="finishStatus != null">
                AND finish_status = #{finishStatus}
            </if>
            <if test="soldOutStatus != null">
                AND sold_out_status = #{soldOutStatus}
            </if>
            <if test="refundStatus != null">
                AND refund_status = #{refundStatus}
            </if>
            <if test="recycleStatus != null">
                AND recycle_status = #{recycleStatus}
            </if>
            <if test="showStatus != null">
                AND show_status = #{showStatus}
            </if>

            <!-- 商家相关 -->
            <if test="corpInfoId != null">
                AND corp_info_id = #{corpInfoId}
            </if>

            <!-- 时间范围 -->
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <![CDATA[<=]]> #{endTime}
            </if>

            <!-- 特殊字段 -->
            <if test="seriesName != null and seriesName != ''">
                AND series_name = #{seriesName}
            </if>
            <if test="collectibleCardName != null and collectibleCardName != ''">
                AND collectible_card_name LIKE CONCAT('%', #{collectibleCardName}, '%')
            </if>
            <if test="collectibleCardId != null">
                AND collectible_card_id = #{collectibleCardId}
            </if>
            <if test="blockType != null">
                AND block_type = #{blockType}
            </if>
        </where>
        <if test="sort != null">
            order by ${sort}
            <if test="order == 'ASC'">
                ASC
            </if>
            <if test="order == 'DESC'">
                DESC
            </if>
        </if>
    </select>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.goods.GGoods"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_goods
        where goods_id = #{goodsId,jdbcType=BIGINT} AND create_time = #{createTime,jdbcType=TIMESTAMP}
    </select>
    <select id="statusCount" resultMap="StatusResultMap">
        select * from merchant_product_status_count
        <where>
            <if test="productType != null">
                and product_type = #{productType,jdbcType=INTEGER}
            </if>
            <if test="corpInfoId != null">
                and corp_info_id = #{corpInfoId,jdbcType=BIGINT}
            </if>
        </where>
    </select>
    <select id="selectListByCorp" resultMap="BaseResultMap">
        select
        corp_info_id
        from g_goods
        <where>
            1=1
            <if test="goodsType != null">
                AND goods_type = #{goodsType,jdbcType=INTEGER}
            </if>
            <if test="soldOutStatus != null">
                AND sold_out_status = #{soldOutStatus,jdbcType=INTEGER}
            </if>
            <if test="soldStatus != null">
                AND sold_status = #{soldStatus,jdbcType=INTEGER}
            </if>
            <if test="finishStatus != null">
                AND finish_status = #{finishStatus,jdbcType=INTEGER}
            </if>
            <if test="listingStatus != null">
                AND listing_status = #{listingStatus,jdbcType=INTEGER}
            </if>
            <if test="showStatus != null">
                AND show_status = #{showStatus,jdbcType=INTEGER}
            </if>
            <if test="searchName != null">
                AND (
                goods_name like concat('%', #{searchName,jdbcType=VARCHAR}, '%')
                OR corp_name like concat('%', #{searchName,jdbcType=VARCHAR}, '%')
                )
            </if>
            AND deleted = 0
        </where>
        group by corp_info_id
    </select>
    <select id="searchCorpMerchantGoods" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from g_goods
        <where>
            <trim prefixOverrides="AND">
                <if test="goodsType != null">
                    AND goods_type = #{goodsType,jdbcType=INTEGER}
                </if>
                <if test="corpInfoId != null">
                    AND corp_info_id = #{corpInfoId,jdbcType=BIGINT}
                </if>
                <if test="soldOutStatus != null">
                    AND sold_out_status = #{soldOutStatus,jdbcType=INTEGER}
                </if>
                <if test="soldStatus != null">
                    AND sold_status = #{soldStatus,jdbcType=INTEGER}
                </if>
                <if test="finishStatus != null">
                    AND finish_status = #{finishStatus,jdbcType=INTEGER}
                </if>
                <if test="listingStatus != null">
                    AND listing_status = #{listingStatus,jdbcType=INTEGER}
                </if>
                <if test="showStatus != null">
                    AND show_status = #{showStatus,jdbcType=INTEGER}
                </if>
                <if test="searchName != null">
                    AND (
                    goods_name like concat('%', #{searchName,jdbcType=VARCHAR}, '%')
                    OR corp_name like concat('%', #{searchName,jdbcType=VARCHAR}, '%')
                    )
                </if>
                AND deleted = 0
            </trim>
        </where>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.goods.GGoods">
        delete
        from g_goods
        where goods_id = #{goodsId,jdbcType=BIGINT}
          AND create_time = #{createTime,jdbcType=TIMESTAMP}
    </delete>
    <insert id="insert">
        insert into g_goods
        ( goods_id, create_time, goods_name
        , goods_type, currency_type, unit_price
        , cost_price, sale_price, show_status
        , listing_status, listing_date, auto_listing_status
        , highest_price, lowest_price, plan_up_time
        , plan_down_time, actual_up_time, actual_down_time
        , goods_describe, remain_stock, total_stock
        , sort, block_type, series_name
        , category_id, category_name, corp_info_id
        , corp_name, corp_logo, status
        , goods_res_id, goods_image, collectible_card_id
        , collectible_image, collectible_card_name, collectible_card_unit_price
        , product_type, sold_status, sold_time
        , sold_out_status, sold_out_time, group_status
        , group_time, publicity_status, publicity_time
        , report_status, report_time, finish_status
        , finish_time, refund_status, refund_time
        , serial_group_id, random_type, audit_status
        , recycle_status, audit_time, refuse_remark
        , audit_user_id, first_buy_discount_status, first_buy_discount_amount
        , remain_random_status, remain_random_amount, unit_type
        , create_id, update_id, update_time
        , deleted)
        values ( #{goodsId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{goodsName,jdbcType=VARCHAR}
               , #{goodsType,jdbcType=TINYINT}, #{currencyType,jdbcType=TINYINT}, #{unitPrice,jdbcType=BIGINT}
               , #{costPrice,jdbcType=BIGINT}, #{salePrice,jdbcType=BIGINT}, #{showStatus,jdbcType=TINYINT}
               , #{listingStatus,jdbcType=TINYINT}, #{listingDate,jdbcType=INTEGER}
               , #{autoListingStatus,jdbcType=TINYINT}
               , #{highestPrice,jdbcType=BIGINT}, #{lowestPrice,jdbcType=BIGINT}, #{planUpTime,jdbcType=TIMESTAMP}
               , #{planDownTime,jdbcType=TIMESTAMP}, #{actualUpTime,jdbcType=TIMESTAMP}
               , #{actualDownTime,jdbcType=TIMESTAMP}
               , #{goodsDescribe,jdbcType=VARCHAR}, #{remainStock,jdbcType=BIGINT}, #{totalStock,jdbcType=BIGINT}
               , #{sort,jdbcType=INTEGER}, #{blockType,jdbcType=TINYINT}, #{seriesName,jdbcType=VARCHAR}
               , #{categoryId,jdbcType=BIGINT}, #{categoryName,jdbcType=VARCHAR}, #{corpInfoId,jdbcType=BIGINT}
               , #{corpName,jdbcType=VARCHAR}, #{corpLogo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}
               , #{goodsResId,jdbcType=BIGINT}, #{goodsImage,jdbcType=VARCHAR}, #{collectibleCardId,jdbcType=BIGINT}
               , #{collectibleImage,jdbcType=VARCHAR}, #{collectibleCardName,jdbcType=VARCHAR}
               , #{collectibleCardUnitPrice,jdbcType=BIGINT}
               , #{productType,jdbcType=TINYINT}, #{soldStatus,jdbcType=TINYINT}, #{soldTime,jdbcType=TIMESTAMP}
               , #{soldOutStatus,jdbcType=TINYINT}, #{soldOutTime,jdbcType=TIMESTAMP}, #{groupStatus,jdbcType=TINYINT}
               , #{groupTime,jdbcType=TIMESTAMP}, #{publicityStatus,jdbcType=TINYINT}
               , #{publicityTime,jdbcType=TIMESTAMP}
               , #{reportStatus,jdbcType=TINYINT}, #{reportTime,jdbcType=TIMESTAMP}, #{finishStatus,jdbcType=TINYINT}
               , #{finishTime,jdbcType=TIMESTAMP}, #{refundStatus,jdbcType=TINYINT}, #{refundTime,jdbcType=TIMESTAMP}
               , #{serialGroupId,jdbcType=BIGINT}, #{randomType,jdbcType=TINYINT}, #{auditStatus,jdbcType=TINYINT}
               , #{recycleStatus,jdbcType=TINYINT}, #{auditTime,jdbcType=TIMESTAMP}, #{refuseRemark,jdbcType=VARCHAR}
               , #{auditUserId,jdbcType=BIGINT}, #{firstBuyDiscountStatus,jdbcType=TINYINT}
               , #{firstBuyDiscountAmount,jdbcType=BIGINT}
               , #{remainRandomStatus,jdbcType=TINYINT}, #{remainRandomAmount,jdbcType=BIGINT}
               , #{unitType,jdbcType=VARCHAR}
               , #{createId,jdbcType=BIGINT}, #{updateId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}
               , #{deleted,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective">
        insert into g_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsId != null">goods_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="goodsType != null">goods_type,</if>
            <if test="currencyType != null">currency_type,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="costPrice != null">cost_price,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="showStatus != null">show_status,</if>
            <if test="listingStatus != null">listing_status,</if>
            <if test="listingDate != null">listing_date,</if>
            <if test="autoListingStatus != null">auto_listing_status,</if>
            <if test="highestPrice != null">highest_price,</if>
            <if test="lowestPrice != null">lowest_price,</if>
            <if test="planUpTime != null">plan_up_time,</if>
            <if test="planDownTime != null">plan_down_time,</if>
            <if test="actualUpTime != null">actual_up_time,</if>
            <if test="actualDownTime != null">actual_down_time,</if>
            <if test="goodsDescribe != null">goods_describe,</if>
            <if test="remainStock != null">remain_stock,</if>
            <if test="totalStock != null">total_stock,</if>
            <if test="sort != null">sort,</if>
            <if test="blockType != null">block_type,</if>
            <if test="seriesName != null">series_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="categoryName != null">category_name,</if>
            <if test="corpInfoId != null">corp_info_id,</if>
            <if test="corpName != null">corp_name,</if>
            <if test="corpLogo != null">corp_logo,</if>
            <if test="status != null">status,</if>
            <if test="goodsResId != null">goods_res_id,</if>
            <if test="goodsImage != null">goods_image,</if>
            <if test="collectibleCardId != null">collectible_card_id,</if>
            <if test="collectibleImage != null">collectible_image,</if>
            <if test="collectibleCardName != null">collectible_card_name,</if>
            <if test="collectibleCardUnitPrice != null">collectible_card_unit_price,</if>
            <if test="productType != null">product_type,</if>
            <if test="soldStatus != null">sold_status,</if>
            <if test="soldTime != null">sold_time,</if>
            <if test="soldOutStatus != null">sold_out_status,</if>
            <if test="soldOutTime != null">sold_out_time,</if>
            <if test="groupStatus != null">group_status,</if>
            <if test="groupTime != null">group_time,</if>
            <if test="publicityStatus != null">publicity_status,</if>
            <if test="publicityTime != null">publicity_time,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="reportTime != null">report_time,</if>
            <if test="finishStatus != null">finish_status,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="serialGroupId != null">serial_group_id,</if>
            <if test="randomType != null">random_type,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="recycleStatus != null">recycle_status,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="refuseRemark != null">refuse_remark,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="firstBuyDiscountStatus != null">first_buy_discount_status,</if>
            <if test="firstBuyDiscountAmount != null">first_buy_discount_amount,</if>
            <if test="remainRandomStatus != null">remain_random_status,</if>
            <if test="remainRandomAmount != null">remain_random_amount,</if>
            <if test="unitType != null">unit_type,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsId != null">#{goodsId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsType != null">#{goodsType,jdbcType=TINYINT},</if>
            <if test="currencyType != null">#{currencyType,jdbcType=TINYINT},</if>
            <if test="unitPrice != null">#{unitPrice,jdbcType=BIGINT},</if>
            <if test="costPrice != null">#{costPrice,jdbcType=BIGINT},</if>
            <if test="salePrice != null">#{salePrice,jdbcType=BIGINT},</if>
            <if test="showStatus != null">#{showStatus,jdbcType=TINYINT},</if>
            <if test="listingStatus != null">#{listingStatus,jdbcType=TINYINT},</if>
            <if test="listingDate != null">#{listingDate,jdbcType=INTEGER},</if>
            <if test="autoListingStatus != null">#{autoListingStatus,jdbcType=TINYINT},</if>
            <if test="highestPrice != null">#{highestPrice,jdbcType=BIGINT},</if>
            <if test="lowestPrice != null">#{lowestPrice,jdbcType=BIGINT},</if>
            <if test="planUpTime != null">#{planUpTime,jdbcType=TIMESTAMP},</if>
            <if test="planDownTime != null">#{planDownTime,jdbcType=TIMESTAMP},</if>
            <if test="actualUpTime != null">#{actualUpTime,jdbcType=TIMESTAMP},</if>
            <if test="actualDownTime != null">#{actualDownTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsDescribe != null">#{goodsDescribe,jdbcType=VARCHAR},</if>
            <if test="remainStock != null">#{remainStock,jdbcType=BIGINT},</if>
            <if test="totalStock != null">#{totalStock,jdbcType=BIGINT},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="blockType != null">#{blockType,jdbcType=TINYINT},</if>
            <if test="seriesName != null">#{seriesName,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=BIGINT},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="corpInfoId != null">#{corpInfoId,jdbcType=BIGINT},</if>
            <if test="corpName != null">#{corpName,jdbcType=VARCHAR},</if>
            <if test="corpLogo != null">#{corpLogo,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="goodsResId != null">#{goodsResId,jdbcType=BIGINT},</if>
            <if test="goodsImage != null">#{goodsImage,jdbcType=VARCHAR},</if>
            <if test="collectibleCardId != null">#{collectibleCardId,jdbcType=BIGINT},</if>
            <if test="collectibleImage != null">#{collectibleImage,jdbcType=VARCHAR},</if>
            <if test="collectibleCardName != null">#{collectibleCardName,jdbcType=VARCHAR},</if>
            <if test="collectibleCardUnitPrice != null">#{collectibleCardUnitPrice,jdbcType=BIGINT},</if>
            <if test="productType != null">#{productType,jdbcType=TINYINT},</if>
            <if test="soldStatus != null">#{soldStatus,jdbcType=TINYINT},</if>
            <if test="soldTime != null">#{soldTime,jdbcType=TIMESTAMP},</if>
            <if test="soldOutStatus != null">#{soldOutStatus,jdbcType=TINYINT},</if>
            <if test="soldOutTime != null">#{soldOutTime,jdbcType=TIMESTAMP},</if>
            <if test="groupStatus != null">#{groupStatus,jdbcType=TINYINT},</if>
            <if test="groupTime != null">#{groupTime,jdbcType=TIMESTAMP},</if>
            <if test="publicityStatus != null">#{publicityStatus,jdbcType=TINYINT},</if>
            <if test="publicityTime != null">#{publicityTime,jdbcType=TIMESTAMP},</if>
            <if test="reportStatus != null">#{reportStatus,jdbcType=TINYINT},</if>
            <if test="reportTime != null">#{reportTime,jdbcType=TIMESTAMP},</if>
            <if test="finishStatus != null">#{finishStatus,jdbcType=TINYINT},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="refundTime != null">#{refundTime,jdbcType=TIMESTAMP},</if>
            <if test="serialGroupId != null">#{serialGroupId,jdbcType=BIGINT},</if>
            <if test="randomType != null">#{randomType,jdbcType=TINYINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="recycleStatus != null">#{recycleStatus,jdbcType=TINYINT},</if>
            <if test="auditTime != null">#{auditTime,jdbcType=TIMESTAMP},</if>
            <if test="refuseRemark != null">#{refuseRemark,jdbcType=VARCHAR},</if>
            <if test="auditUserId != null">#{auditUserId,jdbcType=BIGINT},</if>
            <if test="firstBuyDiscountStatus != null">#{firstBuyDiscountStatus,jdbcType=TINYINT},</if>
            <if test="firstBuyDiscountAmount != null">#{firstBuyDiscountAmount,jdbcType=BIGINT},</if>
            <if test="remainRandomStatus != null">#{remainRandomStatus,jdbcType=TINYINT},</if>
            <if test="remainRandomAmount != null">#{remainRandomAmount,jdbcType=BIGINT},</if>
            <if test="unitType != null">#{unitType,jdbcType=VARCHAR},</if>
            <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
            <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="deleted != null">#{deleted,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.ewd.infrastructure.data.po.goods.GGoods">
        update g_goods
        <set>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=TINYINT},
            </if>
            <if test="currencyType != null">
                currency_type = #{currencyType,jdbcType=TINYINT},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice,jdbcType=BIGINT},
            </if>
            <if test="costPrice != null">
                cost_price = #{costPrice,jdbcType=BIGINT},
            </if>
            <if test="salePrice != null">
                sale_price = #{salePrice,jdbcType=BIGINT},
            </if>
            <if test="showStatus != null">
                show_status = #{showStatus,jdbcType=TINYINT},
            </if>
            <if test="listingStatus != null">
                listing_status = #{listingStatus,jdbcType=TINYINT},
            </if>
            <if test="listingDate != null">
                listing_date = #{listingDate,jdbcType=INTEGER},
            </if>
            <if test="autoListingStatus != null">
                auto_listing_status = #{autoListingStatus,jdbcType=TINYINT},
            </if>
            <if test="highestPrice != null">
                highest_price = #{highestPrice,jdbcType=BIGINT},
            </if>
            <if test="lowestPrice != null">
                lowest_price = #{lowestPrice,jdbcType=BIGINT},
            </if>
            <if test="planUpTime != null">
                plan_up_time = #{planUpTime,jdbcType=TIMESTAMP},
            </if>
            <if test="planDownTime != null">
                plan_down_time = #{planDownTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualUpTime != null">
                actual_up_time = #{actualUpTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualDownTime != null">
                actual_down_time = #{actualDownTime,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsDescribe != null">
                goods_describe = #{goodsDescribe,jdbcType=VARCHAR},
            </if>
            <if test="remainStock != null">
                remain_stock = #{remainStock,jdbcType=BIGINT},
            </if>
            <if test="totalStock != null">
                total_stock = #{totalStock,jdbcType=BIGINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="blockType != null">
                block_type = #{blockType,jdbcType=TINYINT},
            </if>
            <if test="seriesName != null">
                series_name = #{seriesName,jdbcType=VARCHAR},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="categoryName != null">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="corpInfoId != null">
                corp_info_id = #{corpInfoId,jdbcType=BIGINT},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="corpLogo != null">
                corp_logo = #{corpLogo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="goodsResId != null">
                goods_res_id = #{goodsResId,jdbcType=BIGINT},
            </if>
            <if test="goodsImage != null">
                goods_image = #{goodsImage,jdbcType=VARCHAR},
            </if>
            <if test="collectibleCardId != null">
                collectible_card_id = #{collectibleCardId,jdbcType=BIGINT},
            </if>
            <if test="collectibleImage != null">
                collectible_image = #{collectibleImage,jdbcType=VARCHAR},
            </if>
            <if test="collectibleCardName != null">
                collectible_card_name = #{collectibleCardName,jdbcType=VARCHAR},
            </if>
            <if test="collectibleCardUnitPrice != null">
                collectible_card_unit_price = #{collectibleCardUnitPrice,jdbcType=BIGINT},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=TINYINT},
            </if>
            <if test="soldStatus != null">
                sold_status = #{soldStatus,jdbcType=TINYINT},
            </if>
            <if test="soldTime != null">
                sold_time = #{soldTime,jdbcType=TIMESTAMP},
            </if>
            <if test="soldOutStatus != null">
                sold_out_status = #{soldOutStatus,jdbcType=TINYINT},
            </if>
            <if test="soldOutTime != null">
                sold_out_time = #{soldOutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupStatus != null">
                group_status = #{groupStatus,jdbcType=TINYINT},
            </if>
            <if test="groupTime != null">
                group_time = #{groupTime,jdbcType=TIMESTAMP},
            </if>
            <if test="publicityStatus != null">
                publicity_status = #{publicityStatus,jdbcType=TINYINT},
            </if>
            <if test="publicityTime != null">
                publicity_time = #{publicityTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus,jdbcType=TINYINT},
            </if>
            <if test="reportTime != null">
                report_time = #{reportTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishStatus != null">
                finish_status = #{finishStatus,jdbcType=TINYINT},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundStatus != null">
                refund_status = #{refundStatus,jdbcType=TINYINT},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime,jdbcType=TIMESTAMP},
            </if>
            <if test="serialGroupId != null">
                serial_group_id = #{serialGroupId,jdbcType=BIGINT},
            </if>
            <if test="randomType != null">
                random_type = #{randomType,jdbcType=TINYINT},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="recycleStatus != null">
                recycle_status = #{recycleStatus,jdbcType=TINYINT},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refuseRemark != null">
                refuse_remark = #{refuseRemark,jdbcType=VARCHAR},
            </if>
            <if test="auditUserId != null">
                audit_user_id = #{auditUserId,jdbcType=BIGINT},
            </if>
            <if test="firstBuyDiscountStatus != null">
                first_buy_discount_status = #{firstBuyDiscountStatus,jdbcType=TINYINT},
            </if>
            <if test="firstBuyDiscountAmount != null">
                first_buy_discount_amount = #{firstBuyDiscountAmount,jdbcType=BIGINT},
            </if>
            <if test="remainRandomStatus != null">
                remain_random_status = #{remainRandomStatus,jdbcType=TINYINT},
            </if>
            <if test="remainRandomAmount != null">
                remain_random_amount = #{remainRandomAmount,jdbcType=BIGINT},
            </if>
            <if test="unitType != null">
                unit_type = #{unitType,jdbcType=VARCHAR},
            </if>
            <if test="createId != null">
                create_id = #{createId,jdbcType=BIGINT},
            </if>
            <if test="updateId != null">
                update_id = #{updateId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
        </set>
        where goods_id = #{goodsId,jdbcType=BIGINT} AND create_time = #{createTime,jdbcType=TIMESTAMP}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.goods.GGoods">
        update g_goods
        set goods_name                  = #{goodsName,jdbcType=VARCHAR},
            goods_type                  = #{goodsType,jdbcType=TINYINT},
            currency_type               = #{currencyType,jdbcType=TINYINT},
            unit_price                  = #{unitPrice,jdbcType=BIGINT},
            cost_price                  = #{costPrice,jdbcType=BIGINT},
            sale_price                  = #{salePrice,jdbcType=BIGINT},
            show_status                 = #{showStatus,jdbcType=TINYINT},
            listing_status              = #{listingStatus,jdbcType=TINYINT},
            listing_date                = #{listingDate,jdbcType=INTEGER},
            auto_listing_status         = #{autoListingStatus,jdbcType=TINYINT},
            highest_price               = #{highestPrice,jdbcType=BIGINT},
            lowest_price                = #{lowestPrice,jdbcType=BIGINT},
            plan_up_time                = #{planUpTime,jdbcType=TIMESTAMP},
            plan_down_time              = #{planDownTime,jdbcType=TIMESTAMP},
            actual_up_time              = #{actualUpTime,jdbcType=TIMESTAMP},
            actual_down_time            = #{actualDownTime,jdbcType=TIMESTAMP},
            goods_describe              = #{goodsDescribe,jdbcType=VARCHAR},
            remain_stock                = #{remainStock,jdbcType=BIGINT},
            total_stock                 = #{totalStock,jdbcType=BIGINT},
            sort                        = #{sort,jdbcType=INTEGER},
            block_type                  = #{blockType,jdbcType=TINYINT},
            series_name                 = #{seriesName,jdbcType=VARCHAR},
            category_id                 = #{categoryId,jdbcType=BIGINT},
            category_name               = #{categoryName,jdbcType=VARCHAR},
            corp_info_id                = #{corpInfoId,jdbcType=BIGINT},
            corp_name                   = #{corpName,jdbcType=VARCHAR},
            corp_logo                   = #{corpLogo,jdbcType=VARCHAR},
            status                      = #{status,jdbcType=TINYINT},
            goods_res_id                = #{goodsResId,jdbcType=BIGINT},
            goods_image                 = #{goodsImage,jdbcType=VARCHAR},
            collectible_card_id         = #{collectibleCardId,jdbcType=BIGINT},
            collectible_image           = #{collectibleImage,jdbcType=VARCHAR},
            collectible_card_name       = #{collectibleCardName,jdbcType=VARCHAR},
            collectible_card_unit_price = #{collectibleCardUnitPrice,jdbcType=BIGINT},
            product_type                = #{productType,jdbcType=TINYINT},
            sold_status                 = #{soldStatus,jdbcType=TINYINT},
            sold_time                   = #{soldTime,jdbcType=TIMESTAMP},
            sold_out_status             = #{soldOutStatus,jdbcType=TINYINT},
            sold_out_time               = #{soldOutTime,jdbcType=TIMESTAMP},
            group_status                = #{groupStatus,jdbcType=TINYINT},
            group_time                  = #{groupTime,jdbcType=TIMESTAMP},
            publicity_status            = #{publicityStatus,jdbcType=TINYINT},
            publicity_time              = #{publicityTime,jdbcType=TIMESTAMP},
            report_status               = #{reportStatus,jdbcType=TINYINT},
            report_time                 = #{reportTime,jdbcType=TIMESTAMP},
            finish_status               = #{finishStatus,jdbcType=TINYINT},
            finish_time                 = #{finishTime,jdbcType=TIMESTAMP},
            refund_status               = #{refundStatus,jdbcType=TINYINT},
            refund_time                 = #{refundTime,jdbcType=TIMESTAMP},
            serial_group_id             = #{serialGroupId,jdbcType=BIGINT},
            random_type                 = #{randomType,jdbcType=TINYINT},
            audit_status                = #{auditStatus,jdbcType=TINYINT},
            recycle_status              = #{recycleStatus,jdbcType=TINYINT},
            audit_time                  = #{auditTime,jdbcType=TIMESTAMP},
            refuse_remark               = #{refuseRemark,jdbcType=VARCHAR},
            audit_user_id               = #{auditUserId,jdbcType=BIGINT},
            first_buy_discount_status   = #{firstBuyDiscountStatus,jdbcType=TINYINT},
            first_buy_discount_amount   = #{firstBuyDiscountAmount,jdbcType=BIGINT},
            remain_random_status        = #{remainRandomStatus,jdbcType=TINYINT},
            remain_random_amount        = #{remainRandomAmount,jdbcType=BIGINT},
            unit_type                   = #{unitType,jdbcType=VARCHAR},
            create_id                   = #{createId,jdbcType=BIGINT},
            update_id                   = #{updateId,jdbcType=BIGINT},
            update_time                 = #{updateTime,jdbcType=TIMESTAMP},
            deleted                     = #{deleted,jdbcType=TINYINT}
        where goods_id = #{goodsId,jdbcType=BIGINT}
          AND create_time = #{createTime,jdbcType=TIMESTAMP}
    </update>
</mapper>
