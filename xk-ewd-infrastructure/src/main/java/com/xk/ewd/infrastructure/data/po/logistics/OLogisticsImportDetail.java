package com.xk.ewd.infrastructure.data.po.logistics;

import com.xk.ewd.domain.dto.logistics.GiftCorpDto;
import com.xk.ewd.domain.model.logistics.entity.*;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName o_logistics_import_detail
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpEntity.class, convertGenerate = false),
        @AutoMapper(target = LogisticsOrderEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderAddrEntity.class, convertGenerate = false),
        @AutoMapper(target = OrderEntity.class, convertGenerate = false),
        @AutoMapper(target = UserEntity.class, convertGenerate = false),
        @AutoMapper(target = GiftCorpDto.class, convertGenerate = false),
        @AutoMapper(target = GoodsValueObject.class, convertGenerate = false),})
public class OLogisticsImportDetail {
    /**
     * 物流订单号
     */
    private Long logisticsOrderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品名
     */
    private String goodsName;

    /**
     * 商品数
     */
    private String goodsCount;

    /**
     * 物流公司名
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流订单状态
     */
    private Integer logisticsOrderStatus;

    /**
     * 异常备注
     */
    private String errRemark;

    /**
     * 任务id
     */
    private Long taskId;

    private String filePath;
}
