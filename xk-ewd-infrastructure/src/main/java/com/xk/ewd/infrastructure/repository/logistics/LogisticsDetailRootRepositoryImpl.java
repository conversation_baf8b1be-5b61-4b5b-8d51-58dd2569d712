package com.xk.ewd.infrastructure.repository.logistics;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsDetailRootRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OSendGoodsDetailMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OSendGoodsDetail;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsDetailRootRepositoryImpl implements LogisticsDetailRootRepository {

    private final Converter converter;
    private final OSendGoodsDetailMapper oSendGoodsDetailMapper;


    @Override
    public Mono<Void> save(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> save(order, OSendGoodsDetail.class,
                (v, clazz) -> order, oSendGoodsDetailMapper::insertSelective)).then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(LogisticsOrderRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(LogisticsOrderRoot root) {
        return rootToOOrder(root).flatMap(order -> update(order, OSendGoodsDetail.class,
                (v, clazz) -> order, oSendGoodsDetailMapper::updateByPrimaryKeySelective)).then();
    }

    @Override
    public Mono<Void> remove(LogisticsOrderRoot root) {
        return null;
    }

    private @NotNull Flux<OSendGoodsDetail> rootToOOrder(LogisticsOrderRoot root) {
        Long logisticsOrderId = root.getIdentifier().getLogisticsOrderId();
        return Flux.fromIterable(root.getTrajectoryValueObjectList())
                .flatMap(trajectoryValueObject -> {
                    OSendGoodsDetail sendGoodsDetail =
                            OSendGoodsDetail.builder().logisticsId(logisticsOrderId)
                                    .trackDescribe(trajectoryValueObject.getTrackDescribe())
                                    .trackType(trajectoryValueObject.getTrackType())
                                    .trackTime(trajectoryValueObject.getTrackTime()).build();
                    return Mono.just(sendGoodsDetail);
                });
    }

}
