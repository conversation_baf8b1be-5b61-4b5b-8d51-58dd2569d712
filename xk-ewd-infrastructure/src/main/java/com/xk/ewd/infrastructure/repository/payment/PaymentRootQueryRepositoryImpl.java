package com.xk.ewd.infrastructure.repository.payment;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.model.payment.entity.RefundEntity;
import com.xk.ewd.domain.repository.payment.PaymentRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.payment.ORefundMapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PaymentRootQueryRepositoryImpl implements PaymentRootQueryRepository {

    private final ORefundMapper refundMapper;

    private final Converter converter;

    @Override
    public Flux<RefundEntity> searchRefundByPage(Pagination pagination) {
        return this.search(pagination,refundMapper::selectByPage, RefundEntity.class,converter::convert);
    }
}
