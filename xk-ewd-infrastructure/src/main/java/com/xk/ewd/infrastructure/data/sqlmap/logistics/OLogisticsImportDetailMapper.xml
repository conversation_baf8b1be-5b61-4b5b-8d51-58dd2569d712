<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsImportDetailMapper">

    <resultMap id="BaseResultMap" type="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail">
        <result property="logisticsOrderId" column="logistics_order_id" jdbcType="BIGINT"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="goodsName" column="goods_name" jdbcType="VARCHAR"/>
        <result property="goodsCount" column="goods_count" jdbcType="INTEGER"/>
        <result property="logisticsCorpName" column="logistics_corp_name" jdbcType="VARCHAR"/>
        <result property="logisticsNo" column="logistics_no" jdbcType="VARCHAR"/>
        <result property="logisticsOrderStatus" column="logistics_order_status" jdbcType="VARCHAR"/>
        <result property="errRemark" column="err_remark" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        logistics_order_id
        ,order_no,goods_name,
        goods_count,logistics_corp_name,logistics_no,
        logistics_order_status,err_remark,task_id
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_logistics_import_detail
        where
    </select>
    <select id="importDetail" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from o_logistics_import_detail
        where task_id = #{fileTaskId,jdbcType=BIGINT}
    </select>
    <select id="selectCount" resultType="com.xk.ewd.domain.dto.logistics.FileTaskStatusUpdateRspDto">
        select logistics_order_status as logisticsOrderStatus, count(1) as orderCount
        from o_logistics_import_detail
        where task_id = #{taskId,jdbcType=BIGINT}
        group by logistics_order_status
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail">
        delete
        from o_logistics_import_detail
        where
    </delete>
    <insert id="insert">
        insert into o_logistics_import_detail
        ( logistics_order_id, order_no, goods_name
        , goods_count, logistics_corp_name, logistics_no
        , logistics_order_status, err_remark, task_id)
        values ( #{logisticsOrderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}
               , #{goodsCount,jdbcType=VARCHAR}, #{logisticsCorpName,jdbcType=VARCHAR}, #{logisticsNo,jdbcType=VARCHAR}
               , #{logisticsOrderStatus,jdbcType=INTEGER}, #{errRemark,jdbcType=VARCHAR}, #{taskId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective">
        insert into o_logistics_import_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logisticsOrderId != null">logistics_order_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="goodsCount != null">goods_count,</if>
            <if test="logisticsCorpName != null">logistics_corp_name,</if>
            <if test="logisticsNo != null">logistics_no,</if>
            <if test="logisticsOrderStatus != null">logistics_order_status,</if>
            <if test="errRemark != null">err_remark,</if>
            <if test="taskId != null">task_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logisticsOrderId != null">#{logisticsOrderId,jdbcType=BIGINT},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsCount != null">#{goodsCount,jdbcType=VARCHAR},</if>
            <if test="logisticsCorpName != null">#{logisticsCorpName,jdbcType=VARCHAR},</if>
            <if test="logisticsNo != null">#{logisticsNo,jdbcType=VARCHAR},</if>
            <if test="logisticsOrderStatus != null">#{logisticsOrderStatus,jdbcType=INTEGER},</if>
            <if test="errRemark != null">#{errRemark,jdbcType=VARCHAR},</if>
            <if test="taskId != null">#{taskId,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail">
        update o_logistics_import_detail
        <set>
            <if test="logisticsOrderId != null">
                logistics_order_id = #{logisticsOrderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsCount != null">
                goods_count = #{goodsCount,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCorpName != null">
                logistics_corp_name = #{logisticsCorpName,jdbcType=VARCHAR},
            </if>
            <if test="logisticsNo != null">
                logistics_no = #{logisticsNo,jdbcType=VARCHAR},
            </if>
            <if test="logisticsOrderStatus != null">
                logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            </if>
            <if test="errRemark != null">
                err_remark = #{errRemark,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=BIGINT},
            </if>
        </set>
        where
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.ewd.infrastructure.data.po.logistics.OLogisticsImportDetail">
        update o_logistics_import_detail
        set logistics_order_id     = #{logisticsOrderId,jdbcType=BIGINT},
            order_no               = #{orderNo,jdbcType=VARCHAR},
            goods_name             = #{goodsName,jdbcType=VARCHAR},
            goods_count            = #{goodsCount,jdbcType=VARCHAR},
            logistics_corp_name    = #{logisticsCorpName,jdbcType=VARCHAR},
            logistics_no           = #{logisticsNo,jdbcType=VARCHAR},
            logistics_order_status = #{logisticsOrderStatus,jdbcType=INTEGER},
            err_remark             = #{errRemark,jdbcType=VARCHAR},
            task_id = #{taskId,jdbcType=BIGINT}
        where
    </update>
    <insert id="generate">
        SELECT content
        FROM (SELECT 1                                                                                                   AS sort_order,
                     '"物流订单ID","订单编号","商品名称","商品数量","物流公司名称","物流单号","物流订单状态","错误描述"' AS content
              UNION ALL
              SELECT 2,
                     CONCAT(
                             '"', REPLACE(IFNULL(logistics_order_id, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(order_no, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(goods_name, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(goods_count, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(logistics_corp_name, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(logistics_no, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(logistics_order_status, ''), '"', '""'), '",',
                             '"', REPLACE(IFNULL(err_remark, ''), '"', '""'), '",'
                     ) AS content
              FROM o_logistics_import_detail
              where task_id = #{taskId,jdbcType=BIGINT}) t
        ORDER BY sort_order INTO OUTFILE #{filePath,jdbcType=VARCHAR} FORMAT AS 'csv'
    </insert>
</mapper>
