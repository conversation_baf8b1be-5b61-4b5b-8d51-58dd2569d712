package com.xk.ewd.infrastructure.adapter.order;

import org.springframework.stereotype.Service;

import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.ewd.domain.repository.order.OrderQueryRepository;
import com.xk.ewd.domain.repository.order.OrderRootRepository;
import com.xk.ewd.domain.service.order.OrderAdapterService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderAdapterAdapterSearchServiceImpl implements OrderAdapterService {

    private final OrderRootRepository orderRootRepository;

    private final OrderQueryRepository orderQueryRepository;

    @Override
    public Mono<Void> create(Mono<StringIdentifier> identifierMono) {
        return orderQueryRepository.getOrderById(identifierMono).flatMap(orderRootRepository::save);
    }

    @Override
    public Mono<Void> update(Mono<StringIdentifier> identifierMono) {
        return orderQueryRepository.getOrderById(identifierMono)
                .flatMap(orderRootRepository::update);
    }


}
