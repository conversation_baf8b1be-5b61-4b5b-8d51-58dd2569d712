package com.xk.ewd.infrastructure.repository.order;

import org.springframework.stereotype.Repository;

import com.myco.mydata.domain.model.Identifier;
import com.xk.ewd.domain.model.order.OrderLogRoot;
import com.xk.ewd.domain.repository.order.OrderLogRootRepository;
import com.xk.ewd.infrastructure.data.persistence.order.OOrderLogMapper;
import com.xk.ewd.infrastructure.data.po.order.OOrderLog;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class OrderLogRootRepositoryImpl implements OrderLogRootRepository {

    private final OOrderLogMapper oOrderLogMapper;
    private final Converter converter;

    @Override
    public Mono<Void> save(OrderLogRoot root) {
        return Mono.justOrEmpty(root.getOrderLogEntityList()).flatMapMany(Flux::fromIterable)
                .flatMap(order -> save(order, OOrderLog.class, converter::convert,
                        oOrderLogMapper::insertSelective))
                .then();
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(OrderLogRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(OrderLogRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(OrderLogRoot root) {
        return null;
    }
}
