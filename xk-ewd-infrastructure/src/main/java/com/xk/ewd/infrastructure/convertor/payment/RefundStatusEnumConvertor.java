package com.xk.ewd.infrastructure.convertor.payment;

import com.xk.order.enums.payment.RefundStatusEnum;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class RefundStatusEnumConvertor {

    public static RefundStatusEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return RefundStatusEnum.getByCode(code);
    }

    public static Integer map(RefundStatusEnum refundStatusEnum) {
        if (refundStatusEnum == null) {
            return null;
        }
        return refundStatusEnum.getCode();
    }
}
