package com.xk.ewd.infrastructure.convertor.logistics;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.model.logistics.entity.*;
import com.xk.ewd.domain.model.logistics.id.LogisticsOrderIdentifier;
import com.xk.ewd.domain.model.logistics.valobj.GoodsValueObject;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsOrder;

import io.github.linpeilie.BaseMapper;

/**
 * OrderRoot到SendGoodsEntity的转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OLogisticsOrderToLogisticsOrderRootMapper
        implements BaseMapper<OLogisticsOrder, LogisticsOrderRoot> {

    @Override
    public LogisticsOrderRoot convert(OLogisticsOrder source) {
        return convert(source,
                LogisticsOrderRoot.builder()
                        .identifier(
                                LogisticsOrderIdentifier.builder().logisticsOrderId(-1L).build())
                        .build());
    }

    @Override
    public LogisticsOrderRoot convert(OLogisticsOrder source, LogisticsOrderRoot target) {
        if (source == null) {
            return null;
        }

        return LogisticsOrderRoot.builder()
                .identifier(LogisticsOrderIdentifier.builder()
                        .logisticsOrderId(source.getLogisticsOrderId()).build())
                .corpEntity(CorpEntity.builder().corpId(source.getCorpId())
                        .corpName(source.getCorpName()).build())
                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                        .logisticsOrderId(source.getLogisticsOrderId())
                        .logisticsOrderType(source.getLogisticsOrderType())
                        .logisticsCorpName(source.getLogisticsCorpName())
                        .logisticsNo(source.getLogisticsNo())
                        .logisticsOrderStatus(source.getLogisticsOrderStatus()).build())
                .orderAddrEntity(OrderAddrEntity.builder().userAddressId(source.getUserAddressId())
                        .receivingMobile(source.getReceivingMobile())
                        .consigneeName(source.getConsigneeName())
                        .addressSite(source.getAddressSite())
                        .addressDetail(source.getAddressDetail()).build())
                .orderEntity(OrderEntity.builder().orderNo(source.getOrderNo())
                        .freeQuotaDiscountAmount(source.getFreeQuotaDiscountAmount())
                        .payNo(source.getPayNo()).shippingFee(source.getShippingFee())
                        .orderTotalBuyCount(source.getOrderTotalBuyCount())
                        .totalAmount(source.getTotalAmount())
                        .discountAmount(source.getDiscountAmount())
                        .corpDicountAmount(source.getCorpDicountAmount())
                        .couponAmount(source.getCouponAmount())
                        .otherDicountAmount(source.getOtherDicountAmount())
                        .payAmount(source.getPayAmount()).orderType(source.getOrderType())
                        .payType(source.getPayType()).orderStatus(source.getOrderStatus())
                        .remindShippingStatus(source.getRemindShippingStatus())
                        .deleted(source.getDeleted())
                        .createTime(source.getCreateTime()).payTime(source.getPayTime())
                        .sendGoodsTime(source.getSendGoodsTime())
                        .remindShippingStatus(source.getRemindShippingStatus()).build())
                .userEntity(UserEntity.builder().userId(source.getUserId())
                        .userNick(source.getUserNick()).mobile(source.getMobile())
                        .userLogo(source.getUserLogo()).build())
                .goodsValueObject(GoodsValueObject.builder().goodsInfo(source.getGoodsInfo())
                        .giftInfo(source.getGiftInfo()).goodsName(source.getGoodsName())
                        .goodsCount(source.getGoodsCount()).build())
                .build();
    }
}
