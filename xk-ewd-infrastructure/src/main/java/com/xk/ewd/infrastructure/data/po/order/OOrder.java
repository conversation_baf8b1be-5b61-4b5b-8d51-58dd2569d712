package com.xk.ewd.infrastructure.data.po.order;

import java.util.Date;

import com.xk.ewd.domain.model.order.entity.*;
import com.xk.ewd.domain.model.order.valobj.GoodsValueObject;

import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单表
 *
 * @TableName o_order
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({@AutoMapper(target = CorpEntity.class),
        @AutoMapper(target = LogisticsOrderEntity.class),
        @AutoMapper(target = OrderAddrEntity.class), @AutoMapper(target = OrderEntity.class),
        @AutoMapper(target = UserEntity.class), @AutoMapper(target = GoodsValueObject.class),})
public class OOrder {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 免费额度减免金额
     */
    private Long freeQuotaDiscountAmount;

    /**
     * 物流订单id
     */
    private Long logisticsOrderId;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 物流公司名
     */
    private String logisticsCorpName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 运费
     */
    private Long shippingFee;

    /**
     * 商品信息
     */
    private String goodsInfo;

    /**
     * 赠品信息
     */
    private String giftInfo;

    /**
     * 收货人手机号
     */
    private String receivingMobile;

    /**
     * 订单商品总数
     */
    private Integer orderTotalBuyCount;

    /**
     * 商品总金额
     */
    private Long totalAmount;

    /**
     * 满减金额
     */
    private Long discountAmount;

    /**
     * 商家优惠金额
     */
    private Long corpDicountAmount;

    /**
     * 优惠券减免金额
     */
    private Long couponAmount;

    /**
     * 商品优惠总金额
     */
    private Long otherDicountAmount;

    /**
     * 实付金额
     */
    private Long payAmount;

    /**
     * 商户ID
     */
    private Long corpId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户地址ID
     */
    private Long userAddressId;

    /**
     * 商户名称
     */
    private String corpName;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 订单类型：1-商城订单 2-物料订单 3-商品订单
     */
    private Integer orderType;

    /**
     * 物流订单类型：1-商城订单 2-物料订单 3-商品订单 4-赠品订单
     */
    private Integer logisticsOrderType;

    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    private Integer deleted;

    /**
     * 支付方式：1-银行卡 2-支付宝 3-微信支付
     */
    private Integer payType;

    /**
     * 订单状态：1-待付款 2-售卖中 3-待公布 4-已完成 5-未售罄 6-已取消
     */
    private Integer orderStatus;

    /**
     * 物流订单状态 1、待发货2、待收货3、已收货
     */
    private Integer logisticsOrderStatus;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;


    /**
     * 支付成功时间
     */
    private Date payTime;

    /**
     * 退款状态 1-无退款 2-退款中 3-已退款
     */
    private Integer refundStatus;

    /**
     * 发货时间
     */
    private Date sendGoodsTime;

    /**
     * 发货数量
     */
    private Integer shipTotalCount;

    /**
     * 是否中奖
     */
    private Integer prizeStatus;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 支付渠道类型
     */
    private Integer platformType;

    /**
     * 催发货状态：0-未催发 1-已催发
     */
    private Integer remindShippingStatus;
}
