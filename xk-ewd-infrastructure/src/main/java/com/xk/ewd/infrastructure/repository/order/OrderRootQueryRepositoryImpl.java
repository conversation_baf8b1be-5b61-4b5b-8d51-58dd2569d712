package com.xk.ewd.infrastructure.repository.order;

import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.dto.order.OrderStatisticsDto;
import com.xk.ewd.domain.model.order.OrderRoot;
import com.xk.ewd.domain.repository.order.OrderRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.order.OOrderMapper;
import com.xk.ewd.infrastructure.data.po.order.OOrder;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class OrderRootQueryRepositoryImpl implements OrderRootQueryRepository {

    private final Converter converter;
    private final OOrderMapper oOrderMapper;

    @Override
    public Flux<OrderRoot> selectList(Pagination pagination) {
        return search(pagination, oOrderMapper::selectByPage, OrderRoot.class, converter::convert);
    }

    @Override
    public Mono<OrderStatisticsDto> searchStatistics(OrderRoot orderRoot) {
        OrderStatisticsDto addr = oOrderMapper
                .searchStatistics(OOrder.builder().corpId(orderRoot.getCorpEntity().getCorpId())
                        .orderType(orderRoot.getOrderEntity().getOrderType())
                        .startTime(orderRoot.getOrderEntity().getStartTime())
                        .endTime(orderRoot.getOrderEntity().getEndTime()).build());
        return Mono.just(addr);
    }
}
