package com.xk.ewd.infrastructure.convertor.payment;

import com.xk.order.enums.payment.RefundTypeEnum;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class RefundTypeEnumConvertor {

    public static RefundTypeEnum map(Integer code) {
        if (code == null) {
            return null;
        }
        return RefundTypeEnum.getByCode(code);
    }

    public static Integer map(RefundTypeEnum refundTypeEnum) {
        if (refundTypeEnum == null) {
            return null;
        }
        return refundTypeEnum.getCode();
    }
}
