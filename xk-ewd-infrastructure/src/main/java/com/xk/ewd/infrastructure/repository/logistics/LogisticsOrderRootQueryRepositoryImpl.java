package com.xk.ewd.infrastructure.repository.logistics;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.myco.framework.support.mybatis.Pagination;
import com.xk.ewd.domain.dto.logistics.AppAddrDto;
import com.xk.ewd.domain.dto.logistics.GiftCorpDto;
import com.xk.ewd.domain.dto.logistics.LogisticsUserViewDto;
import com.xk.ewd.domain.model.logistics.LogisticsOrderRoot;
import com.xk.ewd.domain.repository.logistics.LogisticsOrderRootQueryRepository;
import com.xk.ewd.infrastructure.data.persistence.logistics.OLogisticsOrderMapper;
import com.xk.ewd.infrastructure.data.po.logistics.OAppAddr;
import com.xk.ewd.infrastructure.data.po.logistics.OLogisticsOrder;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
@RequiredArgsConstructor
public class LogisticsOrderRootQueryRepositoryImpl implements LogisticsOrderRootQueryRepository {

    private final Converter converter;
    private final OLogisticsOrderMapper oLogisticsOrderMapper;

    @Override
    public Flux<LogisticsOrderRoot> selectList(Pagination pagination) {
        return search(pagination, oLogisticsOrderMapper::selectList, LogisticsOrderRoot.class,
                converter::convert);
    }

    @Override
    public Flux<AppAddrDto> addr(LogisticsOrderRoot logisticsOrderRoot) {
        List<OAppAddr> addr = oLogisticsOrderMapper.addr(
                OLogisticsOrder.builder().userId(logisticsOrderRoot.getUserEntity().getUserId())
                        .corpId(logisticsOrderRoot.getCorpEntity().getCorpId()).build());
        return CollectionUtils.isEmpty(addr) ? Flux.empty()
                : Flux.fromIterable(addr).map(x -> converter.convert(x, AppAddrDto.class));
    }

    @Override
    public Flux<GiftCorpDto> giftCorpsOrder(Pagination pagination) {
        return search(pagination, oLogisticsOrderMapper::giftCorpsOrder, GiftCorpDto.class,
                converter::convert);
    }

    @Override
    public Flux<LogisticsUserViewDto> userViewCorp(Pagination pagination) {
        return search(pagination, oLogisticsOrderMapper::userViewCorp, LogisticsUserViewDto.class,
                converter::convert);
    }

    @Override
    public Mono<LogisticsOrderRoot> selectById(LogisticsOrderRoot record) {
        OLogisticsOrder order = OLogisticsOrder.builder()
                .logisticsOrderId(record.getIdentifier().getLogisticsOrderId()).build();
        OLogisticsOrder oLogisticsOrder = oLogisticsOrderMapper.selectByPrimaryKey(order);
        return oLogisticsOrder == null ? Mono.empty()
                : Mono.just(converter.convert(oLogisticsOrder, LogisticsOrderRoot.class));
    }

    @Override
    public Mono<Long> getWeekTotalRecords() {
        return Mono.just(oLogisticsOrderMapper.getWeekTotalRecords());
    }

    @Override
    public Flux<LogisticsOrderRoot> getWeekRecords(Pagination pagination) {
        return search(pagination, oLogisticsOrderMapper::getWeekRecords, LogisticsOrderRoot.class,
                converter::convert);
    }
}
