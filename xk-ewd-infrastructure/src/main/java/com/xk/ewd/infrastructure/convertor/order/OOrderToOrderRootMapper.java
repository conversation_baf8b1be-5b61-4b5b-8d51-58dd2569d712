package com.xk.ewd.infrastructure.convertor.order;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

import com.xk.ewd.domain.model.order.OrderRoot;
import com.xk.ewd.domain.model.order.entity.*;
import com.xk.ewd.domain.model.order.id.OrderIdentifier;
import com.xk.ewd.domain.model.order.valobj.GoodsValueObject;
import com.xk.ewd.infrastructure.data.po.order.OOrder;

import io.github.linpeilie.BaseMapper;

/**
 * OrderRoot到SendGoodsEntity的转换器
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public class OOrderToOrderRootMapper implements BaseMapper<OOrder, OrderRoot> {

    @Override
    public OrderRoot convert(OOrder source) {
        return convert(source, OrderRoot.builder()
                .identifier(OrderIdentifier.builder().orderNo("").build()).build());
    }

    @Override
    public OrderRoot convert(OOrder source, OrderRoot target) {
        if (source == null) {
            return null;
        }

        return OrderRoot.builder()
                .identifier(OrderIdentifier.builder().orderNo(source.getOrderNo()).build())
                .corpEntity(CorpEntity.builder().corpId(source.getCorpId())
                        .corpName(source.getCorpName()).build())
                .logisticsOrderEntity(LogisticsOrderEntity.builder()
                        .logisticsOrderId(source.getLogisticsOrderId())
                        .logisticsOrderType(source.getLogisticsOrderType())
                        .logisticsCorpName(source.getLogisticsCorpName())
                        .logisticsNo(source.getLogisticsNo())
                        .logisticsOrderStatus(source.getLogisticsOrderStatus())
                        .sendGoodsTime(source.getSendGoodsTime()).build())
                .orderAddrEntity(OrderAddrEntity.builder().userAddressId(source.getUserAddressId())
                        .receivingMobile(source.getReceivingMobile())
                        .consigneeName(source.getConsigneeName())
                        .addressSite(source.getAddressSite())
                        .addressDetail(source.getAddressDetail()).build())
                .orderEntity(OrderEntity.builder().orderNo(source.getOrderNo())
                        .freeQuotaDiscountAmount(source.getFreeQuotaDiscountAmount())
                        .payNo(source.getPayNo()).shippingFee(source.getShippingFee())
                        .orderTotalBuyCount(source.getOrderTotalBuyCount())
                        .totalAmount(source.getTotalAmount())
                        .discountAmount(source.getDiscountAmount())
                        .corpDicountAmount(source.getCorpDicountAmount())
                        .couponAmount(source.getCouponAmount())
                        .otherDicountAmount(source.getOtherDicountAmount())
                        .platformType(source.getPlatformType()).payStatus(source.getPayStatus())
                        .deleted(source.getDeleted())
                        .payAmount(source.getPayAmount()).orderType(source.getOrderType())
                        .payType(source.getPayType()).orderStatus(source.getOrderStatus())
                        .createTime(source.getCreateTime()).payTime(source.getPayTime())
                        .refundStatus(source.getRefundStatus())
                        .shipTotalCount(source.getShipTotalCount())
                        .prizeStatus(source.getPrizeStatus()).build())
                .userEntity(UserEntity.builder().userId(source.getUserId())
                        .userNick(source.getUserNick()).mobile(source.getMobile()).build())
                .goodsValueObject(GoodsValueObject.builder().goodsInfo(source.getGoodsInfo())
                        .goodsId(source.getGoodsId()).goodsName(source.getGoodsName())
                        .productType(source.getProductType())
                        .giftInfo(source.getGiftInfo()).build())
                .build();
    }
}
