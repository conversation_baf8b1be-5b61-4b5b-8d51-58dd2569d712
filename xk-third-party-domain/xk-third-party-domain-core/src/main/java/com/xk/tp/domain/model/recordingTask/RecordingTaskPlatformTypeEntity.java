package com.xk.tp.domain.model.recordingTask;

import java.util.Map;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;
import com.xk.tp.domain.model.recordingTask.ids.RecordingTaskIdentifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;

import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingTaskPlatformTypeEntity implements Entity<RecordingTaskIdentifier> {

    private Long accessAccountId;

    private LivePlatformTypeEnum livePlatformTypeEnum;

    /**
     * 账号配置
     */
    private Map<String, String> config;


    @Override
    public @NonNull RecordingTaskIdentifier getIdentifier() {
        return RecordingTaskIdentifier.builder().livePlatformTypeEnum(livePlatformTypeEnum).build();
    }

    @Override
    public Validatable<RecordingTaskIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
