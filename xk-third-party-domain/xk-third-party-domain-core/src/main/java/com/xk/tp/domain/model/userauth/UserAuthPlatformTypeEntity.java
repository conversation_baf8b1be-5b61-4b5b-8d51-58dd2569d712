package com.xk.tp.domain.model.userauth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.userauth.ids.UserAuthIdentifier;
import com.xk.tp.enums.access.UserAuthChannelTypeEnum;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthPlatformTypeEntity implements Entity<UserAuthIdentifier> {

    private Long userAuthId;

    private Long accessAccountId;

    private UserAuthChannelTypeEnum channelType;

    @Override
    public @NonNull UserAuthIdentifier getIdentifier() {
        return UserAuthIdentifier.builder().userAuthId(userAuthId).build();
    }

    @Override
    public Validatable<UserAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
