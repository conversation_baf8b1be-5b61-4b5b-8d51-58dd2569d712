package com.xk.tp.domain.model.sem;

import lombok.Getter;

import java.util.Arrays;


/**
 * <AUTHOR>
 */

@Getter
public enum SemSupplierEnum {

    BAIDU("baidu", "百度"),
    ;


    private String value;
    private String name;


    SemSupplierEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static SemSupplierEnum getEnumByValue(String value) {
        return Arrays.stream(SemSupplierEnum.values())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

}
