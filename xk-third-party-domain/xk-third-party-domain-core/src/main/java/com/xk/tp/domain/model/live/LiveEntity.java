package com.xk.tp.domain.model.live;

import java.util.List;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveEntity implements Entity<LiveIdentifier> {

    private LivePlatformTypeEnum livePlatformTypeEnum;

    private String liveId;

    private LiveUserTypeEnum liveUserTypeEnum;

    private List<String> userIds;

    @Override
    public @NonNull LiveIdentifier getIdentifier() {
        return LiveIdentifier.builder().liveId(liveId).livePlatformTypeEnum(livePlatformTypeEnum)
                .liveUserTypeEnum(liveUserTypeEnum)
                .build();
    }

    @Override
    public Validatable<LiveIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
