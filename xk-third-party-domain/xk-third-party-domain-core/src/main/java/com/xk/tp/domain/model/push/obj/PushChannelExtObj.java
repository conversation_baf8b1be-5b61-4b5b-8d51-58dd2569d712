package com.xk.tp.domain.model.push.obj;

import com.xk.tp.enums.access.AccessChannelTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送渠道补充信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/11 15:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushChannelExtObj {

    /**
     * 账号id
     */
    private Long accessAccountId;

    /**
     * 渠道id
     */
    private Long accessId;

    /**
     * 推送渠道
     */
    private AccessChannelTypeEnum pushChannelType;

    /**
     * 通知的图标名称，包含后缀名（需要在客户端开发时嵌入），如“push.png”，长度 ≤ 64字
     */
    private String logo;

    /**
     * 通知图标URL地址，长度 ≤ 256字
     */
    private String logoUrl;

    /**
     * appId
     */
    private String appId;

    /**
     * appKey
     */
    private String appKey;

    /**
     * masterSecret
     */
    private String masterSecret;

    /**
     * 接口调用前缀
     */
    private String domain;
}
