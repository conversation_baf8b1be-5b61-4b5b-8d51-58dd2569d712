package com.xk.tp.domain.model.live;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class LiveRoot extends DomainRoot<LiveIdentifier> {

    private LiveEntity liveEntity;

    @Builder
    public LiveRoot(LiveIdentifier identifier, LiveEntity liveEntity) {
        super(identifier);
        this.liveEntity = liveEntity;
    }

    @Override
    public Validatable<LiveIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
