package com.xk.tp.domain.model.account.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountIdentifier  implements Identifier<AccountIdentifier> {

    private Long accountId;

    @Override
    public @NonNull AccountIdentifier getIdentifier() {
        return AccountIdentifier.builder().accountId(accountId).build();
    }
}
