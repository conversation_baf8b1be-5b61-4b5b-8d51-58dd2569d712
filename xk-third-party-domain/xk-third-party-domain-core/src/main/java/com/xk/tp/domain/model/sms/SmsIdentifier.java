package com.xk.tp.domain.model.sms;

import com.myco.mydata.domain.model.Identifier;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Builder
@Getter
public class SmsIdentifier implements Identifier<SmsIdentifier> {

    private Long accessAccountId;

    @NonNull
    @Override
    public SmsIdentifier getIdentifier() {
        return SmsIdentifier.builder().accessAccountId(accessAccountId).build();
    }
}
