package com.xk.tp.domain.model.auth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserEntity implements Entity<String> {

    private Long userId;
    private String name;
    private String mobile;
    private String idCard;

    @Override
    public @NonNull String getIdentifier() {
        return this.idCard;
    }

    @Override
    public Validatable<String> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
