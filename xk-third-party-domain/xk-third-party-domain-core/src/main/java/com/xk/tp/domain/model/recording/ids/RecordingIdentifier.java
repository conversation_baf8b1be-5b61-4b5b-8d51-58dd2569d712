package com.xk.tp.domain.model.recording.ids;

import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingIdentifier implements Identifier<RecordingIdentifier> {

    /**
     * 直播平台
     */
    private LivePlatformTypeEnum livePlatformTypeEnum;

    /**
     * 直播间ID
     */
    private String liveId;

    private LiveUserTypeEnum liveUserTypeEnum;

    /**
     * 录播任务ID
     */
    private String recordingTaskId;

    @NonNull
    @Override
    public RecordingIdentifier getIdentifier() {
        return RecordingIdentifier.builder().liveId(liveId)
                .livePlatformTypeEnum(livePlatformTypeEnum).liveUserTypeEnum(liveUserTypeEnum)
                .build();
    }

}
