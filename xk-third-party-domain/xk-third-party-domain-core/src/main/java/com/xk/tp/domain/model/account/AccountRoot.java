package com.xk.tp.domain.model.account;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.account.ids.AccountIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class AccountRoot extends DomainRoot<AccountIdentifier> {

    private AccountEntity accountEntity;

    private List<AccountEntity> routeAccountEntityList;

    @Builder
    public AccountRoot(AccountIdentifier identifier, AccountEntity accountEntity,List<AccountEntity> routeAccountEntityList) {
        super(identifier);
        this.accountEntity = accountEntity;
        this.routeAccountEntityList = routeAccountEntityList;
    }

    @Override
    public Validatable<AccountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
