package com.xk.tp.domain.model.log;

import com.myco.framework.cache.annotations.KeyProperty;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class UseLogEntity implements Entity<LongIdentifier> {

    /**
     * 记录id
     */
    @KeyProperty
    private Long logId;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * url地址
     */
    private String apiUrl;

    /**
     * 参数
     */
    private String params;

    /**
     * 响应参数
     */
    private String response;

    /**
     * 业务类型
     */
    private Integer busiType;

    /**
     * 业务id
     */
    private String busiId;

    /**
     * 具体业务类型 商品审核  担担查找
     */
    private Integer cateType;

    /**
     * 来源平台
     */
    private String sourcePlatform;

    /**
     * 目标平台
     */
    private Long targetAccessId;


    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(logId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
