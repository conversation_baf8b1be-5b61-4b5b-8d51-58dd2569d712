package com.xk.tp.domain.model.im;

import java.util.Map;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.im.ids.ImIdentifier;
import com.xk.tp.domain.model.logistics.id.LogisticsIdentifier;
import com.xk.tp.enums.im.ImPlatformTypeEnum;
import com.xk.tp.enums.logistics.LogisticsApiEnum;

import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImPlatformTypeEntity implements Entity<ImIdentifier> {


    private Long accessAccountId;

    private ImPlatformTypeEnum imPlatformTypeEnum;

    /**
     * 账号配置
     */
    private Map<String, String> config;


    @Override
    public @NonNull ImIdentifier getIdentifier() {
        return ImIdentifier.builder().imPlatformTypeEnum(imPlatformTypeEnum).build();
    }

    @Override
    public Validatable<ImIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
