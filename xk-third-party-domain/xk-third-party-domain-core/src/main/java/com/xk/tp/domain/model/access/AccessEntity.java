package com.xk.tp.domain.model.access;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

/**
 * ApiConfigExt
 *
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
public class AccessEntity implements Entity<AccessIdentifier> {

    /**
     * 通道id
     */
    private Long accessId;

    /**
     * 通道名称
     */
    private String accessName;

    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 渠道类型
     */
    private AccessChannelTypeEnum channelType;

    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;

    /**
     * 业务分组
     */
    private BusinessGroupEnum businessGroup;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateId;

    private Integer status;

    private Map<String, AccessExtValObj> accessExtValObjMap;

    private List<AccessAccountEntity> accessAccountEntityList;


    @Override
    public @NonNull AccessIdentifier getIdentifier() {
        return AccessIdentifier.builder().accessId(accessId).build();
    }

    @Override
    public Validatable<AccessIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

    public Map<String, String> getConfig() {
        if (accessExtValObjMap == null) {
            return Map.of();
        }
        return accessExtValObjMap.values().stream().collect(
                Collectors.toMap(AccessExtValObj::getFieldKey, AccessExtValObj::getFiledValue));
    }
}
