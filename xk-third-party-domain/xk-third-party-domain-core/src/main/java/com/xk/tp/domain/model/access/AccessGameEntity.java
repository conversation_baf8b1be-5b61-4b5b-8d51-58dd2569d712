package com.xk.tp.domain.model.access;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.access.AccessGameStatusEnum;
import com.xk.tp.domain.model.access.ids.AccessGameIdentifier;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessGameEntity implements Entity<AccessGameIdentifier> {

    private Long accessId;

    private Long gameId;

    private String tpGameId;

    private String tpGameName;

    private AccessGameStatusEnum status;

    @Override
    public @NonNull AccessGameIdentifier getIdentifier() {
        return AccessGameIdentifier.builder().accessId(accessId).tpGameId(tpGameId).build();
    }

    @Override
    public Validatable<AccessGameIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
