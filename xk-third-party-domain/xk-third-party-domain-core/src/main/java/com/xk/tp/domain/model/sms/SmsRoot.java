package com.xk.tp.domain.model.sms;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;

import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsRoot extends DomainRoot<SmsIdentifier> {

    private SmsEntity smsEntity;

    private SendSmsEntity sendSmsEntity;

    @Builder
    public SmsRoot(SmsIdentifier identifier, SmsEntity smsEntity, SendSmsEntity sendSmsEntity) {
        super(identifier);
        this.smsEntity = smsEntity;
        this.sendSmsEntity = sendSmsEntity;
    }

    @Override
    public Validatable<SmsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
