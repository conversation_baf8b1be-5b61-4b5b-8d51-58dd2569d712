package com.xk.tp.domain.commons;


import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * domain相关错误码
 * 定义错误码区间 11000-11999
 *
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum XkThirdPartyDomainErrorEnum implements ExceptionIdentifier {
    DOMAIN_ERROR(11000, "domain错误"),

    PAY_NOTIFY_SEND_EVENT_ERROR(11003, "支付发送事件失败"),
    CORP_SECRET_CREATE_ERROR(11004, "创建企业密钥失败"),
    ACCESS_ACCOUNT_NOT_EXIST(11006, "未查找到可用的账号信息"),
    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
