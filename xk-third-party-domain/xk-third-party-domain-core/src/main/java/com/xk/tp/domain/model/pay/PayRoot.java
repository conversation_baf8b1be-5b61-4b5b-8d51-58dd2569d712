package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.pay.ids.PayIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class PayRoot extends DomainRoot<PayIdentifier> {

    private PayPlatformTypeEntity payPlatformTypeEntity;

    private PreCreateEntity preCreateEntity;
    private CloseOrderEntity closeOrderEntity;
    private CallBackValObj callBackValObj;
    private RefundOrderEntity refundOrderEntity;
    private RefundCallValObj refundCallValObj;
    private RevokeOrderEntity revokeOrderEntity;

    private PayNotifyEntity payNotifyEntity;

    private PayRecordEntity payRecordEntity;

    private PayResultValObj payResultValObj;

    @Builder
    public PayRoot(PayIdentifier identifier, PayPlatformTypeEntity payPlatformTypeEntity,
                   PreCreateEntity preCreateEntity, CloseOrderEntity closeOrderEntity, CallBackValObj callBackValObj,
                   RefundOrderEntity refundOrderEntity, RefundCallValObj refundCallValObj,
                   RevokeOrderEntity revokeOrderEntity, PayNotifyEntity payNotifyEntity,
                   PayRecordEntity payRecordEntity,PayResultValObj payResultValObj) {
        super(identifier);
        this.payPlatformTypeEntity = payPlatformTypeEntity;
        this.preCreateEntity = preCreateEntity;
        this.closeOrderEntity = closeOrderEntity;
        this.callBackValObj = callBackValObj;
        this.refundOrderEntity = refundOrderEntity;
        this.refundCallValObj = refundCallValObj;
        this.revokeOrderEntity = revokeOrderEntity;
        this.payNotifyEntity = payNotifyEntity;
        this.payRecordEntity = payRecordEntity;
        this.payResultValObj = payResultValObj;
    }

    @Override
    public Validatable<PayIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }


    public String converPrice(Long price) {
        if (payPlatformTypeEntity.getBit() == 0) {
            return new BigDecimal(price.toString()).setScale(payPlatformTypeEntity.getReservedBit(), RoundingMode.DOWN)
                    .toString();
        }
        return new BigDecimal(price.toString())
                .divide(BigDecimal.valueOf((int) Math.pow(10, payPlatformTypeEntity.getBit())),
                        payPlatformTypeEntity.getReservedBit(), RoundingMode.HALF_UP)
                .toString();
    }

    public Long getLongPrice(String price) {
        return new BigDecimal(price).longValue();
    }

    public double getDoublePrice(String price) {
        return new BigDecimal(price).doubleValue();
    }
}
