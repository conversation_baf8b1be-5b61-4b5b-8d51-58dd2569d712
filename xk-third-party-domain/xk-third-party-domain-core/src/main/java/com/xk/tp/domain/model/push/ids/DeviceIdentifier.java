package com.xk.tp.domain.model.push.ids;


import com.myco.mydata.domain.model.Identifier;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Builder
@Getter
public class DeviceIdentifier implements Identifier<DeviceIdentifier> {


    private String deviceId;

    @Override
    public @NonNull DeviceIdentifier getIdentifier() {
        return DeviceIdentifier.builder().deviceId(deviceId).build();
    }
}
