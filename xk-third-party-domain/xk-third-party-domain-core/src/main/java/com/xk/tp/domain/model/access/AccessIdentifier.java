package com.xk.tp.domain.model.access;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessIdentifier implements Identifier<AccessIdentifier> {

    private final Long accessId;

    @Override
    public @NonNull AccessIdentifier getIdentifier() {
        return AccessIdentifier.builder().accessId(accessId).build();
    }
}
