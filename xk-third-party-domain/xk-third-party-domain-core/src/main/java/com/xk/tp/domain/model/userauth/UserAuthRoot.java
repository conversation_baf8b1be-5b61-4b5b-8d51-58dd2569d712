package com.xk.tp.domain.model.userauth;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.userauth.ids.UserAuthIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class UserAuthRoot extends DomainRoot<UserAuthIdentifier> {

    private PreUserAuthEntity preUserAuthEntity;
    private UserAuthAccessTokenEntity userAuthAccessTokenEntity;
    private UserAuthInfoEntity  userAuthInfoEntity;

    private UserAuthPlatformTypeEntity userAuthPlatformTypeEntity;
    private UserAuthConfigValObj userAuthConfigValObj;

    @Builder
    public UserAuthRoot(UserAuthIdentifier userAuthIdentifier,PreUserAuthEntity preUserAuthEntity,UserAuthAccessTokenEntity userAuthAccessTokenEntity,UserAuthInfoEntity userAuthInfoEntity,
                        UserAuthPlatformTypeEntity userAuthPlatformTypeEntity, UserAuthConfigValObj userAuthConfigValObj) {
        super(userAuthIdentifier);
        this.preUserAuthEntity = preUserAuthEntity;
        this.userAuthAccessTokenEntity = userAuthAccessTokenEntity;
        this.userAuthInfoEntity = userAuthInfoEntity;
        this.userAuthPlatformTypeEntity = userAuthPlatformTypeEntity;
        this.userAuthConfigValObj = userAuthConfigValObj;
    }


    @Override
    public Validatable<UserAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
