package com.xk.tp.domain.model.thirdcaptcha;

import com.xk.tp.enums.thirdcaptcha.CaptchaTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThirdCheckCaptchaEntity {

    /**
     * 用户验证票据
     */
    private String ticket;
    /**
     * 用户ip
     */
    private String userIp;
    /**
     * 前端回调函数返回的随机字符串
     */
    private String randstr;
    /**
     * 业务类型
     */
    private CaptchaTypeEnum captchaType;
}
