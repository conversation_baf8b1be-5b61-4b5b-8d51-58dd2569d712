package com.xk.tp.domain.commons.response;

import com.xk.tp.enums.pay.RefundStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/27 14:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundAmountData {

    /**
     * 商户平台退款单号
     */
    private String refundNo;

    private String refundOrderId;

    private Long refundAmount;

    @Builder.Default
    private Boolean isResult = true;

    /**
     * 1退款中 2 退款成功 3退款失败
     */
    @Builder.Default
    private Integer refundStatus = RefundStatusEnum.SUCCESS.getStatus();
}
