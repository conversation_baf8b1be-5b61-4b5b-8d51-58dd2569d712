package com.xk.tp.domain.service.pay;

import com.xk.tp.domain.commons.response.ApiPayResultData;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.CallBackResultData;
import com.xk.tp.domain.commons.response.RefundAmountData;
import com.xk.tp.domain.model.pay.PayRoot;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
public interface ApiPayService {

    /**
     * 获取类型
     *
     * @return
     */
    Integer getPayType();

    ApiResult<ApiPayResultData> preCreate(PayRoot apiPayRoot);

    ApiResult<RefundAmountData> refundOrder(PayRoot apiPayRoot);

    ApiResult<Void> closeOrder(PayRoot apiPayRoot);

    ApiResult<CallBackResultData> callBack(PayRoot apiPayRoot);

    ApiResult<RefundAmountData> refundCallBack(PayRoot apiPayRoot);

    ApiResult revokeOrder(PayRoot apiPayRoot);

    ApiResult<Boolean> findPayResult(PayRoot apiPayRoot);
}
