package com.xk.tp.domain.service.im.impl;

import com.xk.tp.domain.model.im.ImRoot;
import com.xk.tp.domain.repository.im.ImRootRepository;
import com.xk.tp.domain.service.im.ImRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImRootServiceImpl implements ImRootService {

    private final ImRootRepository imRootRepository;

    @Override
    public Mono<Void> createGroup(ImRoot imRoot) {
        return imRootRepository.createGroup(imRoot);
    }

    @Override
    public Mono<Void> deleteGroup(ImRoot imRoot) {
        return imRootRepository.deleteGroup(imRoot);
    }

    @Override
    public Mono<Long> selectOnlineNumber(ImRoot imRoot) {
        return imRootRepository.selectOnlineNumber(imRoot);
    }

    @Override
    public Mono<Long> sendGroupMsgAll(ImRoot imRoot) {
        return imRootRepository.sendGroupMsgAll(imRoot);
    }

    @Override
    public Mono<Long> sendGroupMsg(ImRoot imRoot) {
        return imRootRepository.sendGroupMsg(imRoot);
    }

    @Override
    public Mono<Void> sendGroupSystemMsg(ImRoot imRoot) {
        return imRootRepository.sendGroupSystemMsg(imRoot);
    }

    @Override
    public Mono<String> sign(ImRoot imRoot) {
        return imRootRepository.sign(imRoot);
    }
}
