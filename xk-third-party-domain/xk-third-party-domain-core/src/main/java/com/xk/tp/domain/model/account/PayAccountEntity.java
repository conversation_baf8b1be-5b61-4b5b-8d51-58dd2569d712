package com.xk.tp.domain.model.account;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.account.ids.AccountIdentifier;
import com.xk.tp.domain.model.account.ids.PayAccountIdentifier;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayAccountEntity implements Entity<PayAccountIdentifier> {

    private Long accountId;

    /**
     * appId
     */
    private String appId;
    /**
     * 私钥
     */
    private String appKey;
    /**
     * 公钥
     */
    private String publicKey;
    /**
     * 商户号
     */
    private String cusId;

    /**
     * apiV3Key
     */
    private String apiV3Key;

    /**
     * 商户序列号
     */
    private String merchantSerialNumber;

    /**
     * 支付回调地址
     */
    private String notifyUrl;

    /**
     * 页面返回地址
     */
    private String returnUrl;


    /**
     * 支付过期时间
     */
    private Integer expireTime;
    /**
     * 退款回调地址
     */
    private String refundNotifyUrl;

    /**
     * 区域ID
     */
    private String regionId;

    /**
     * 场景id
     */
    private String sceneId;

    /**
     * 系统id
     */
    private String sysId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 编码
     */
    private String charset;


    @Override
    public @NonNull PayAccountIdentifier getIdentifier() {
        return PayAccountIdentifier.builder().accountId(accountId).build();
    }

    @Override
    public Validatable<PayAccountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
