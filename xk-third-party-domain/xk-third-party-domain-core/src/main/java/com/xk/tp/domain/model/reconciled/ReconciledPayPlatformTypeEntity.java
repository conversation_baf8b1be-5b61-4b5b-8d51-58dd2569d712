package com.xk.tp.domain.model.reconciled;

import java.util.Map;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.pay.ids.PayIdentifier;
import com.xk.tp.domain.model.reconciled.ids.ReconciledIdentifier;
import com.xk.tp.enums.pay.PayChannelTypeEnum;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReconciledPayPlatformTypeEntity implements Entity<ReconciledIdentifier> {

    private Long accessAccountId;

    private PayPlatformTypeEnum platformType;

    private PayChannelTypeEnum channelType;

    /**
     * 计算移动位数
     */
    private Integer bit;

    //保留位数
    private Integer reservedBit;
    /**
     * 账号配置
     */
    private Map<String, String> config;


    @Override
    public @NonNull ReconciledIdentifier getIdentifier() {
        return ReconciledIdentifier.builder().accessAccountId(accessAccountId).build();
    }

    @Override
    public Validatable<ReconciledIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
