package com.xk.tp.domain.model.im.ids;

import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.enums.im.ImPlatformTypeEnum;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImIdentifier implements Identifier<ImIdentifier> {

    private ImPlatformTypeEnum imPlatformTypeEnum;

    private String imId;

    @NonNull
    @Override
    public ImIdentifier getIdentifier() {
        return ImIdentifier.builder().imId(imId).imPlatformTypeEnum(imPlatformTypeEnum)
                .build();
    }

}
