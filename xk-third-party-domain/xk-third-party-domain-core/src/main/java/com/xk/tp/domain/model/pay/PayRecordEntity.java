package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * 支付下单记录
 * tp_pay_record
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayRecordEntity implements Entity<LongIdentifier> {
    /**
     * 支付id
     */
    private Long payRecordId;

    /**
     * 订单编号
     */
    private String orderNo;

    private Integer channelType;

    private Integer platformType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付平台
     */
    private String device;

    /**
     * 账号id
     */
    private Long accessAccountId;

    /**
     * 订单金额
     */
    private Long amount;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(payRecordId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}