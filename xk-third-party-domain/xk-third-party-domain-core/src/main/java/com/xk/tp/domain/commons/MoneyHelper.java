package com.xk.tp.domain.commons;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class MoneyHelper {

    private static final Integer UNIT = 1000000;
    private static final Long UNIT_LONG = Long.valueOf(UNIT);
    private static final BigDecimal UNIT_DECIMAL = BigDecimal.valueOf(UNIT);

    public static BigDecimal divideMoney(Long source) {
        if (source == null) {
            return BigDecimal.ZERO;
        }
        return divide(source, UNIT_LONG, 6, RoundingMode.HALF_UP);
    }

    public static Long multiply(Long source) {
        if (source == null) {
            return 0L;
        }
        return multiply(source, UNIT_LONG).longValue();
    }

    public static BigDecimal divide(Long source, Long divisor, Integer scale) {
        if (source == null || divisor == null || scale == null) {
            return BigDecimal.ZERO;
        }
        return divide(source, divisor, scale, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide(Long source, Long divisor, Integer scale,
            RoundingMode roundingMode) {
        if (source == null || divisor == null || scale == null || roundingMode == null) {
            return BigDecimal.ZERO;
        }
        return divide(BigDecimal.valueOf(source), BigDecimal.valueOf(divisor), scale, roundingMode);
    }

    public static BigDecimal divide(BigDecimal source, BigDecimal divisor) {
        if (source == null || divisor == null) {
            return BigDecimal.ZERO;
        }
        return divide(source, divisor, 6);
    }

    public static BigDecimal divide(BigDecimal source, BigDecimal divisor, Integer scale) {
        if (source == null || divisor == null || scale == null) {
            return BigDecimal.ZERO;
        }
        return divide(source, divisor, scale, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide(BigDecimal source, BigDecimal divisor, Integer scale,
            RoundingMode roundingMode) {
        if (source == null || divisor == null || scale == null || roundingMode == null) {
            return BigDecimal.ZERO;
        }
        return source.divide(divisor, scale, roundingMode);
    }

    public static BigDecimal add(BigDecimal source, Long value) {
        if (source == null || value == null) {
            return BigDecimal.ZERO;
        }

        return add(source, BigDecimal.valueOf(value));
    }

    public static BigDecimal add(BigDecimal source, BigDecimal value) {
        if (source == null || value == null) {
            return BigDecimal.ZERO;
        }

        return source.add(value);
    }

    public static BigDecimal multiply(Long source, Long value) {
        if (source == null || value == null) {
            return BigDecimal.ZERO;
        }

        return multiply(BigDecimal.valueOf(source), value);
    }

    public static BigDecimal multiply(BigDecimal source, Long value) {
        if (source == null || value == null) {
            return BigDecimal.ZERO;
        }

        return multiply(source, BigDecimal.valueOf(value));
    }

    public static BigDecimal multiply(BigDecimal source, BigDecimal value) {
        if (source == null || value == null) {
            return BigDecimal.ZERO;
        }

        return source.multiply(value);
    }

    public static BigDecimal setMoneyScale(BigDecimal source) {
        if (source == null) {
            return BigDecimal.ZERO;
        }

        return source.divide(UNIT_DECIMAL, 2, RoundingMode.HALF_UP).multiply(UNIT_DECIMAL);
    }
}
