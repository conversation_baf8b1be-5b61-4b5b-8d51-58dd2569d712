package com.xk.tp.domain.commons;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

@Getter
@AllArgsConstructor
public enum TpAuthDictEnum implements DictObjectEnum {

    FACE_VERIFY_EXPIRE_TIME("300"),
    ;

    private static final Map<String, TpAuthDictEnum> MAP;

    static {
        MAP = Arrays.stream(TpAuthDictEnum.values())
                .collect(Collectors.toMap(TpAuthDictEnum::name, enumValue -> enumValue));
    }

    private final String defaultValue;

    public static TpAuthDictEnum getEnum(String name) {
        return MAP.get(name);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return name();
    }
}
