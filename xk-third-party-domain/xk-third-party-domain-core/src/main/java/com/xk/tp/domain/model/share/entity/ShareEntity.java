package com.xk.tp.domain.model.share.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.domain.model.common.CreateValObj;
import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;
import com.xk.tp.domain.model.share.ids.ShareIdentifier;
import com.xk.tp.domain.model.share.obj.*;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.*;

/**
 * 分享实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 8:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShareEntity implements Entity<ShareIdentifier> {

    /**
     * 分享类型枚举
     */
    private ShareTypeEnum shareType;

    /**
     * 通道账号标识符
     */
    private AccessAccountIdentifier accessAccountIdentifier;

    /**
     * 分享类型扩展数据
     */
    private ShareTypeExtValObj shareTypeExtValObj;

    /**
     * 分享视图数据
     */
    private ShareViewObj shareViewObj;

    /**
     * 文本分享数据
     */
    private TextValObj textValObj;

    /**
     * 图片分享数据
     */
    private ImageValObj imageValObj;

    /**
     * 视频分享数据
     */
    private VideoValObj videoValObj;

    /**
     * 网页分享数据
     */
    private WebpageValObj webpageValObj;

    /**
     * 小程序分享数据
     */
    private MiniProgramValObj miniProgramValObj;

    /**
     * 音乐视频分享数据
     */
    private MusicVideoValObj musicVideoValObj;

    /**
     * 创建信息
     */
    private CreateValObj createValObj;


    @Override
    public @NonNull ShareIdentifier getIdentifier() {
        return ShareIdentifier.builder().shareTypeEnum(shareType).build();
    }

    @Override
    public Validatable<ShareIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
