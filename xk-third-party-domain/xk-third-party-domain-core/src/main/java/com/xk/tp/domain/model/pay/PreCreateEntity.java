package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreCreateEntity implements Entity<String> {

    private String orderNo;
    private Long payRecordId;

    private String phone;

    private String userCreateTime;

    private String goodsName;

    private String description;


    private Long amount;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 订单超时时间
     */
    private Integer expireTime;
    /**
     * 用户标识
     */
    private String userId;
//    private String userMobile;
//    private String userRegisterTime;

    private String device;

    private String clientIp;
    /**
     * iOS, Android, Wap
     */
    private String phoneModel;

    /**
     * 加密参数
     */
    private String encryData;

    /**
     * 表单提交
     */
    private String formData;
    /**
     * url
     */
    private String url;

    @Override
    public @NonNull String getIdentifier() {
        return this.orderNo;
    }

    @Override
    public Validatable<String> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
