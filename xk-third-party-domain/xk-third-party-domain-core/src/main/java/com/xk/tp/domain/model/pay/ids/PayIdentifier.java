package com.xk.tp.domain.model.pay.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayIdentifier implements Identifier<PayIdentifier> {

//    private PayPlatformTypeEnum platformType;
//
//    private PayChannelTypeEnum channelType;


    private Long accessAccountId;

    @NonNull
    @Override
    public PayIdentifier getIdentifier() {
        return PayIdentifier.builder().accessAccountId(accessAccountId).build();
    }

}
