package com.xk.tp.domain.model.account;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.account.ids.AccountIdentifier;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import com.xk.tp.enums.account.RouteAccountEnum;
import lombok.*;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountEntity implements Entity<AccountIdentifier> {

    private Long accountId;

    private Long accessAccountId;
    private String accountName;
    private AccessDomainStatusEnum status;
    private Integer sort;
    private Integer priority;
    private Long quota;
    private Long accessId;
    private Long createId;
    private Date createTime;
    private Date updateTime;
    private Long updateId;


    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 渠道类型
     */
    private AccessChannelTypeEnum channelType;

    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;

    /**
     * 业务分组
     */
    private BusinessGroupEnum businessGroup;


    private String device;
    private List<RouteAccountEnum> routeAccountEnumList;
    private Long amount;

    @Override
    public @NonNull AccountIdentifier getIdentifier() {
        return AccountIdentifier.builder().accountId(accountId).build();
    }

    @Override
    public Validatable<AccountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
