package com.xk.tp.domain.commons.response;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * date 2024/07/16
 */
@AllArgsConstructor
public enum ResponseCode implements IResponseCode {

    SUCCESS(200, "成功"),
    ERROR(500, "失败"),

    ;

    private final Integer code;
    private final String message;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
