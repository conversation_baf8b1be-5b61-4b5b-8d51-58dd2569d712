package com.xk.tp.domain.model.recording;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.recording.ids.RecordingIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class RecordingRoot extends DomainRoot<RecordingIdentifier> {

    private RecordingEntity recordingEntity;

    @Builder
    public RecordingRoot(RecordingIdentifier identifier, RecordingEntity recordingEntity) {
        super(identifier);
        this.recordingEntity = recordingEntity;
    }

    @Override
    public Validatable<RecordingIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
