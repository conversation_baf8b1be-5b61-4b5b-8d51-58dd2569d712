package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessGameIdentifier implements Identifier<AccessGameIdentifier> {

    private Long accessId;

    private String tpGameId;

    @Override
    public @NonNull AccessGameIdentifier getIdentifier() {
        return AccessGameIdentifier.builder().accessId(accessId).tpGameId(tpGameId).build();
    }
}
