package com.xk.tp.domain.model.pay;

import java.util.Date;
import java.util.Map;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransferValObj {

    /**
     * 收款类型
     */
    private Integer payChannelType;

    /**
     * 银行卡号 | 支付宝账号
     */
    private String accountNo;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 收款人姓名
     */
    private String name;
}
