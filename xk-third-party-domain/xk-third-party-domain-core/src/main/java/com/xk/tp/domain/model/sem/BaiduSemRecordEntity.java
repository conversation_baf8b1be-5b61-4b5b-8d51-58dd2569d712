package com.xk.tp.domain.model.sem;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * 支付下单记录
 * tp_pay_record
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaiduSemRecordEntity implements Entity<LongIdentifier> {

    private Long id;

    /**
     * 落地页url地址
     */
    private String logidurl;

    /**
     * 转化类型
     */
    private BaiSemNewTypeEnum newType;

    /**
     * 响应
     */
    private String resData;

    /**
     * token
     */
    private String token;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 业务类型
     */
    private SemBusiTypeEnum busiType;

    /**
     * 业务id
     */
    private String busiId;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(id).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}