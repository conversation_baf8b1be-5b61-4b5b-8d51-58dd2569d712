package com.xk.tp.domain.model.access;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Random;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.support.DomainStaticBeanFactory;
import com.xk.tp.domain.commons.XkThirdPartyDomainErrorEnum;
import com.xk.tp.domain.support.AccessSequenceEnum;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class AccessRoot extends DomainRoot<AccessIdentifier> {

    private final List<AccessGameEntity> accessGameEntities;
    private final List<AccessInterfaceAuthInfoEntity> accessInterfaceAuthInfoEntities;
    private final List<AccessTagEntity> accessTagEntities;
    private final List<AccessTagRelationEntity> accessTagRelationEntities;
    private AccessEntity accessEntity;
    private List<AccessAccountEntity> accessAccountEntities;


    @Builder
    public AccessRoot(AccessIdentifier identifier, AccessEntity accessEntity,
            List<AccessAccountEntity> accessAccountEntities,
            List<AccessGameEntity> accessGameEntities,
            List<AccessInterfaceAuthInfoEntity> accessInterfaceAuthInfoEntities,
            List<AccessTagEntity> accessTagEntities,
            List<AccessTagRelationEntity> accessTagRelationEntities) {
        super(identifier);
        this.accessEntity = accessEntity;
        this.accessAccountEntities = accessAccountEntities;
        this.accessGameEntities = accessGameEntities;
        this.accessInterfaceAuthInfoEntities = accessInterfaceAuthInfoEntities;
        this.accessTagEntities = accessTagEntities;
        this.accessTagRelationEntities = accessTagRelationEntities;
    }

    public static Long generateIdentifier(AccessSequenceEnum sequenceEnum) {
        IdentifierRoot identifierRoot = IdentifierRoot.builder().identifier(sequenceEnum)
                .type(IdentifierGenerateEnum.CACHE).build();
        return (Long) DomainStaticBeanFactory.getIdentifierGenerateService()
                .generateIdentifier(identifierRoot);
    }

    // 字母数字随机6位+时间戳+号商id 进行md5得到32位
    public static String generateCorpSecret(Long corpId) {
        return generateMd5WithTimestampAndId(corpId);
    }

    public static String generateMd5WithTimestampAndId(Long merchantId) {
        String randomStr = generateRandomAlphaNumeric(6);
        long timestamp = System.currentTimeMillis();
        String toHash = randomStr + timestamp + merchantId;
        return md5Hash(toHash);
    }

    public static String generateRandomAlphaNumeric(int length) {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(characters.charAt(random.nextInt(characters.length())));
        }
        return sb.toString();
    }

    public static String md5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder(32);
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1)
                    hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            return XkThirdPartyDomainErrorEnum.CORP_SECRET_CREATE_ERROR.getDefaultMessage();
        }
    }

    @Override
    public Validatable<AccessIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
