package com.xk.tp.domain.model.push.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.push.ids.DeviceIdentifier;
import lombok.*;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceEntity implements Entity<DeviceIdentifier> {


    /**
     * 设备标识符id
     */
    private String deviceId;


    @Override
    public @NonNull DeviceIdentifier getIdentifier() {
        return DeviceIdentifier.builder().deviceId(deviceId).build();
    }

    @Override
    public Validatable<DeviceIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
