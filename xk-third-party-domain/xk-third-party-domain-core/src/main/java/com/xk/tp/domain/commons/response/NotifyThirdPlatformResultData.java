package com.xk.tp.domain.commons.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotifyThirdPlatformResultData {

    /**
     * 认证唯一标识
     */
    private String authFlowId;

    /**
     * 认证结果
     */
    private String passed;

    /**
     * 原因描述
     */
    private String remark;

    private Boolean isSuccess;
}
