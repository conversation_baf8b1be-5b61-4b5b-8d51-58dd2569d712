package com.xk.tp.domain.model.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundCallValObj {

    private String paramsStr;

    private String bodyStr;

    private Map<String,String> params;

    private Map<String,String> headers;
}
