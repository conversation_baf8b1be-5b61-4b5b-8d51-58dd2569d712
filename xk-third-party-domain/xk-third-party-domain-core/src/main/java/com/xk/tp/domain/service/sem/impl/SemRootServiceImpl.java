package com.xk.tp.domain.service.sem.impl;

import com.alibaba.fastjson.JSON;
import com.xk.tp.domain.model.sem.BaiduSemConfigEntity;
import com.xk.tp.domain.model.sem.SemRoot;
import com.xk.tp.domain.repository.sem.SemRootRepository;
import com.xk.tp.domain.service.sem.SemRootService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Date;


@Slf4j
@Service
@RequiredArgsConstructor
public class SemRootServiceImpl implements SemRootService {

    private final SemRootRepository semRootRepository;

    @Override
    public Mono<Void> uploadConvertData(SemRoot semRoot) {
        return semRootRepository.uploadConvertData(semRoot).flatMap(res -> {

            BaiduSemConfigEntity baiduSemConfigEntity = semRoot.getBaiduSemConfigEntity();
            semRoot.getBaiduSemRecordEntity().setToken(JSON.toJSONString(baiduSemConfigEntity.getConfigMap()));
            semRoot.getBaiduSemRecordEntity().setLogidurl(baiduSemConfigEntity.getLogidUrl());
            semRoot.getBaiduSemRecordEntity().setCreateTime(new Date());
            semRoot.getBaiduSemRecordEntity().setResData(res.getData().getResData());
            return semRootRepository.saveBaiduSemRecord(semRoot);
        });
    }
}
