package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessTagRelationIdentifier implements Identifier<AccessTagRelationIdentifier> {

    private Long tagId;

    private Long accessId;

    private String tpTagId;

    @Override
    public @NonNull AccessTagRelationIdentifier getIdentifier() {
        return AccessTagRelationIdentifier.builder().accessId(accessId).tpTagId(tpTagId).tagId(tagId).build();
    }
}
