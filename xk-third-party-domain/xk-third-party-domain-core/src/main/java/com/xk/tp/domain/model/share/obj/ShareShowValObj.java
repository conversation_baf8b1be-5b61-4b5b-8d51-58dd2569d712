package com.xk.tp.domain.model.share.obj;

import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分享展示值valObj
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 15:20
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareShowValObj {

    /**
     * 分享业务id
     * 业务id，如商品id等
     */
    private Long shareBusinessId;

    /**
     * 分享业务类型
     * 1:商品
     */
    private ShareBusinessTypeEnum shareBusinessType;

    /**
     * 业务补充字段
     * shareBusinessType 为商品时，业务补充字段为商品类型 1:商城商品 2:物料商品 3:收藏卡 4:商家商品
     */
    private String businessExtField;

    /**
     * 渠道类型
     * 1:微信
     */
    private Integer channelType;

    /**
     * 分享类型
     * 1:文本 2:图片 3:网页 4:视频 5:音乐视频 6:小程序
     */
    private ShareTypeEnum shareType;

    /**
     * 分享视图数据
     */
    private ShareViewObj shareViewObj;

    /**
     * 文本分享数据
     */
    private TextValObj textValObj;

    /**
     * 图片分享数据
     */
    private ImageValObj imageValObj;

    /**
     * 视频分享数据
     */
    private VideoValObj videoValObj;

    /**
     * 网页分享数据
     */
    private WebpageValObj webpageValObj;

    /**
     * 小程序分享数据
     */
    private MiniProgramValObj miniProgramValObj;

    /**
     * 音乐视频分享数据
     */
    private MusicVideoValObj musicVideoValObj;
}
