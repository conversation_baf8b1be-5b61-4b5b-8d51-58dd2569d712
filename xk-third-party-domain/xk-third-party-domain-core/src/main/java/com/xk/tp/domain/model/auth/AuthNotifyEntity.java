package com.xk.tp.domain.model.auth;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.auth.AuthEventStatusEnum;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthNotifyEntity implements Entity<LongIdentifier> {

    private Long notifyId;

    private String authFlowId;

    private AuthEventStatusEnum authStatus;

    private Integer expireTime;

    private String remark;

    private Date createTime;

    private Long accessAccountId;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(notifyId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
