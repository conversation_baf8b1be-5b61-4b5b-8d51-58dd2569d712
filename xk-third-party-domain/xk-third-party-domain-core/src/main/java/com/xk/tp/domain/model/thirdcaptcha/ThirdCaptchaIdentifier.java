package com.xk.tp.domain.model.thirdcaptcha;

import com.myco.mydata.domain.model.Identifier;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Builder
@Getter
public class ThirdCaptchaIdentifier implements Identifier<ThirdCaptchaIdentifier> {

    private Long accessAccountId;

    @NonNull
    @Override
    public ThirdCaptchaIdentifier getIdentifier() {
        return ThirdCaptchaIdentifier.builder().accessAccountId(accessAccountId).build();
    }
}
