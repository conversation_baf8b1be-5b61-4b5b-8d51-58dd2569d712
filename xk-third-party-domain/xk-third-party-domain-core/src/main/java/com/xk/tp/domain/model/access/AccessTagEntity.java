package com.xk.tp.domain.model.access;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.access.ids.AccessTagIdentifier;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessTagEntity implements Entity<AccessTagIdentifier> {

    private Long accessId;

    private String tpTagId;

    private String tpTagName;

    private String tpGameId;

    @Override
    public @NonNull AccessTagIdentifier getIdentifier() {
        return AccessTagIdentifier.builder().accessId(accessId).tpTagId(tpTagId).build();
    }

    @Override
    public Validatable<AccessTagIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
