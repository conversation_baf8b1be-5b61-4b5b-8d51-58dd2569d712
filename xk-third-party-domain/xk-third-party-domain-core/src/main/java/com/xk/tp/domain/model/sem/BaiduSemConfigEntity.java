package com.xk.tp.domain.model.sem;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Map;

/**
 * 支付下单记录
 * tp_pay_record
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaiduSemConfigEntity implements Entity<LongIdentifier> {

    private Long accessAccountId;

    private Map<String, String> configMap;

    private String logidUrl;

    private Integer newType;

    private Integer convertValue;

    private Long convertTime;
    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(accessAccountId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}