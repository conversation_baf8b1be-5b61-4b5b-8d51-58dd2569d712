package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessAccountExtIdentifier implements Identifier<AccessAccountExtIdentifier> {

    private Long accessAccountId;
    private String fieldKey;

    @Override
    public @NonNull AccessAccountExtIdentifier getIdentifier() {
        return AccessAccountExtIdentifier.builder().accessAccountId(accessAccountId).fieldKey(fieldKey).build();
    }
}
