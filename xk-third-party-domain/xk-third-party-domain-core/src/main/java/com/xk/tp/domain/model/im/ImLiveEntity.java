package com.xk.tp.domain.model.im;

import com.alibaba.fastjson2.annotation.JSONField;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.im.ids.ImIdentifier;
import com.xk.tp.enums.im.ImPlatformTypeEnum;
import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImLiveEntity implements Entity<ImIdentifier> {

    private ImPlatformTypeEnum imPlatformTypeEnum;

    private String imId;

    private String name;

    private String ownerAccount;

    private String userId;

    private String text;

    /**
     * 消息发送者
     */
    private String fromAccount;

    @Override
    public @NonNull ImIdentifier getIdentifier() {
        return ImIdentifier.builder().imId(imId).imPlatformTypeEnum(imPlatformTypeEnum)
                .build();
    }

    @Override
    public Validatable<ImIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
