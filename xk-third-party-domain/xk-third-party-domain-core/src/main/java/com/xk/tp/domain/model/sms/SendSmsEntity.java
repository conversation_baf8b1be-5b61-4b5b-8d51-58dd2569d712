package com.xk.tp.domain.model.sms;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;

import com.alibaba.fastjson2.JSONObject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendSmsEntity {

    /**
     * 发送类型
     */
    private Integer sendType;
    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 签名
     */
    private String sign;


    /**
     * 【测试】验证码123
     */
    private String content;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 三方模版id
     */
    private String tpTemplateId;

    /**
     * 模板参数
     */
    private List<TemplateParamsData> templateParams;

    /**
     * 扩展号
     */
    private String ext;
    /**
     * 通道id
     */
    private Long accessId;

    public String paramsToJSONString() {
        if (CollectionUtils.isEmpty(templateParams)) {
            return "";
        }

        JSONObject jsonObject = new JSONObject();
        templateParams.forEach(t -> jsonObject.put(t.getName(), t.getValue()));
        return jsonObject.toJSONString();
    }

    public Map<String,String> paramsToMap() {
        Map<String,String> result = new LinkedHashMap<>();
        if (CollectionUtils.isEmpty(templateParams)) {
            return result;
        }

        templateParams.forEach(t -> result.put(t.getName(), t.getValue()));
        return result;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TemplateParamsData {
        private String name;

        private String value;
    }

}
