package com.xk.tp.domain.model.access;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessInterfaceAuthStatusEnum;
import com.xk.tp.enums.access.AccessInterfaceAuthTypeEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessInterfaceAuthInfoObject {

    private Long id;

    private Long accessId;

    private Long userId;

    private AccessInterfaceAuthTypeEnum type;

    private String interfaceKey;

    private String interfaceSecret;

    private Long createId;

    private Date createTime;

    private Date updateTime;

    private Long updateId;

    private BusinessTypeEnum businessType;

    private PlatformTypeEnum platformType;

    private Long corpId;

    private AccessInterfaceAuthStatusEnum authStatus;

    /**
     * 渠道类型
     */
    private AccessChannelTypeEnum channelType;


    /**
     * 业务分组
     */
    private BusinessGroupEnum businessGroup;
}
