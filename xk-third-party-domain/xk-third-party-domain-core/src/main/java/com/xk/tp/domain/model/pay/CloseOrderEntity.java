package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CloseOrderEntity implements Entity<String> {

    private String merchantId;//查询商户号
    private String appId;
    private String orderNo;
    private Long payRecordId;
    private Integer channelType;
    private Integer platformType;

    private Date orderTime;

    @Override
    public @NonNull String getIdentifier() {
        return this.orderNo;
    }

    @Override
    public Validatable<String> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
