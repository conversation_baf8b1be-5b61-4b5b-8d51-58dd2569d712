package com.xk.tp.domain.model.push.obj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送api信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/11 16:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiPushObj {

    /**
     * 调用接口
     */
    private String apiUrl;

    /**
     * 请求参数
     */
    private String params;

    /**
     * 响应参数
     */
    private String response;

    /**
     * 是否成功
     */
    private Boolean success;
}
