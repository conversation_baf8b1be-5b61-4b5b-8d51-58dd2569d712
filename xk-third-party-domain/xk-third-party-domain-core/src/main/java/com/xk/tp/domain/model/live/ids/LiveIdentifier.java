package com.xk.tp.domain.model.live.ids;

import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveIdentifier implements Identifier<LiveIdentifier> {

    private LivePlatformTypeEnum livePlatformTypeEnum;

    private String liveId;

    private LiveUserTypeEnum liveUserTypeEnum;

    @NonNull
    @Override
    public LiveIdentifier getIdentifier() {
        return LiveIdentifier.builder().liveId(liveId).livePlatformTypeEnum(livePlatformTypeEnum)
                .build();
    }

}
