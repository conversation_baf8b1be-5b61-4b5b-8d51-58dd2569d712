package com.xk.tp.domain.model.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallBackValObj {

    private String orderNo;

    private String timestamp;

    private String nonce;
    private String signature;
    private String signatureType;
    private String serial;

    private String paramsStr;


    private Map<String, String> requestParam;

    private String device;

    private Map<String, Object> headers;

    private String bodyStr;
}
