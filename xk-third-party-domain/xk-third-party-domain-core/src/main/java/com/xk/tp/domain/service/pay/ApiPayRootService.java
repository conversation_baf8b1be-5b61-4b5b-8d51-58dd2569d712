package com.xk.tp.domain.service.pay;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.commons.response.ApiPayResultData;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.CallBackResultData;
import com.xk.tp.domain.model.pay.PayRoot;
import reactor.core.publisher.Mono;

public interface ApiPayRootService extends IDomainService<PayRoot> {


    Mono<ApiResult<ApiPayResultData>> preCreate(PayRoot apiPayRoot);

    Mono<Boolean> refundOrder(PayRoot apiPayRoot);

    Mono<ApiResult<Void>> closeOrder(PayRoot apiPayRoot);

    Mono<ApiResult<CallBackResultData>> callBack(PayRoot apiPayRoot);

    Mono<Boolean> refundCallBack(PayRoot apiPayRoot);

    Mono<ApiResult> revokeOrder(PayRoot apiPayRoot);

    Mono<Long> generatePayNotifyId();

    Mono<Boolean> payNotify(PayRoot apiPayRoot);

    Mono<Boolean> findPayResult(PayRoot apiPayRoot);
}
