package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RevokeOrderEntity implements Entity<String> {

    private String merchantId;
    private String appId;
    private String refundOrderNo;
    private String orderNo;
    private BigDecimal totalAmount;
    private Integer channelType;
    private Integer platformType;

    @Override
    public @NonNull String getIdentifier() {
        return this.refundOrderNo;
    }

    @Override
    public Validatable<String> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
