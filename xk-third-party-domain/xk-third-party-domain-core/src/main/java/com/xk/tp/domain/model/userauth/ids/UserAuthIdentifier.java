package com.xk.tp.domain.model.userauth.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthIdentifier implements Identifier<UserAuthIdentifier> {


    private Long userAuthId;

    @Override
    public @NonNull UserAuthIdentifier getIdentifier() {
        return UserAuthIdentifier.builder().userAuthId(userAuthId).build();
    }
}
