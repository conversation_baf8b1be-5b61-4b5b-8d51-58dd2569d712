package com.xk.tp.domain.model.share.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.share.ids.ShareBusinessIdentifier;
import com.xk.tp.domain.model.share.obj.ShareBusinessValObj;
import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import lombok.*;

/**
 * 分享业务实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/7 20:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShareBusinessEntity implements Entity<ShareBusinessIdentifier> {

    /**
     * 分享业务id
     */
    private Long shareBusinessId;

    /**
     * 分享业务类型
     */
    private ShareBusinessTypeEnum shareBusinessType;

    /**
     * 业务补充字段
     * shareBusinessType 为商品时，业务补充字段为商品类型 1:商城商品 2:物料商品 3:收藏卡 4:商家商品
     */
    private String businessExtField;

    /**
     * 业务数据
     */
    private ShareBusinessValObj shareBusinessValObj;


    @Override
    public @NonNull ShareBusinessIdentifier getIdentifier() {
        return ShareBusinessIdentifier.builder().shareBusinessId(shareBusinessId).build();
    }

    @Override
    public Validatable<ShareBusinessIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
