package com.xk.tp.domain.model.auth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthRecordEntity implements Entity<LongIdentifier> {

    /**
     * 认证记录id
     */
    private Long authRecordId;

    /**
     * 认证号
     */
    private String authFlowId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 账号id
     */
    private Long accessAccountId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(authRecordId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
