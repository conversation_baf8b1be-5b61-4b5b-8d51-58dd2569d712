package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundOrderEntity implements Entity<StringIdentifier> {

    private String refundOrderId;
    private String orderNo;
    private Long payRecordId;
    private Date orderTime;
    /**
     * 支付单号
     */
    private String payNo;
    private Date refundOrderTime;
    private Long totalAmount;
    private Long refundAmount;

    private String userId;

    private String device;

    @Override
    public @NonNull StringIdentifier getIdentifier() {
        return StringIdentifier.builder().id(refundOrderId).build();
    }

    @Override
    public Validatable<StringIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
