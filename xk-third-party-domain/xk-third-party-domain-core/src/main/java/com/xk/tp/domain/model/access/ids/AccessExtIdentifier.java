package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessExtIdentifier implements Identifier<AccessExtIdentifier> {

    private Long accessId;
    private String fieldKey;

    @Override
    public @NonNull AccessExtIdentifier getIdentifier() {
        return AccessExtIdentifier.builder().accessId(accessId).fieldKey(fieldKey).build();
    }
}
