package com.xk.tp.domain.commons;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * date 2024/07/15
 */
@Getter
@RequiredArgsConstructor
public enum TpSequenceEnum implements SequenceIdentifier {

    MERCHANT_SNAPSHOT("tp_merchant_snapshot", "snapshot_id", "MerchantSnapshotMapper"),
    TP_PAY_RECORD("tp_pay_record", "pay_notify_id", "TpPayRecordMapper"),
    TP_PAY_NOTIFY("tp_pay_notify", "notify_id", "TpPayNotifyMapper"),
    TP_AUTH_NOTIFY("tp_auth_notify", "notify_id", "TpAuthNotifyMapper"),
    TP_AUTH_RECORD("tp_auth_record", "auth_record_id", "TpAuthRecordMapper"),

    ;

    private final String table;
    private final String pk;
    private final String className;


    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    public String getTable() {
        return this.table;
    }

    public String getPk() {
        return this.pk;
    }

    public String getClassName() {
        return this.className;
    }
}
