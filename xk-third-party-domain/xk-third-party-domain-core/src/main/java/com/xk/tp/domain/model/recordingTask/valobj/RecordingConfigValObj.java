package com.xk.tp.domain.model.recordingTask.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingConfigValObj {

    /**
     * 录制模式 1：单流录制，分别录制房间的订阅UserId的音频和视频，将录制文件上传至云存储； 2：合流录制，将房间内订阅UserId的音视频混录成一个音视频文件，将录制文件上传至云存储；
     */
    private Long recordMode;

    /**
     * 房间内持续没有主播的状态超过MaxIdleTime的时长，自动停止录制，单位：秒。默认值为 30 秒，该值需大于等于 5秒，且小于等于 86400秒(24小时)。
     */
    private Long maxIdleTime;

    /**
     * 录制的媒体流类型： 0：录制音频+视频流（默认）; 1：仅录制音频流； 2：仅录制视频流，
     */
    private Long streamType;

    /**
     * 输出文件的格式 0：(默认)输出文件为hls格式 1：输出文件格式为hls+mp4。 2：输出文件格式为hls+aac 。 3：输出文件格式为mp4。 4：输出文件格式为aac。
     */
    private Long outputFormat;

    /**
     * 单流录制模式下，用户的音视频是否合并 合流录制此参数无需设置，默认音视频合并。 0：单流音视频不合并（默认）。 1：单流音视频合并成一个ts。
     */
    private Long avMerge;

    /**
     * 如果是aac或者mp4文件格式，超过长度限制后，系统会自动拆分视频文件。单位：分钟。默认为1440min（24h），取值范围为1-1440。 【单文件限制最大为2G，满足文件大小 >2G
     * 或录制时长度 > 24h任意一个条件，文件都会自动切分】 Hls 格式录制此参数不生效。 示例值：1440
     */
    private Long maxMediaFileDuration;

    /**
     * 指定录制主辅流 0：主流+辅流（默认）； 1:主流； 2:辅流。
     */
    private Long mediaId;


}
