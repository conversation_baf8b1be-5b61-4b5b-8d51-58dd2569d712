package com.xk.tp.domain.model.auth;

import com.alibaba.fastjson2.JSONObject;
import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.auth.ids.ApiAuthIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class AuthRoot extends DomainRoot<ApiAuthIdentifier> {

    private AuthEntity authEntity;

    private UserEntity userEntity;

    private AuthNotifyEntity authNotifyEntity;

    private AuthRecordEntity authRecordEntity;

    /**
     * 环境参数
     */
    private JSONObject metaInfo;

    @Builder
    public AuthRoot(ApiAuthIdentifier identifier, AuthEntity authEntity, UserEntity userEntity,
                    AuthNotifyEntity authNotifyEntity, AuthRecordEntity authRecordEntity, JSONObject metaInfo) {
        super(identifier);
        this.authEntity = authEntity;
        this.userEntity = userEntity;
        this.authNotifyEntity = authNotifyEntity;
        this.authRecordEntity = authRecordEntity;
        this.metaInfo = metaInfo;
    }

    @Override
    public Validatable<ApiAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
