package com.xk.tp.domain.model.access;

import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessDomainStatusEnum;
import com.xk.tp.enums.access.BusinessGroupEnum;

import lombok.*;

/**
 * ApiConfigExt
 *
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessAccountEntity implements Entity<AccessAccountIdentifier> {

    private Long accessAccountId;
    private String accountName;
    private AccessDomainStatusEnum status;
    private Integer sort;
    private Integer priority;
    private Long quota;
    private Long accessId;
    private Long createId;
    private Date createTime;
    private Date updateTime;
    private Long updateId;


    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 渠道类型
     */
    private AccessChannelTypeEnum channelType;

    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;

    /**
     * 业务分组
     */
    private BusinessGroupEnum businessGroup;

    private Map<String, AccessAccountExtValObj> accessAccountExtValObjMap;


    @Override
    public @NonNull AccessAccountIdentifier getIdentifier() {
        return AccessAccountIdentifier.builder().accessAccountId(accessAccountId).build();
    }

    @Override
    public Validatable<AccessAccountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

    public Map<String, String> getConfig() {
        return accessAccountExtValObjMap.values().stream().collect(Collectors
                .toMap(AccessAccountExtValObj::getFieldKey, AccessAccountExtValObj::getFiledValue));
    }
}
