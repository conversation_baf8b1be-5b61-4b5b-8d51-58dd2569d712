package com.xk.tp.domain.commons.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/7/29 20:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallBackResultData {
    private String orderNo;

    private String payNo;
    /**
     * 1成功 2失败
     */
    private Integer orderStatus;
    /**
     * 三方状态
     */
    private String tradeStatus;

    private String remark;

    private Long amount;

    private String device;

    private String encryData;
}