package com.xk.tp.domain.service.im;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.im.ImRoot;
import reactor.core.publisher.Mono;

public interface ImService extends IDomainService<ImRoot> {

    /**
     * 获取平台类型
     */
    Integer getImPlatformType();

    Mono<Void> createGroup(ImRoot imRoot);

    Mono<Void> deleteGroup(ImRoot imRoot);

    Mono<Long> selectOnlineNumber(ImRoot imRoot);

    Mono<Long> sendGroupMsgAll(ImRoot imRoot);

    Mono<Long> sendGroupMsg(ImRoot imRoot);

    Mono<Void> sendGroupSystemMsg(ImRoot imRoot);

    Mono<String> sign(ImRoot imRoot);
}
