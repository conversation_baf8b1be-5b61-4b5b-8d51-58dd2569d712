package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessAccountIdentifier implements Identifier<AccessAccountIdentifier> {

    private Long accessAccountId;

    @Override
    public @NonNull AccessAccountIdentifier getIdentifier() {
        return AccessAccountIdentifier.builder().accessAccountId(accessAccountId).build();
    }
}
