package com.xk.tp.domain.model.share.ids;

import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.*;

/**
 * 分享聚合根标识符
 *
 * @param shareTypeEnum 分享类型枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/7 20:18
 */
@Builder
public record ShareIdentifier(ShareTypeEnum shareTypeEnum) implements Identifier<ShareIdentifier> {

    @Override
    public @NonNull ShareIdentifier getIdentifier() {
        return this;
    }
}
