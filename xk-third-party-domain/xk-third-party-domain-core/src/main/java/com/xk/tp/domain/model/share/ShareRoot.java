package com.xk.tp.domain.model.share;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.share.entity.ShareBusinessEntity;
import com.xk.tp.domain.model.share.entity.ShareEntity;
import com.xk.tp.domain.model.share.ids.ShareIdentifier;
import com.xk.tp.domain.model.share.ids.ShareLogIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分享聚合根
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 8:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class ShareRoot extends DomainRoot<ShareIdentifier> {

    /**
     * 分享日志标识符
     */
    private ShareLogIdentifier shareLogIdentifier;

    /**
     * 分享业务实体
     */
    private ShareBusinessEntity shareBusinessEntity;

    /**
     * 分享数据实体
     */
    private ShareEntity shareEntity;

    @Builder
    public ShareRoot(ShareIdentifier shareIdentifier, ShareLogIdentifier shareLogIdentifier, ShareBusinessEntity shareBusinessEntity, ShareEntity shareEntity) {
        super(shareIdentifier);
        this.shareLogIdentifier = shareLogIdentifier;
        this.shareBusinessEntity = shareBusinessEntity;
        this.shareEntity = shareEntity;
    }


    @Override
    public Validatable<ShareIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
