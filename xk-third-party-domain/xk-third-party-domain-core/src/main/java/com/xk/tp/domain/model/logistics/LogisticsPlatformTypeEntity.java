package com.xk.tp.domain.model.logistics;

import java.util.Map;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;
import com.xk.tp.domain.model.logistics.id.LogisticsIdentifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;

import com.xk.tp.enums.logistics.LogisticsApiEnum;
import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsPlatformTypeEntity implements Entity<LogisticsIdentifier> {

    private String logisticsCorpName;

    private Long accessAccountId;

    private LogisticsApiEnum logisticsApiEnum;

    /**
     * 账号配置
     */
    private Map<String, String> config;


    @Override
    public @NonNull LogisticsIdentifier getIdentifier() {
        return LogisticsIdentifier.builder().logisticsCorpName(logisticsCorpName).build();
    }

    @Override
    public Validatable<LogisticsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
