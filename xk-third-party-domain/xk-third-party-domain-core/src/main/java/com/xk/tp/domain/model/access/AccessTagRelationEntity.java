package com.xk.tp.domain.model.access;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.access.ids.AccessTagRelationIdentifier;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessTagRelationEntity implements Entity<AccessTagRelationIdentifier> {

    private Long tagId;

    private Long accessId;

    private String tpTagId;

    @Override
    public @NonNull AccessTagRelationIdentifier getIdentifier() {
        return AccessTagRelationIdentifier.builder().accessId(accessId).tpTagId(tpTagId).tagId(tagId).build();
    }

    @Override
    public Validatable<AccessTagRelationIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
