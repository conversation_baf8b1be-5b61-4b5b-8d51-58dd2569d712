package com.xk.tp.domain.model.access.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Getter
@Builder
@AllArgsConstructor
public class AccessTagIdentifier implements Identifier<AccessTagIdentifier> {

    private Long accessId;

    private String tpTagId;

    @Override
    public @NonNull AccessTagIdentifier getIdentifier() {
        return AccessTagIdentifier.builder().accessId(accessId).tpTagId(tpTagId).build();
    }
}
