package com.xk.tp.domain.model.recordingTask.ids;

import com.myco.mydata.domain.model.Identifier;

import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingTaskIdentifier implements Identifier<RecordingTaskIdentifier> {

    /**
     * 直播流水号
     */
    private Long liveNo;

    @NonNull
    @Override
    public RecordingTaskIdentifier getIdentifier() {
        return RecordingTaskIdentifier.builder().liveNo(liveNo)
                .build();
    }

}
