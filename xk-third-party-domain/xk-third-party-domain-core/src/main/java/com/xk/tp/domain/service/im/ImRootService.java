package com.xk.tp.domain.service.im;

import com.myco.mydata.domain.service.IDomainService;
import com.xk.tp.domain.model.im.ImRoot;
import com.xk.tp.domain.model.live.LiveRoot;
import reactor.core.publisher.Mono;

public interface ImRootService extends IDomainService<ImRoot> {

    Mono<Void> createGroup(ImRoot imRoot);

    Mono<Void> deleteGroup(ImRoot imRoot);

    Mono<Long> selectOnlineNumber(ImRoot imRoot);

    Mono<Long> sendGroupMsgAll(ImRoot imRoot);

    Mono<Long> sendGroupMsg(ImRoot imRoot);

    Mono<Void> sendGroupSystemMsg(ImRoot imRoot);

    Mono<String> sign(ImRoot imRoot);
}
