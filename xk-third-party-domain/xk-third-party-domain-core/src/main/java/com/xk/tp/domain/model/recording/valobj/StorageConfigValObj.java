package com.xk.tp.domain.model.recording.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StorageConfigValObj {

    /**
     * 腾讯云对象存储COS以及第三方云存储账号信息 0：腾讯云对象存储 COS 1：AWS
     */
    private Integer vendor;

    /**
     * 腾讯云对象存储的[地域信息] 示例值：cn-shanghai-1
     */
    private String region;

    /**
     * 云存储桶名称。 示例值：test-bucket
     */
    private String bucket;

    /**
     * 云存储的access_key账号信息
     */
    private String accessKey;

    /**
     * 云存储的secret_key账号信息
     */
    private String secretKey;

    /**
     * 云存储bucket 的指定位置，由字符串数组组成 示例值：["prefix1", "prefix2"]
     */
    private String fileNamePrefix;
}
