package com.xk.tp.domain.service.pay.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.domain.event.pay.PayFinishEvent;
import com.xk.domain.event.pay.RefundAmountEvent;
import com.xk.tp.domain.commons.TpSequenceEnum;
import com.xk.tp.domain.commons.XkThirdPartyDomainErrorEnum;
import com.xk.tp.domain.commons.response.ApiPayResultData;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.commons.response.CallBackResultData;
import com.xk.tp.domain.commons.response.RefundAmountData;
import com.xk.tp.domain.model.access.AccessAccountExtValObj;
import com.xk.tp.domain.model.access.AccessIdentifier;
import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;
import com.xk.tp.domain.model.pay.*;
import com.xk.tp.domain.repository.pay.PayRootQueryRepository;
import com.xk.tp.domain.repository.pay.PayRootRepository;
import com.xk.tp.domain.service.access.AccessRootService;
import com.xk.tp.domain.service.pay.ApiPayRootService;
import com.xk.tp.domain.support.XkThirdPartyDomainException;
import com.xk.tp.enums.pay.PayChannelTypeEnum;
import com.xk.tp.enums.pay.PayNotifyStatusEnum;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.enums.pay.RefundStatusEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiPayRootServiceImpl implements ApiPayRootService {


    private final PayRootRepository payRootRepository;

    private final PayRootQueryRepository payRootQueryRepository;

    private final AccessRootService accessRootService;

    private final IdentifierGenerateService identifierGenerateService;
    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Mono<ApiResult<ApiPayResultData>> preCreate(PayRoot apiPayRoot) {

        // //加密
        // PreCreateEntity preCreateEntity = apiPayRoot.getPreCreateEntity();
        // //订单号+ 金额 + 盐值
        // String salt = SystemParamTableHolder.getSubstringAfter("pay.salt");
        // String encryData = MD5Util.md5(salt + preCreateEntity.getOrderNo() +
        // preCreateEntity.getAmount());
        //
        // preCreateEntity.setEncryData(encryData);
        PreCreateEntity preCreateEntity = apiPayRoot.getPreCreateEntity();
        if (preCreateEntity.getOrderTime() == null) {
            preCreateEntity.setOrderTime(new Date());
        }
        // 拿到配置
        return payRootRepository.preCreate(apiPayRoot);
    }

    @Override
    public Mono<Boolean> refundOrder(PayRoot apiPayRoot) {
        return payRootRepository.refundOrder(apiPayRoot).flatMap(apiResult -> {
            log.info("支付退款结果：{}",apiResult.isSuccess());
            // 校验是否成功
            if (apiResult.isSuccess()) {
                // 判断是否返回结果
                RefundAmountData data = apiResult.getData();
//                if (!data.getIsResult()) {
//                    return Mono.empty();
//                }
                // 发送事件
                return sendRefundAmountEvent(data).thenReturn(true);
            }
            // 失败事件
            return sendRefundAmountEvent(RefundAmountData.builder()
                    .refundOrderId(apiPayRoot.getRefundOrderEntity().getRefundOrderId())
                    .refundStatus(RefundStatusEnum.FAIL.getStatus()).build()).thenReturn(false);
        });
    }

    private Mono<Void> sendRefundAmountEvent(RefundAmountData data) {
        return Mono.fromCallable(() -> {
            log.info("发送退款事件----------{}",JSON.toJSONString(data));
            EventRoot event = EventRoot.builder()
                    .domainEvent(RefundAmountEvent.builder().identifier(-1L)
                            .refundOrderId(data.getRefundOrderId()).status(data.getRefundStatus())
                            .build())
                    .build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("b支付回调发送事件失败,data:{}", JSON.toJSONString(data));
                }
                return b;
            } catch (ExceptionWrapperThrowable e) {
                log.error("支付回调发送事件失败,data:{},msg:{}", JSON.toJSONString(data), e.getMessage(), e);
                return false;
            }
        }).then();
    }

    @Override
    public Mono<ApiResult<Void>> closeOrder(PayRoot apiPayRoot) {
        return payRootRepository.closeOrder(apiPayRoot);
    }

    @Override
    public Mono<ApiResult<CallBackResultData>> callBack(PayRoot apiPayRoot) {
        return payRootRepository.callBack(apiPayRoot);
    }

    @Override
    public Mono<Boolean> refundCallBack(PayRoot apiPayRoot) {
        return accessRootService
                .findAccessAccountByAccountId(AccessAccountIdentifier.builder()
                        .accessAccountId(apiPayRoot.getIdentifier().getAccessAccountId()).build())
                .flatMap(accessAccountEntity -> {
                    return accessRootService
                            .findById(AccessIdentifier.builder()
                                    .accessId(accessAccountEntity.getAccessId()).build())
                            .flatMap(accessEntity -> {
                                return accessRootService
                                        .findAccessAccountExtByAccessAccountExt(
                                                AccessAccountExtValObj.builder()
                                                        .accessAccountId(accessAccountEntity
                                                                .getAccessAccountId())
                                                        .build())
                                        .flatMap(ext -> {
                                            PayPlatformTypeEntity payPlatformTypeEntity =
                                                    PayPlatformTypeEntity.builder()
                                                            .accessAccountId(accessAccountEntity
                                                                    .getAccessAccountId())
                                                            .channelType(PayChannelTypeEnum
                                                                    .getEnumByValue(accessEntity
                                                                            .getChannelType()
                                                                            .getValue()))
                                                            .platformType(PayPlatformTypeEnum
                                                                    .getEnumByValue(accessEntity
                                                                            .getPlatformType()))
                                                            .config(ext).build();

                                            apiPayRoot.setPayPlatformTypeEntity(
                                                    payPlatformTypeEntity);
                                            // 执行业务
                                            return payRootRepository.refundCallBack(apiPayRoot)
                                                    .flatMap(apiResult -> {
                                                        if (!apiResult.isSuccess()) {
                                                            // 保存记录
                                                            return Mono.just(Boolean.FALSE);
                                                        }
                                                        return sendRefundAmountEvent(
                                                                apiResult.getData())
                                                                .then(Mono.just(Boolean.TRUE));
                                                    });
                                        });
                            });
                }).onErrorMap(d -> {
                    log.error("支付报错:{}", d.getMessage(), d);
                    return d;
                });
    }

    @Override
    public Mono<ApiResult> revokeOrder(PayRoot apiPayRoot) {
        return null;
    }

    @Override
    public Mono<Long> generatePayNotifyId() {
        return Mono.fromCallable(() -> {
            return (Long) identifierGenerateService.generateIdentifier(
                    IdentifierRoot.builder().identifier(TpSequenceEnum.TP_PAY_NOTIFY)
                            .type(IdentifierGenerateEnum.CACHE).build());
        });
    }

    @Override
    public Mono<Boolean> payNotify(PayRoot apiPayRoot) {
        return accessRootService
                .findAccessAccountByAccountId(AccessAccountIdentifier.builder()
                        .accessAccountId(apiPayRoot.getIdentifier().getAccessAccountId()).build())
                .flatMap(accessAccountEntity -> {
                    return accessRootService
                            .findById(AccessIdentifier.builder()
                                    .accessId(accessAccountEntity.getAccessId()).build())
                            .flatMap(accessEntity -> {
                                return accessRootService
                                        .findAccessAccountExtByAccessAccountExt(
                                                AccessAccountExtValObj.builder()
                                                        .accessAccountId(accessAccountEntity
                                                                .getAccessAccountId())
                                                        .build())
                                        .flatMap(ext -> {
                                            PayPlatformTypeEntity payPlatformTypeEntity =
                                                    PayPlatformTypeEntity.builder()
                                                            .accessAccountId(accessAccountEntity
                                                                    .getAccessAccountId())
                                                            .channelType(PayChannelTypeEnum
                                                                    .getEnumByValue(accessEntity
                                                                            .getChannelType()
                                                                            .getValue()))
                                                            .platformType(PayPlatformTypeEnum
                                                                    .getEnumByValue(accessEntity
                                                                            .getPlatformType()))
                                                            .config(ext).build();

                                            apiPayRoot.setPayPlatformTypeEntity(
                                                    payPlatformTypeEntity);
                                            // 执行业务
                                            return payRootRepository.callBack(apiPayRoot)
                                                    .flatMap(apiResult -> {
                                                        if (!apiResult.isSuccess()) {
                                                            // 保存记录
                                                            return Mono.just(Boolean.FALSE);
                                                        }
                                                        CallBackResultData data =
                                                                apiResult.getData();

                                                        // if
                                                        // (accessEntity.getPlatformType().getIsCheck()){
                                                        //
                                                        // String salt =
                                                        // SystemParamTableHolder.getSubstringAfter("pay.salt");
                                                        // //校验加密参数
                                                        // if(!MD5Util.md5(salt + data.getOrderNo()
                                                        // +
                                                        // data.getAmount()).equals(data.getEncryData())){
                                                        // log.error("自定义加密校验失败-------,DTO:{}",
                                                        // JSON.toJSONString(data));
                                                        // return Mono.just(Boolean.FALSE);
                                                        // }
                                                        // }
                                                        // 执行操作
                                                        apiPayRoot.setPayNotifyEntity(
                                                                PayNotifyEntity.builder()
                                                                        .payNo(data.getPayNo())
                                                                        .orderNo(data.getOrderNo())
                                                                        .accessAccountId(
                                                                                accessAccountEntity
                                                                                        .getAccessAccountId())
                                                                        .amount(data.getAmount())
                                                                        .remark(data.getRemark())
                                                                        .channelType(
                                                                                payPlatformTypeEntity
                                                                                        .getChannelType()
                                                                                        .getValue())
                                                                        .platformType(
                                                                                payPlatformTypeEntity
                                                                                        .getPlatformType()
                                                                                        .getValue())
                                                                        .device(data.getDevice())
                                                                        .tradeStatus(data
                                                                                .getTradeStatus())
                                                                        .status(PayNotifyStatusEnum
                                                                                .getEnumByStatus(
                                                                                        data.getOrderStatus()))
                                                                        .build());
                                                        return sendPayFinishEvent(
                                                                apiPayRoot.getPayNotifyEntity());
                                                    });
                                        });
                            });
                }).onErrorMap(d -> {
                    log.error("支付报错:{}", d.getMessage(), d);
                    return d;
                });
    }

    @Override
    public Mono<Boolean> findPayResult(PayRoot payRoot) {
        return payRootRepository.findPayResult(payRoot)
                .flatMap(apiPayRoot->{
                    if (apiPayRoot.isSuccess()){
                        return Mono.just(apiPayRoot.getData());
                    }
                    return Mono.just(false);
                });
    }

    private Mono<Boolean> testPayNotify(PayRoot apiPayRoot) {
        PayPlatformTypeEntity payPlatformTypeEntity = apiPayRoot.getPayPlatformTypeEntity();
        CallBackValObj callBackValObj = apiPayRoot.getCallBackValObj();
        String orderNo = callBackValObj.getOrderNo();
        String payNo = orderNo + "_" + payPlatformTypeEntity.getPlatformType().getValue();
        // 执行操作
        apiPayRoot.setPayNotifyEntity(PayNotifyEntity.builder().payNo(payNo).orderNo(orderNo)
                .accessAccountId(apiPayRoot.getIdentifier().getAccessAccountId())
                .amount(1000000000L).remark("test").channelType(2).platformType(1).device("pc")
                .tradeStatus("success").status(PayNotifyStatusEnum.SUCCESS).build());
        return sendPayFinishEvent(apiPayRoot.getPayNotifyEntity());
    }

    private Mono<Boolean> sendPayFinishEvent(PayNotifyEntity payNotifyEntity) {
        return Mono.fromCallable(() -> {
            log.info("发送支付回调事件----------");

            EventRoot event = EventRoot.builder()
                    .domainEvent(PayFinishEvent.builder().identifier(-1L)
                            .orderNo(payNotifyEntity.getOrderNo()).payNo(payNotifyEntity.getPayNo())
                            .status(payNotifyEntity.getStatus().getStatus())
                            .amount(payNotifyEntity.getAmount())
                            .platformType(payNotifyEntity.getPlatformType())
                            .channelType(payNotifyEntity.getChannelType())
                            .device(payNotifyEntity.getDevice())
                            .accessAccountId(payNotifyEntity.getAccessAccountId())
                            .tradeStatus(payNotifyEntity.getTradeStatus())
                            .payTime(new Date())
                            .remark(payNotifyEntity.getRemark()).build())
                    .build();
            try {
                boolean b = eventRootService.publish(event);
                if (!b) {
                    log.error("b支付回调发送事件失败,orderNo:{}", payNotifyEntity.getOrderNo());
                }
                return b;
            } catch (ExceptionWrapperThrowable e) {
                log.error("支付回调发送事件失败,orderNo:{},msg:{}", payNotifyEntity.getOrderNo(),
                        e.getMessage(), e);
                return false;
            }
        }).flatMap(status -> {
            if (!status) {
                return Mono.error(new XkThirdPartyDomainException(
                        XkThirdPartyDomainErrorEnum.PAY_NOTIFY_SEND_EVENT_ERROR));
            }
            return Mono.just(Boolean.TRUE);
        });
    }
}
