package com.xk.tp.domain.model.account.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayAccountIdentifier implements Identifier<PayAccountIdentifier> {

    private Long accountId;

    @Override
    public @NonNull PayAccountIdentifier getIdentifier() {
        return PayAccountIdentifier.builder().accountId(accountId).build();
    }
}
