package com.xk.tp.domain.model.recording;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.recording.ids.RecordingIdentifier;
import com.xk.tp.domain.model.recording.valobj.MixLayoutConfigValObj;
import com.xk.tp.domain.model.recording.valobj.MixTranscodeConfigValObj;
import com.xk.tp.domain.model.recording.valobj.RecordingConfigValObj;
import com.xk.tp.domain.model.recording.valobj.StorageConfigValObj;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.live.LiveUserTypeEnum;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingEntity implements Entity<RecordingIdentifier> {

    /**
     * 直播平台
     */
    private LivePlatformTypeEnum livePlatformTypeEnum;

    private LiveUserTypeEnum liveUserTypeEnum;

    /**
     * 录播id
     */
    private String recordingTaskId;

    /**
     * 直播id
     */
    private String liveId;

    /**
     * 发起人
     */
    private String creator;

    /**
     * 录播参数控制
     */
    private RecordingConfigValObj recordingConfigValObj;

    /**
     * 录播存储控制
     */
    private StorageConfigValObj storageConfigValObj;

    /**
     * 合流的转码参数
     */
    private MixTranscodeConfigValObj mixTranscodeConfigValObj;

    /**
     * 合流的布局参数-清晰度设置
     */
    private MixLayoutConfigValObj mixLayoutConfigValObj;

    @Override
    public @NonNull RecordingIdentifier getIdentifier() {
        return RecordingIdentifier.builder().liveId(liveId)
                .livePlatformTypeEnum(livePlatformTypeEnum).recordingTaskId(recordingTaskId)
                .build();
    }

    @Override
    public Validatable<RecordingIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
