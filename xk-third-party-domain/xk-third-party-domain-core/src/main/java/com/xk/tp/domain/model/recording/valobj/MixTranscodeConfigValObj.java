package com.xk.tp.domain.model.recording.valobj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MixTranscodeConfigValObj {

    /**
     * 视频的宽度值，单位为像素，默认值360。 PS: 不能超过1920，与height的乘积不能超过 1920*1080
     */
    private Long width;

    /**
     * 视频的高度值，单位为像素，默认值640。 PS：不能超过1920，与width的乘积不能超过1920*1080。
     */
    private Long height;

    /**
     * 视频的帧率，范围[1, 60]， 默认15。
     */
    private Long fps;

    /**
     * 视频的码率,单位是bps，范围[64000, 8192000]， 默认550000bps。
     */
    private Long bitRate;

    /**
     * 视频关键帧时间间隔，单位秒，默认值10秒。
     */
    private Long gop;

    /**
     * 音频采样率枚举值:(注意1 代表48000HZ, 2 代表44100HZ, 3 代表16000HZ) 1：48000Hz（默认）; 2：44100Hz 3：16000Hz。
     */
    // private Long audioSampleRate;

    /**
     * 声道数枚举值: 1：单声道; 2：双声道（默认）。 示例值：2
     */
    // private Long audioChannel;

    /**
     * 音频码率: 取值范围[32000, 128000] ，单位bps，默认64000bps。
     */
    // private Long audioBitRate;

}
