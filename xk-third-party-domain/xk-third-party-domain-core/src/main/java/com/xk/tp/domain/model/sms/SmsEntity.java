package com.xk.tp.domain.model.sms;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.sms.SmsChannelTypeEnum;
import com.xk.tp.enums.sms.SmsPlatformTypeEnum;
import lombok.*;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsEntity implements Entity<SmsIdentifier> {

    private Long accessAccountId;

    private SmsPlatformTypeEnum platformType;

    private SmsChannelTypeEnum channelType;

    private Map<String, String> config;

    @Override
    public @NonNull SmsIdentifier getIdentifier() {
        return SmsIdentifier.builder().accessAccountId(accessAccountId).build();
    }

    @Override
    public Validatable<SmsIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
