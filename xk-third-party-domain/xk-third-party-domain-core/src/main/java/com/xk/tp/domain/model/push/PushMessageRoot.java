package com.xk.tp.domain.model.push;


import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.push.entity.DeviceEntity;
import com.xk.tp.domain.model.push.entity.MessageContextEntity;
import com.xk.tp.domain.model.push.entity.PushMessageEntity;
import com.xk.tp.domain.model.push.ids.DeviceIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageRoot extends DomainRoot<DeviceIdentifier> {


    private DeviceEntity deviceEntity;
    private PushMessageEntity pushMessageEntity;
    private MessageContextEntity messageContextEntity;


    @Override
    public Validatable<DeviceIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
