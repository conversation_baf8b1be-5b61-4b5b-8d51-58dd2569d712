package com.xk.tp.domain.model.pay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayResultValObj {

    private String orderNo;
    private Long payRecordId;
    private String payNo;

    private Date orderTime;

    private Integer channelType;
    private Integer platformType;

    private Long accessAccountId;

    private Map<String, String> config;
}
