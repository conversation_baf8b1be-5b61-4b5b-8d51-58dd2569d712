package com.xk.tp.domain.model.sem;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum SemBusiTypeEnum {
    REGISTER(1,"注册"),
    GOODS_PUBLISH(2,"商品发布"),
    PAY_ORDER(3,"支付订单"),
    ;

    private Integer code;

    private String msg;

    private static final Map<Integer, SemBusiTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SemBusiTypeEnum.values())
                .collect(Collectors.toMap(SemBusiTypeEnum::getCode, enumValue -> enumValue));
    }

    public static SemBusiTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
