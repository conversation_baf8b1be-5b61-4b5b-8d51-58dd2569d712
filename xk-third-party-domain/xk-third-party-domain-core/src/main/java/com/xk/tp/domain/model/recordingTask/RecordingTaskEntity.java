package com.xk.tp.domain.model.recordingTask;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.recordingTask.ids.RecordingTaskIdentifier;

import lombok.*;

import java.util.Date;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordingTaskEntity implements Entity<RecordingTaskIdentifier> {

    /**
     * 直播流水号
     */
    private Long liveNo;

    /**
     * 录播任务id
     */
    private String recordingTaskId;

    /**
     * 记录开始录播时间
     */
    private Date startTime;

    /**
     * 记录结束录播时间
     */
    private Date endTime;

    /**
     * 时长，单位秒
     */
    private Long time;

    @Override
    public @NonNull RecordingTaskIdentifier getIdentifier() {
        return RecordingTaskIdentifier.builder().liveNo(liveNo)
                .build();
    }

    @Override
    public Validatable<RecordingTaskIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
