package com.xk.tp.domain.model.reconciled;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.reconciled.ids.ReconciledIdentifier;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.enums.reconciled.BillTypeEnum;

import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReconciledEntity implements Entity<ReconciledIdentifier> {

    /**
     * 支付平台类型
     */
    private PayPlatformTypeEnum payPlatformTypeEnum;

    /**
     * 对账单类型
     */
    private BillTypeEnum billTypeEnum;

    /**
     * 交易日期
     */
    private Date tradeTime;

    /**
     * 商户ID/商户号
     */
    private String corpId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 请求流水号
     */
    private String reqId;

    @Override
    public @NonNull ReconciledIdentifier getIdentifier() {
        return ReconciledIdentifier.builder().payPlatformTypeEnum(payPlatformTypeEnum).build();
    }

    @Override
    public Validatable<ReconciledIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
