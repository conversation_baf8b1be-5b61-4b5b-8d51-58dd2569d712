package com.xk.tp.domain.model.userauth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.userauth.ids.UserAuthIdentifier;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAuthAccessTokenEntity implements Entity<UserAuthIdentifier> {

    private Long userAuthId;

    private String code;

    private String accessToken;
    private Integer expiresIn;
    private String refreshToken;
    private String openid;
    private String scope;
    private String unionid;

    @Override
    public @NonNull UserAuthIdentifier getIdentifier() {
        return UserAuthIdentifier.builder().userAuthId(userAuthId).build();
    }

    @Override
    public Validatable<UserAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
