package com.xk.tp.domain.commons.response;

import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * date 2024/07/16
 */
@Getter
@ToString
public class ApiResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer code;
    private String message;
    private T data;

    public static <T> ApiResult<T> success() {
        return success(null);
    }

    public static <T> ApiResult<T> success(T o) {
        ApiResult<T> response = new ApiResult<>();
        response.code = ResponseCode.SUCCESS.getCode();
        response.message = ResponseCode.SUCCESS.getMessage();
        response.data = o;
        return response;
    }

    public boolean isSuccess(){
        return ResponseCode.SUCCESS.getCode().equals(code);
    }

    public static <T> ApiResult<T> error() {
        return error(ResponseCode.ERROR);
    }

    public static <T> ApiResult<T> error(IResponseCode code) {
        return ApiResult.error(code.getCode(), code.getMessage());
    }

    public static <T> ApiResult<T> error(IResponseCode code, Object... params) {
        return ApiResult.error(code.getCode(), MessageFormat.format(code.getMessage(), params));
    }

    public static <T> ApiResult<T> error(String message) {
        return ApiResult.error(ResponseCode.ERROR.getCode(), message);
    }

    public static <T> ApiResult<T> error(Integer code, String message) {
        ApiResult<T> response = new ApiResult<>();
        response.code = code;
        response.message = message;
        return response;
    }
}
