package com.xk.tp.domain.model.im;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.im.ids.ImIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class ImRoot extends DomainRoot<ImIdentifier> {

    private final ImLiveEntity imLiveEntity;

    private final ImPlatformTypeEntity imPlatformTypeEntity;

    @Builder
    public ImRoot(ImIdentifier identifier, ImLiveEntity imLiveEntity, ImPlatformTypeEntity imPlatformTypeEntity) {
        super(identifier);
        this.imLiveEntity = imLiveEntity;
        this.imPlatformTypeEntity = imPlatformTypeEntity;
    }

    @Override
    public Validatable<ImIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
