package com.xk.tp.domain.model.live;

import java.util.Map;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.live.ids.LiveIdentifier;
import com.xk.tp.domain.model.pay.ids.PayIdentifier;
import com.xk.tp.enums.live.LivePlatformTypeEnum;
import com.xk.tp.enums.pay.PayChannelTypeEnum;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LivePlatformTypeEntity implements Entity<LiveIdentifier> {

    private Long accessAccountId;

    private LivePlatformTypeEnum livePlatformTypeEnum;

    /**
     * 账号配置
     */
    private Map<String, String> config;


    @Override
    public @NonNull LiveIdentifier getIdentifier() {
        return LiveIdentifier.builder().livePlatformTypeEnum(livePlatformTypeEnum).build();
    }

    @Override
    public Validatable<LiveIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
