package com.xk.tp.domain.model.access;

import com.myco.mydata.commons.constant.BusinessTypeEnum;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.StringIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.access.AccessInterfaceAuthStatusEnum;
import com.xk.tp.enums.access.AccessInterfaceAuthTypeEnum;
import lombok.*;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessInterfaceAuthInfoEntity implements Entity<StringIdentifier> {

    private Long id;

    private Long accessId;

    private Long userId;

    private AccessInterfaceAuthTypeEnum type;

    private String interfaceKey;

    private String interfaceSecret;

    private Long createId;

    private Date createTime;

    private Date updateTime;

    private Long updateId;

    private BusinessTypeEnum businessType;

    private PlatformTypeEnum platformType;

    private Long corpId;

    private AccessInterfaceAuthStatusEnum authStatus;

    @Override
    public @NonNull StringIdentifier getIdentifier() {
        return StringIdentifier.builder().id(interfaceKey).build();
    }

    @Override
    public Validatable<StringIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
