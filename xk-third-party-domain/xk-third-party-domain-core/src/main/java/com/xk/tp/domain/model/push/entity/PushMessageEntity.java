package com.xk.tp.domain.model.push.entity;

import java.util.Date;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.push.ids.DeviceIdentifier;
import com.xk.tp.domain.model.push.obj.PushChannelExtObj;

import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageEntity implements Entity<DeviceIdentifier> {


    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 推送人id
     */
    private Long pushUserId;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 账号id
     */
    private Long accessAccountId;


    /**
     * 推送渠道补充信息
     */
    private PushChannelExtObj pushChannelExtObj;

    /**
     * 设备类型
     */
    private PlatformTypeEnum platformType;


    @Override
    public @NonNull DeviceIdentifier getIdentifier() {
        return DeviceIdentifier.builder().deviceId(deviceId).build();
    }

    @Override
    public Validatable<DeviceIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
