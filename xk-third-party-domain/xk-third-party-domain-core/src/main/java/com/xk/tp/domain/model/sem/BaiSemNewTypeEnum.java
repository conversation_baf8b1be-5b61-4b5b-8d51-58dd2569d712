package com.xk.tp.domain.model.sem;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum BaiSemNewTypeEnum {
    FORM_SUBMIT(3,"表单提交成功"),
    USER_REGISTER(25,"注册"),
    SERVICE_PAY(10,"服务购买成功"),
    ;

    private Integer code;

    private String msg;

    private static final Map<Integer, BaiSemNewTypeEnum> MAP;

    static {
        MAP = Arrays.stream(BaiSemNewTypeEnum.values())
                .collect(Collectors.toMap(BaiSemNewTypeEnum::getCode, enumValue -> enumValue));
    }

    public static BaiSemNewTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
