package com.xk.tp.domain.model.userauth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.userauth.ids.UserAuthIdentifier;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreUserAuthEntity implements Entity<UserAuthIdentifier> {

    private Long userAuthId;

    @Override
    public @NonNull UserAuthIdentifier getIdentifier() {
        return UserAuthIdentifier.builder().userAuthId(userAuthId).build();
    }

    @Override
    public Validatable<UserAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
