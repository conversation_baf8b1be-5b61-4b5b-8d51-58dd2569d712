package com.xk.tp.domain.model.pay;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.pay.PayNotifyStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayNotifyEntity implements Entity<LongIdentifier> {

    private Long notifyId;

    private String orderNo;

    private String payNo;

    private Integer channelType;

    private Integer platformType;

    private PayNotifyStatusEnum status;

    private String tradeStatus;

    private String remark;

    private Long amount;

    private String device;

    private Long accessAccountId;

    private Date createTime;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(notifyId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
