package com.xk.tp.domain.model.account;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.account.ids.AccountIdentifier;
import com.xk.tp.domain.model.account.ids.PayAccountIdentifier;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class PayAccountRoot extends DomainRoot<PayAccountIdentifier> {

    private PayAccountEntity payAccountEntity;

    @Override
    public Validatable<PayAccountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
