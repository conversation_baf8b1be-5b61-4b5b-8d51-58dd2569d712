package com.xk.tp.domain.model.sem;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.support.DomainStaticBeanFactory;
import com.xk.tp.enums.access.AccessPlatformTypeEnum;
import com.xk.tp.domain.support.SemSequenceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class SemRoot extends DomainRoot<LongIdentifier> {

    private final BaiduSemRecordEntity baiduSemRecordEntity;

    private final BaiduSemConfigEntity baiduSemConfigEntity;

    private final AccessPlatformTypeEnum accessPlatformTypeEnum;

    @Builder
    public SemRoot(LongIdentifier identifier, BaiduSemRecordEntity baiduSemRecordEntity,
                   BaiduSemConfigEntity baiduSemConfigEntity, AccessPlatformTypeEnum accessPlatformTypeEnum) {
        super(identifier);
        this.baiduSemRecordEntity = baiduSemRecordEntity;
        this.baiduSemConfigEntity = baiduSemConfigEntity;
        this.accessPlatformTypeEnum = accessPlatformTypeEnum;
    }

    public static Long generateIdentifier(SemSequenceEnum sequenceEnum) {
        IdentifierRoot identifierRoot = IdentifierRoot.builder()
                .identifier(sequenceEnum)
                .type(IdentifierGenerateEnum.CACHE)
                .build();
        return (Long) DomainStaticBeanFactory.getIdentifierGenerateService().generateIdentifier(identifierRoot);
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
