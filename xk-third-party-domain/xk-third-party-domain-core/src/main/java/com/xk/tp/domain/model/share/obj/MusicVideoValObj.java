package com.xk.tp.domain.model.share.obj;

import lombok.*;

/**
 * 音乐视频分享
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/7 21:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MusicVideoValObj extends ShareValObj{

    /**
     * 音频网页的URL
     */
    private String musicWebUrl;

    /**
     * 音频数据的URL
     */
    private String musicUrl;

    /**
     * 歌手名
     */
    private String singerName;

    /**
     * 歌曲时长
     */
    private Integer duration;

    /**
     * 歌词
     */
    private String songLyric;

    /**
     * 专辑名
     */
    private String albumName;

    /**
     * 音乐流派
     */
    private String musicGenre;

    /**
     * 发行时间
     */
    private Long issueDate;

    /**
     * 音乐标识符
     */
    private String identification;

    /**
     * 高清专辑封面图
     */
    private String hdAlbumThumbFileUrl;
}
