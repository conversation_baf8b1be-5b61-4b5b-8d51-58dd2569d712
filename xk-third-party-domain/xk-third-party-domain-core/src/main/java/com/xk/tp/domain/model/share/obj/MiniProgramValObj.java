package com.xk.tp.domain.model.share.obj;

import lombok.*;

/**
 * 小程序分享
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/7 21:14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiniProgramValObj extends ShareValObj{

    /**
     * 兼容低版本的网页链接
     */
    private String webpageUrl;

    /**
     * 小程序的userName
     */
    private String userName;

    /**
     * 小程序页面路径
     */
    private String path;

    /**
     * 小程序新版本的预览图url
     */
    private String hdImageUrl;

    /**
     * 是否使用带 shareTicket 的分享
     */
    private Boolean withShareTicket;
}
