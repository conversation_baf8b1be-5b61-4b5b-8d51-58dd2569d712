package com.xk.tp.domain.model.log;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.support.DomainStaticBeanFactory;
import com.xk.tp.domain.support.LogSequenceEnum;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class UseLogRoot extends DomainRoot<LongIdentifier> {

    private final UseLogEntity useLogEntity;


    @Builder
    public UseLogRoot(LongIdentifier identifier, UseLogEntity useLogEntity) {
        super(identifier);
        this.useLogEntity = useLogEntity;
    }

    public static Long generateIdentifier(LogSequenceEnum sequenceEnum) {
        IdentifierRoot identifierRoot = IdentifierRoot.builder()
                .identifier(sequenceEnum)
                .type(IdentifierGenerateEnum.CACHE)
                .build();
        return (Long) DomainStaticBeanFactory.getIdentifierGenerateService().generateIdentifier(identifierRoot);
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
