package com.xk.tp.domain.model.auth;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.auth.AuthChannelTypeEnum;
import com.xk.tp.enums.auth.AuthPlatformTypeEnum;
import com.xk.tp.domain.model.auth.ids.ApiAuthIdentifier;
import lombok.*;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthEntity implements Entity<ApiAuthIdentifier> {

    private Long accessAccountId;

    private AuthPlatformTypeEnum platformType;

    private AuthChannelTypeEnum channelType;

    private Map<String, String> config;

    @Override
    public @NonNull ApiAuthIdentifier getIdentifier() {
        return ApiAuthIdentifier.builder().accessAccountId(accessAccountId).build();
    }

    @Override
    public Validatable<ApiAuthIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
