package com.xk.tp.domain.model.share.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

/**
 * 分享业务标识符
 *
 * @param shareBusinessId 分享业务id
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/7 20:35
 */
@Builder
public record ShareBusinessIdentifier(Long shareBusinessId) implements Identifier<ShareBusinessIdentifier> {

    @Override
    public @NonNull ShareBusinessIdentifier getIdentifier() {
        return this;
    }
}
