package com.xk.tp.domain.model.recordingTask;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.domain.model.recordingTask.ids.RecordingTaskIdentifier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class RecordingTaskRoot extends DomainRoot<RecordingTaskIdentifier> {

    private RecordingTaskEntity recordingTaskEntity;

    @Builder
    public RecordingTaskRoot(RecordingTaskIdentifier identifier, RecordingTaskEntity recordingTaskEntity) {
        super(identifier);
    }

    @Override
    public Validatable<RecordingTaskIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
