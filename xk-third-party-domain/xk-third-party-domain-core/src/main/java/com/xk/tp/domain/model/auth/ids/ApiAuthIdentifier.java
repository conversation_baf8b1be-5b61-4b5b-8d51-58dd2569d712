package com.xk.tp.domain.model.auth.ids;

import com.myco.mydata.domain.model.Identifier;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @Date 2024/7/27 11:05
 */
@Builder
@Getter
public class ApiAuthIdentifier implements Identifier<ApiAuthIdentifier> {

    private Long accessAccountId;

    @NonNull
    @Override
    public ApiAuthIdentifier getIdentifier() {
        return ApiAuthIdentifier.builder().accessAccountId(accessAccountId).build();
    }
}
