package com.xk.tp.domain.model.sem;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@Data
public class ConversionTypeValueObject implements Serializable {

    /*
        落地页Url
     */
    private String logidUrl;
    /*
        转化类型
     */
    private Integer newType;

    private Integer convertValue;

    private Long convertTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
