package com.xk.tp.domain.model.thirdcaptcha;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.tp.enums.access.AccessChannelTypeEnum;
import com.xk.tp.enums.access.AccessPlatformTypeEnum;
import lombok.*;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThirdCaptchaEntity implements Entity<ThirdCaptchaIdentifier> {

    private Long accessAccountId;

    private AccessPlatformTypeEnum platformType;

    private AccessChannelTypeEnum channelType;

    private Map<String, String> config;

    @Override
    public @NonNull ThirdCaptchaIdentifier getIdentifier() {
        return ThirdCaptchaIdentifier.builder().accessAccountId(accessAccountId).build();
    }

    @Override
    public Validatable<ThirdCaptchaIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
