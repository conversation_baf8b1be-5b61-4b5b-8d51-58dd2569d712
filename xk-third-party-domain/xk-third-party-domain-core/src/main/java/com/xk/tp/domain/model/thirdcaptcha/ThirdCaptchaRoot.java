package com.xk.tp.domain.model.thirdcaptcha;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

/**
 * <AUTHOR>
 * date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ThirdCaptchaRoot extends DomainRoot<ThirdCaptchaIdentifier> {


    private ThirdCaptchaEntity thirdValidateCode;

    private ThirdCheckCaptchaEntity thirdCheckCaptcha;

    @Builder
    public ThirdCaptchaRoot(ThirdCaptchaIdentifier identifier, ThirdCaptchaEntity thirdValidateCode, ThirdCheckCaptchaEntity thirdCheckCaptcha) {
        super(identifier);
        this.thirdValidateCode = thirdValidateCode;
        this.thirdCheckCaptcha = thirdCheckCaptcha;
    }

    @Override
    public Validatable<ThirdCaptchaIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
