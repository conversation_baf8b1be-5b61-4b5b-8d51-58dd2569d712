package com.xk.tp.domain.event.reconciled;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_THIRD_PARTY, domainName = DomainNameEnum.THIRD_PARTY)
public class CreateFinancialTransactionEvent extends AbstractCommonsDomainEvent implements Serializable {

    private final PayPlatformTypeEnum payPlatformTypeEnum;

    private final String financialTransactionId;

    private final Date financialDate;

    private final String payNo;

    private final Long payAmount;

    private final String payAccount;

    private final String receiveAccount;

    private final Integer payType;

    private final Integer payDirection;

    private final Date payCreateTime;

    @Builder
    public CreateFinancialTransactionEvent(@NonNull Long identifier, Map<String, Object> context, PayPlatformTypeEnum payPlatformTypeEnum, String financialTransactionId, Date financialDate, String payNo, Long payAmount, String payAccount, String receiveAccount, Integer payType, Integer payDirection, Date payCreateTime) {
        super(identifier, context);
        this.payPlatformTypeEnum = payPlatformTypeEnum;
        this.financialTransactionId = financialTransactionId;
        this.financialDate = financialDate;
        this.payNo = payNo;
        this.payAmount = payAmount;
        this.payAccount = payAccount;
        this.receiveAccount = receiveAccount;
        this.payType = payType;
        this.payDirection = payDirection;
        this.payCreateTime = payCreateTime;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
