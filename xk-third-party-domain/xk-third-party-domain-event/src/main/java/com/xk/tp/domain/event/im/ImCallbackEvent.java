package com.xk.tp.domain.event.im;


import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractTpDomainEvent;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 推送消息事件
 */
@Getter
public class ImCallbackEvent extends AbstractTpDomainEvent {

    /**
     * 群组id
     */
    private final String imId;

    /**
     * 回调类型 1、进入房间 2、退出房间
     */
    private final Integer callbackType;

    /**
     * 推送人id
     */
    private final List<String> userIds;


    @Builder
    public ImCallbackEvent(@NonNull Long identifier, Map<String, Object> context, String imId, Integer callbackType, List<String> userIds) {
        super(identifier, context);

        this.imId = imId;
        this.callbackType = callbackType;
        this.userIds = userIds;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).imId(this.imId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
