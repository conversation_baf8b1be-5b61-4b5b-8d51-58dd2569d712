package com.xk.tp.domain.event.push;


import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractTpDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;


/**
 * 推送消息事件
 */
@Getter
public class PushAppMessageEvent extends AbstractTpDomainEvent {

    /**
     * 设备标识符id
     */
    private final String deviceId;

    /**
     * 推送人id
     */
    private final Long pushUserId;

    /**
     * 推送消息id
     */
    private final String messageId;

    /**
     * 消息模板id
     */
    private final Long messageTemplateId;

    /**
     * 消息标题
     */
    private final String title;

    /**
     * 消息内容
     */
    private final String content;

    /**
     * 消息类型
     */
    private final String messageType;

    /**
     * 推送渠道
     */
    private Integer pushChannelType;

    /**
     * 点击通知后续动作
     * intent：打开应用内特定页面（intent和want字段必须填写一个）
     * url：打开网页地址
     * payload：自定义消息内容启动应用
     * payload_custom：自定义消息内容不启动应用
     * startapp：打开应用首页
     * none：纯通知，无后续动作
     */
    private String clickType;

    /**
     * 点击通知栏消息时，唤起系统默认浏览器打开此链接
     */
    private String url;


    @Builder
    public PushAppMessageEvent(@NonNull Long identifier, Map<String, Object> context, String deviceId,
                               Long pushUserId, String messageId, Long messageTemplateId, String title,
                               String content, String messageType, Integer pushChannelType,
                               String clickType, String url) {
        super(identifier, context);
        this.deviceId = deviceId;
        this.pushUserId = pushUserId;
        this.messageId = messageId;
        this.messageTemplateId = messageTemplateId;
        this.title = title;
        this.content = content;
        this.messageType = messageType;
        this.pushChannelType = pushChannelType;
        this.clickType = clickType;
        this.url = url;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
