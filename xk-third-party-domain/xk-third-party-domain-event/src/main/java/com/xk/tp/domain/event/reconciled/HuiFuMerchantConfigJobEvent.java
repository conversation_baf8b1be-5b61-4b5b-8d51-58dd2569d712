package com.xk.tp.domain.event.reconciled;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_THIRD_PARTY, domainName = DomainNameEnum.THIRD_PARTY)
public class HuiFuMerchantConfigJobEvent extends AbstractCommonsDomainEvent implements Serializable {

    private final LocalDate tradeDate;

    @Builder
    public HuiFuMerchantConfigJobEvent(@NonNull Long identifier, Map<String, Object> context, LocalDate tradeDate) {
        super(identifier, context);
        this.tradeDate = tradeDate;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
