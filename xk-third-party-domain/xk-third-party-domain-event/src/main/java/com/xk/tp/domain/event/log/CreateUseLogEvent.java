package com.xk.tp.domain.event.log;

import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * 日志事件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/9 17:30
 */
@Getter
@EventDefinition(
        appName = AppNameEnum.YD_LOG,
        domainName = DomainNameEnum.LOG)
public class CreateUseLogEvent extends AbstractCommonsDomainEvent {

    /**
     * 创建人
     */
    private final Long createId;


    /**
     * url地址
     */
    private final String apiUrl;

    /**
     * 参数
     */
    private final String params;

    /**
     * 响应参数
     */
    private final String response;

    /**
     * 业务类型
     */
    private final Integer busiType;

    /**
     * 业务id
     */
    private final String busiId;

    /**
     * 具体业务类型 商品审核  担担查找
     */
    private final Integer cateType;

    /**
     * 来源平台
     */
    private final String sourcePlatform;

    /**
     * 目标平台
     */
    private final Long targetAccessId;

    @Builder
    public CreateUseLogEvent(@NonNull Long identifier, Map<String, Object> context, Long logId, Long createId, String apiUrl, String params, String response,
                             Integer busiType, String busiId, Integer cateType, String sourcePlatform, Long targetAccessId) {
        super(identifier, context);
        this.createId = createId;
        this.apiUrl = apiUrl;
        this.params = params;
        this.response = response;
        this.busiType = busiType;
        this.busiId = busiId;
        this.cateType = cateType;
        this.sourcePlatform = sourcePlatform;
        this.targetAccessId = targetAccessId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
