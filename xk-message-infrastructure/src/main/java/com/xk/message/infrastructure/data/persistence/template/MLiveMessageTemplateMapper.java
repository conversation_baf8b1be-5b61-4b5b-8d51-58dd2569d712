package com.xk.message.infrastructure.data.persistence.template;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.message.infrastructure.data.po.template.MLiveMessageTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_live_message_template(直播间内容模板)】的数据库操作Mapper
* @createDate 2025-08-06 20:43:34
* @Entity com.xk.message.infrastructure.data.po.template.MLiveMessageTemplate
*/
@Repository
@Table("m_live_message_template")
public interface MLiveMessageTemplateMapper {

    int deleteByPrimaryKey(MLiveMessageTemplate record);

    int insert(MLiveMessageTemplate record);

    int insertSelective(MLiveMessageTemplate record);

    MLiveMessageTemplate selectByPrimaryKey(MLiveMessageTemplate record);

    int updateByPrimaryKeySelective(MLiveMessageTemplate record);

    int updateByPrimaryKey(MLiveMessageTemplate record);

    List<MLiveMessageTemplate> selectByBusinessMessageTemplateId(MLiveMessageTemplate record);
}
