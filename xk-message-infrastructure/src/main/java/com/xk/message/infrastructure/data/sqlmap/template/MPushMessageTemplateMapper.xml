<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xk.message.infrastructure.data.persistence.template.MPushMessageTemplateMapper">

    <resultMap id="BaseResultMap" type="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate">
            <id property="templateId" column="template_id" jdbcType="BIGINT"/>
            <result property="templateContent" column="template_content" jdbcType="VARCHAR"/>
            <result property="templateParam" column="template_param" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="businessType" column="business_type" jdbcType="INTEGER"/>
            <result property="platformType" column="platform_type" jdbcType="INTEGER"/>
            <result property="businessMessageTemplateId" column="business_message_template_id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        template_id,template_content,template_param,
        create_id,create_time,update_id,
        update_time,business_type,platform_type,
        business_message_template_id,title
    </sql>

    <select id="selectByPrimaryKey" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from m_push_message_template
        where  template_id = #{templateId,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate">
        delete from m_push_message_template
        where  template_id = #{templateId,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="template_id" keyProperty="templateId" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate" useGeneratedKeys="true">
        insert into m_push_message_template
        ( template_id,template_content,template_param
        ,create_id,create_time,update_id
        ,update_time,business_type,platform_type
        ,business_message_template_id,title)
        values (#{templateId,jdbcType=BIGINT},#{templateContent,jdbcType=VARCHAR},#{templateParam,jdbcType=VARCHAR}
        ,#{createId,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP},#{updateId,jdbcType=BIGINT}
        ,#{updateTime,jdbcType=TIMESTAMP},#{businessType,jdbcType=INTEGER},#{platformType,jdbcType=INTEGER}
        ,#{businessMessageTemplateId,jdbcType=BIGINT},#{title,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="template_id" keyProperty="templateId" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate" useGeneratedKeys="true">
        insert into m_push_message_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="templateId != null">template_id,</if>
                <if test="templateContent != null">template_content,</if>
                <if test="templateParam != null">template_param,</if>
                <if test="createId != null">create_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateId != null">update_id,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="businessType != null">business_type,</if>
                <if test="platformType != null">platform_type,</if>
                <if test="businessMessageTemplateId != null">business_message_template_id,</if>
                <if test="title != null">title,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="templateId != null">#{templateId,jdbcType=BIGINT},</if>
                <if test="templateContent != null">#{templateContent,jdbcType=VARCHAR},</if>
                <if test="templateParam != null">#{templateParam,jdbcType=VARCHAR},</if>
                <if test="createId != null">#{createId,jdbcType=BIGINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateId != null">#{updateId,jdbcType=BIGINT},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="businessType != null">#{businessType,jdbcType=INTEGER},</if>
                <if test="platformType != null">#{platformType,jdbcType=INTEGER},</if>
                <if test="businessMessageTemplateId != null">#{businessMessageTemplateId,jdbcType=BIGINT},</if>
                <if test="title != null">#{title,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate">
        update m_push_message_template
        <set>
                <if test="templateContent != null">
                    template_content = #{templateContent,jdbcType=VARCHAR},
                </if>
                <if test="templateParam != null">
                    template_param = #{templateParam,jdbcType=VARCHAR},
                </if>
                <if test="createId != null">
                    create_id = #{createId,jdbcType=BIGINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateId != null">
                    update_id = #{updateId,jdbcType=BIGINT},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="businessType != null">
                    business_type = #{businessType,jdbcType=INTEGER},
                </if>
                <if test="platformType != null">
                    platform_type = #{platformType,jdbcType=INTEGER},
                </if>
                <if test="businessMessageTemplateId != null">
                    business_message_template_id = #{businessMessageTemplateId,jdbcType=BIGINT},
                </if>
                <if test="title != null">
                    title = #{title,jdbcType=VARCHAR},
                </if>
        </set>
        where   template_id = #{templateId,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate">
        update m_push_message_template
        set 
            template_content =  #{templateContent,jdbcType=VARCHAR},
            template_param =  #{templateParam,jdbcType=VARCHAR},
            create_id =  #{createId,jdbcType=BIGINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_id =  #{updateId,jdbcType=BIGINT},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            business_type =  #{businessType,jdbcType=INTEGER},
            platform_type =  #{platformType,jdbcType=INTEGER},
            business_message_template_id =  #{businessMessageTemplateId,jdbcType=BIGINT},
            title =  #{title,jdbcType=VARCHAR}
        where   template_id = #{templateId,jdbcType=BIGINT} 
    </update>

    <select id="selectByBusinessMessageTemplateId"
            parameterType="com.xk.message.infrastructure.data.po.template.MPushMessageTemplate"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from m_push_message_template
        where business_message_template_id = #{businessMessageTemplateId,jdbcType=BIGINT}
    </select>
</mapper>
