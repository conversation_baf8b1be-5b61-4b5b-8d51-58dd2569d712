package com.xk.message.infrastructure.data.po.template;

import java.util.Date;

import com.myco.framework.cache.CacheLevel;
import com.myco.framework.cache.annotations.Cache;
import com.myco.framework.cache.annotations.KeyProperty;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.domain.model.template.entity.LiveMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.PushMessageTemplateEntity;
import com.xk.message.enums.message.BusinessTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直播间内容模板
 * @TableName m_live_message_template
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(level = CacheLevel.REDIS)
@AutoMappers({
        @AutoMapper(target = LiveMessageTemplateEntity.class, uses = {BusinessTypeEnum.class, PlatformTypeEnum.class})
})
public class MLiveMessageTemplate {
    /**
     * 模板id
     */
    @KeyProperty
    private Long templateId;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    @ReverseAutoMapping(
            expression = "java(source.getBusinessType() == null ? null : source.getBusinessType().getValue())"
    )
    private Integer businessType;

    /**
     * 平台类型
     */
    @ReverseAutoMapping(
            expression = "java(source.getPlatformType() == null ? null : source.getPlatformType().getValue())"
    )
    private Integer platformType;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;
}