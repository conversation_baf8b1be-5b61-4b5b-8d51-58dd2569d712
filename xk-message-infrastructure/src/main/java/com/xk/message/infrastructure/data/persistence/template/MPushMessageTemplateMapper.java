package com.xk.message.infrastructure.data.persistence.template;

import com.myco.framework.sharding.annotation.Table;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.message.infrastructure.data.po.template.MPushMessageTemplate;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_push_message_template(推送内容模板)】的数据库操作Mapper
* @createDate 2025-08-06 19:34:45
* @Entity com.xk.message.infrastructure.data.po.template.MPushMessageTemplate
*/
@Repository
@Table("m_push_message_template")
public interface MPushMessageTemplateMapper {

    int deleteByPrimaryKey(MPushMessageTemplate record);

    int insert(MPushMessageTemplate record);

    int insertSelective(MPushMessageTemplate record);

    MPushMessageTemplate selectByPrimaryKey(MPushMessageTemplate record);

    int updateByPrimaryKeySelective(MPushMessageTemplate record);

    int updateByPrimaryKey(MPushMessageTemplate record);

    List<MPushMessageTemplate> selectByBusinessMessageTemplateId(MPushMessageTemplate record);
}
