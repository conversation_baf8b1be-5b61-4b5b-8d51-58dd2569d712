<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.myco.mydata</groupId>
        <artifactId>mydata-start-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.xk</groupId>
    <artifactId>xk-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>xk-dependencies-pom</name>
    <description>xk-dependencies</description>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>
        <!-- xk-start Internal properties -->
        <xk-start-parent.version>${revision}</xk-start-parent.version>
        <xk-config.version>${revision}</xk-config.version>
        <xk-promotion.version>${revision}</xk-promotion.version>
        <xk-acct.version>${revision}</xk-acct.version>
        <xk-auth.version>${revision}</xk-auth.version>
        <xk-corp.version>${revision}</xk-corp.version>
        <xk-goods.version>${revision}</xk-goods.version>
        <xk-order.version>${revision}</xk-order.version>
        <xk-search.version>${revision}</xk-search.version>
        <xk-finance.version>${revision}</xk-finance.version>
        <xk-third-party.version>${revision}</xk-third-party.version>
        <xk-cms.version>${revision}</xk-cms.version>
        <xk-message.version>${revision}</xk-message.version>
        <xk-ewd.version>${revision}</xk-ewd.version>
        <dg-java-sdk.version>3.0.27</dg-java-sdk.version>
        <juniversalchardet.version>2.5.0</juniversalchardet.version>
        <httpclient.version>4.5.9</httpclient.version>
        <tencentcloud.version>3.1.1315</tencentcloud.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-trtc</artifactId>
                <version>${tencentcloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.albfernandez</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>${juniversalchardet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huifu.bspay.sdk</groupId>
                <artifactId>dg-java-sdk</artifactId>
                <version>${dg-java-sdk.version}</version>
            </dependency>
            <!-- xk-start Internal dependencies -->
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-parent</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-domain</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-domain-core</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-domain-enum</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-domain-event</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-interfaces</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-gateway</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-infrastructure</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-application</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk</groupId>
                <artifactId>xk-start-server</artifactId>
                <version>${xk-start-parent.version}</version>
            </dependency>
            <!-- xk-promotion Internal dependencies -->
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-domain</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-domain-core</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-domain-enum</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-domain-event</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-interfaces</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-gateway</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-infrastructure</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-application</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.promotion</groupId>
                <artifactId>xk-promotion-server</artifactId>
                <version>${xk-promotion.version}</version>
            </dependency>

            <!-- xk-config Internal dependencies -->
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-domain</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-domain-core</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-domain-enum</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-domain-event</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-interfaces</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-gateway</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-infrastructure</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-application</artifactId>
                <version>${xk-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.config</groupId>
                <artifactId>xk-config-server</artifactId>
                <version>${xk-config.version}</version>
            </dependency>

            <!-- xk-acct Internal dependencies -->
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-domain</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-domain-core</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-domain-enum</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-domain-event</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-interfaces</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-gateway</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-infrastructure</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-application</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.acct</groupId>
                <artifactId>xk-acct-server</artifactId>
                <version>${xk-acct.version}</version>
            </dependency>
            <!-- xk-auth Internal dependencies -->
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-domain</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-domain-core</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-domain-enum</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-domain-event</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-interfaces</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-gateway</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-infrastructure</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-application</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.auth</groupId>
                <artifactId>xk-auth-server</artifactId>
                <version>${xk-auth.version}</version>
            </dependency>
            <!-- xk-corp Internal dependencies -->
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-domain</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-domain-core</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-domain-enum</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-domain-event</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-interfaces</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-gateway</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-infrastructure</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-application</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.corp</groupId>
                <artifactId>xk-corp-server</artifactId>
                <version>${xk-corp.version}</version>
            </dependency>
            <!-- xk-goods Internal dependencies -->
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-domain</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-domain-core</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-domain-enum</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-domain-event</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-interfaces</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-gateway</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-infrastructure</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-application</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.goods</groupId>
                <artifactId>xk-goods-server</artifactId>
                <version>${xk-goods.version}</version>
            </dependency>
            <!-- xk-order Internal dependencies -->
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-domain</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-domain-core</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-domain-enum</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-domain-event</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-interfaces</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-gateway</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-infrastructure</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-application</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.order</groupId>
                <artifactId>xk-order-server</artifactId>
                <version>${xk-order.version}</version>
            </dependency>
            <!-- xk-search Internal dependencies -->
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-domain</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-domain-core</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-domain-enum</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-domain-event</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-interfaces</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-gateway</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-infrastructure</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-application</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.search</groupId>
                <artifactId>xk-search-server</artifactId>
                <version>${xk-search.version}</version>
            </dependency>
            <!-- xk-finance Internal dependencies -->
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-domain</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-domain-core</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-domain-enum</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-domain-event</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-interfaces</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-gateway</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-infrastructure</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-application</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.finance</groupId>
                <artifactId>xk-finance-server</artifactId>
                <version>${xk-finance.version}</version>
            </dependency>
            <!-- xk-third-party Internal dependencies -->
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-domain</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-domain-core</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-domain-enum</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-domain-event</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-interfaces</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-gateway</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-infrastructure</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-application</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.tp</groupId>
                <artifactId>xk-third-party-server</artifactId>
                <version>${xk-third-party.version}</version>
            </dependency>
            <!-- xk-cms Internal dependencies -->
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-domain</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-domain-core</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-domain-enum</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-domain-event</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-interfaces</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-gateway</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-infrastructure</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-application</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.cms</groupId>
                <artifactId>xk-cms-server</artifactId>
                <version>${xk-cms.version}</version>
            </dependency>
            <!-- xk-message Internal dependencies -->
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-domain</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-domain-core</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-domain-enum</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-domain-event</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-interfaces</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-gateway</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-infrastructure</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-application</artifactId>
                <version>${xk-message.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.message</groupId>
                <artifactId>xk-message-server</artifactId>
                <version>${xk-message.version}</version>
            </dependency>

            <!-- xk-ewd Internal dependencies -->
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-domain</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-domain-core</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-domain-enum</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-domain-event</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-interfaces</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-gateway</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-infrastructure</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-application</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xk.ewd</groupId>
                <artifactId>xk-ewd-server</artifactId>
                <version>${xk-ewd.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!---->
    <repositories>
        <repository>
            <id>${deploy.public.id}</id>
            <name>Nexus Repository</name>
            <url>${deploy.public.url}</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>${deploy.releases.id}</id>
            <url>${deploy.releases.url}</url>
        </repository>
        <snapshotRepository>
            <id>${deploy.snapshot.id}</id>
            <url>${deploy.snapshot.url}</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <!--默认开启local-->
        <profile>
            <id>local</id>
            <properties>
                <profileActive>local</profileActive>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <profileActive>pro</profileActive>
            </properties>
        </profile>
    </profiles>
</project>
