<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk</groupId>
        <artifactId>xk-dependencies</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.xk.order</groupId>
    <artifactId>xk-order</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>xk-order-pom</name>
    <description>xk-order</description>
    <modules>
        <module>xk-order-domain</module>
        <module>xk-order-interfaces</module>
        <module>xk-order-gateway</module>
        <module>xk-order-infrastructure</module>
        <module>xk-order-application</module>
        <module>xk-order-server</module>
    </modules>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>
    </properties>
    <!---->
    <repositories>
        <repository>
            <id>${deploy.public.id}</id>
            <name>Nexus Repository</name>
            <url>${deploy.public.url}</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>${deploy.releases.id}</id>
            <url>${deploy.releases.url}</url>
        </repository>
        <snapshotRepository>
            <id>${deploy.snapshot.id}</id>
            <url>${deploy.snapshot.url}</url>
        </snapshotRepository>
    </distributionManagement>
</project>
