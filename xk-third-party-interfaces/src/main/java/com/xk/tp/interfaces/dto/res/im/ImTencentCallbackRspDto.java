package com.xk.tp.interfaces.dto.res.im;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImTencentCallbackRspDto {

    @JSONField(name = "ActionStatus")
    private String actionStatus;

    @JSONField(name = "ErrorInfo")
    private String errorInfo;

    @JSONField(name = "ErrorCode")
    private Integer errorCode;

}
