package com.xk.tp.interfaces.dto.req.im;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.myco.mydata.domain.model.action.session.AbstractSession;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImCallbackReqDto extends AbstractSession {

    /**
     * 回调命令
     */
    @JsonAlias("CallbackCommand")
    private String callbackCommand;

    /**
     * 群组id
     */
    @JsonAlias("GroupId")
    private String groupId;

    /**
     * 群组类型
     */
    @JsonAlias("Type")
    private String type;

    /**
     * 成员离开方式：Kicked-被踢；Quit-主动退群
     */
    @JsonAlias("ExitType")
    private String exitType;

    /**
     * 操作者
     */
    @JsonAlias("Operator_Account")
    private String operatorAccount;

    /**
     * 离开群的成员列表
     */
    @JsonAlias("NewMemberList")
    private List<ImExitMember> newMemberList;

    /**
     * 离开群的成员列表
     */
    @JsonAlias("ExitMemberList")
    private List<ImExitMember> exitMemberList;

    /**
     * 触发本次回调的时间戳
     */
    @JsonAlias("EventTime")
    private Long eventTime;

    /**
     * 入群方式
     */
    @JsonAlias("JoinType")
    private String joinType;

    /**
     *
     */
    @JsonAlias("TopicId")
    private String TopicId;

}
