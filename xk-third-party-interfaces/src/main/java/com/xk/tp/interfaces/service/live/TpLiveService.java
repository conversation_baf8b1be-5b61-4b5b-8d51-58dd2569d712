package com.xk.tp.interfaces.service.live;

import com.xk.tp.interfaces.dto.req.live.SignReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.tp.interfaces.dto.req.live.DismissRoomReqDto;
import com.xk.tp.interfaces.dto.req.live.RemoveUserReqDto;

import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@HttpExchange("/tp/live")
public interface TpLiveService extends IApplicationService {

    @PostExchange("/remove/user")
    Mono<Void> removeUser(@RequestBody Mono<RemoveUserReqDto> dtoMono);

    @PostExchange("/dismiss/room")
    Mono<Void> dismissRoom(@RequestBody Mono<DismissRoomReqDto> dtoMono);

    @PostExchange("/remove/user/str")
    Mono<Void> removeUserStr(@RequestBody Mono<RemoveUserReqDto> dtoMono);

    @PostExchange("/dismiss/room/str")
    Mono<Void> dismissRoomStr(@RequestBody Mono<DismissRoomReqDto> dtoMono);

    @PostExchange("/sign")
    Mono<String> sign(@RequestBody Mono<SignReqDto> dtoMono);
}
