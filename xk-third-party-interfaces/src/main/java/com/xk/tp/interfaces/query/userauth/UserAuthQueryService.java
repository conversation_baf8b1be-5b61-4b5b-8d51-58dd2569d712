package com.xk.tp.interfaces.query.userauth;

import com.myco.mydata.interfaces.query.IQueryService;
import com.xk.tp.interfaces.dto.req.userauth.AuthUserInfoReqDto;
import com.xk.tp.interfaces.dto.req.userauth.PreUserAuthReqDto;
import com.xk.tp.interfaces.dto.res.userauth.AuthUserInfoResDto;
import com.xk.tp.interfaces.dto.res.userauth.PreUserAuthResDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 用户三方授权
 */
@HttpExchange("/tp/query/userAuth")
public interface UserAuthQueryService extends IQueryService {

    /**
     * 查询预授权信息
     *
     * @param dtoMono
     * @return
     */
    @PostExchange("/findPreUserAuth")
    Mono<PreUserAuthResDto> findPreUserAuth(@RequestBody Mono<PreUserAuthReqDto> dtoMono);

    /**
     * 查询用户信息
     * @param dtoMono dtoMono
     * @return Mono<AuthUserInfoResDto>
     */
    @PostExchange("/findUserInfo")
    Mono<AuthUserInfoResDto> findUserInfo(@RequestBody Mono<AuthUserInfoReqDto> dtoMono);
}
