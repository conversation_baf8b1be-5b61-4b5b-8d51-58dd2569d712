package com.xk.tp.interfaces.service.pay;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.tp.interfaces.dto.pay.*;
import com.xk.tp.interfaces.dto.res.pay.PlaceOrderResDto;

import reactor.core.publisher.Mono;

/**
 * 支付操作
 *
 * <AUTHOR>
 * date 2024/07/23
 */
@HttpExchange("/tp/transfer")
public interface TransferPayService extends IApplicationService {

    /**
     * 提现
     *
     * @param dto
     * @return
     */
    @PostExchange("/createPay")
    Mono<Boolean> transfer(@RequestBody Mono<TransferOrderDto> dto);

}
