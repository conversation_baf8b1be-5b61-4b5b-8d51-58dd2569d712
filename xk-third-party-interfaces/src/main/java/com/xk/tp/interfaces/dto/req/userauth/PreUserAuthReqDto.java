package com.xk.tp.interfaces.dto.req.userauth;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PreUserAuthReqDto extends RequireSessionDto {

    /**
     * 渠道类型 1微信 6阿里云 7苹果
     */
    @NotNull
    private Integer channelType;

}
