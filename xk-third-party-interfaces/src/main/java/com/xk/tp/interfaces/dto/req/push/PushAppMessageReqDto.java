package com.xk.tp.interfaces.dto.req.push;


import com.myco.mydata.domain.model.action.session.AbstractSession;

import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushAppMessageReqDto extends AbstractSession {

    /**
     * 设备标识符id
     */
    private String deviceId;

    /**
     * 推送人id
     */
    private Long pushUserId;

    /**
     * 推送消息id
     */
    private String messageId;

    /**
     * 消息模板id
     */
    private Long messageTemplateId;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 推送渠道
     */
    private Integer pushChannelType;
}
