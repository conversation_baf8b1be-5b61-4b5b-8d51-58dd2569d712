package com.xk.tp.interfaces.dto.res.share;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务分享响应dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 9:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShareBusinessResDto {

    /**
     * 分享业务id
     * 业务id，如商品id等
     */
    private Long shareBusinessId;

    /**
     * 分享业务类型
     * 1:商品
     */
    private Integer shareBusinessType;

    /**
     * 业务补充字段
     * shareBusinessType 为商品时，业务补充字段为商品类型 1:商城商品 2:物料商品 3:收藏卡 4:商家商品
     */
    private String businessExtField;

    /**
     * 渠道类型
     * 1:微信
     */
    private Integer channelType;

    /**
     * 分享类型
     * 1:文本 2:图片 3:网页 4:视频 5:音乐视频 6:小程序
     */
    private Integer shareType;

    /**
     * 分享视图数据
     */
    private ShareViewDto shareView;


    /**
     * 网页数据
     */
    private WebpageValDto webPageVal;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ShareViewDto {

        /**
         * 平台appId
         */
        private String appId;

        /**
         *  消息标题
         */
        private String title;

        /**
         *  消息描述
         */
        private String description;

        /**
         *  缩略图url
         */
        private String thumbUrl ;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WebpageValDto {

        /**
         * html链接
         */
        private String webpageUrl;
    }




}
