package com.xk.tp.interfaces.service.im;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.tp.interfaces.dto.req.im.*;
import com.xk.tp.interfaces.dto.res.im.ImTencentCallbackRspDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@HttpExchange("/tp/im")
public interface TpImService extends IApplicationService {

    @PostExchange("/create/group")
    Mono<Void> createGroup(@RequestBody Mono<CreateGroupReqDto> dtoMono);

    @PostExchange("/delete/group")
    Mono<Void> deleteGroup(@RequestBody Mono<DeleteGroupReqDto> dtoMono);

    @PostExchange("/sign")
    Mono<String> sign(@RequestBody Mono<ImSignReqDto> dtoMono);

    @PostExchange("/online/number")
    Mono<Long> selectOnlineNumber(@RequestBody Mono<ImOnlineNumberReqDto> dtoMono);

    @PostExchange("/send/msg/all")
    Mono<Long> sendMsgAll(@RequestBody Mono<ImSendMsgAllReqDto> dtoMono);

    @PostExchange("/send/msg")
    Mono<Long> sendMsg(@RequestBody Mono<ImSendMsgReqDto> dtoMono);

    @PostExchange("/send/msg/system")
    Mono<Void> sendMsgSystem(@RequestBody Mono<ImSendMsgSystemReqDto> dtoMono);

    @PostExchange("/tencent/callback")
    Mono<ImTencentCallbackRspDto> tencentCallback(@RequestBody Mono<ImCallbackReqDto> dtoMono);
}
