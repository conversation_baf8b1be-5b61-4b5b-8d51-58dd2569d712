package com.xk.tp.interfaces.dto.req.share;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务分享请求dto
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 9:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ShareBusinessReqDto extends RequireSessionDto {

    /**
     * 分享业务id
     * 业务id，如商品id等
     */
    @NotNull(message = "分享业务id不能为空")
    private Long shareBusinessId;

    /**
     * 分享业务类型
     * 1:商品
     */
    private Integer shareBusinessType = 1;

    /**
     * 业务补充字段
     * shareBusinessType 为商品时，业务补充字段为商品类型 1:商城商品 2:物料商品 3:收藏卡 4:商家商品
     */
    private String businessExtField;

    /**
     * 渠道类型
     * 1:微信
     */
    private Integer channelType = 1;

    /**
     * 分享类型
     * 1:文本 2:图片 3:网页 4:视频 5:音乐视频 6:小程序
     */
    @NotNull(message = "分享类型不能为空")
    private Integer shareType = 3;
}
