package com.xk.tp.interfaces.dto.req.userauth;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccessTokenReqDto extends AbstractSession {


    /**
     * 请求code
     */
    @NotBlank
    private String code;
    /**
     * 渠道类型 1微信 6阿里云 7苹果
     */
    @NotNull
    private Integer channelType;
}
