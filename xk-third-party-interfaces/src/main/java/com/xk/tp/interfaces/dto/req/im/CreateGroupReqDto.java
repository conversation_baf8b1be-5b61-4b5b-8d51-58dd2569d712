package com.xk.tp.interfaces.dto.req.im;

import com.myco.mydata.domain.model.action.session.AbstractSession;
import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import lombok.*;

/**
 * <AUTHOR> date 2024/07/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateGroupReqDto extends RequireSessionDto {

    /**
     * 直播平台类型
     */
    private Integer imPlatformType;

    /**
     * 群组id
     */
    private String imId;

    /**
     * 群组名称
     */
    private String name;

    /**
     * 管理员账号
     */
    private String ownerAccount;

}
