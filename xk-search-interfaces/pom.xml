<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xk.search</groupId>
        <artifactId>xk-search</artifactId>
        <version>${revision}</version> <!-- lookup parent from repository -->
    </parent>
    <artifactId>xk-search-interfaces</artifactId>
    <packaging>jar</packaging>
    <name>xk-search-interfaces</name>
    <description>xk-search-interfaces</description>
    <properties>
        <mapstruct-plus.mapperPackage>com.xk.search.interfaces.convertor.auto</mapstruct-plus.mapperPackage>
        <mapstruct-plus.adapterPackage>com.xk.search.interfaces.convertor.adapter</mapstruct-plus.adapterPackage>
        <mapstruct-plus.autoConfigPackage>com.xk.search.interfaces.config</mapstruct-plus.autoConfigPackage>
        <mapstruct-plus.adapterClassName>XkSearchInterfacesConverterMapperAdapter</mapstruct-plus.adapterClassName>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.xk</groupId>
            <artifactId>xk-start-interfaces</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xk.search</groupId>
            <artifactId>xk-search-domain-enum</artifactId>
        </dependency>
    </dependencies>
</project>
