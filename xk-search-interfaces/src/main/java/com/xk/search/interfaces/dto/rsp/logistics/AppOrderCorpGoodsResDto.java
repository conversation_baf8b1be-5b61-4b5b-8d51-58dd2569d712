package com.xk.search.interfaces.dto.rsp.logistics;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/8/6 17:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppOrderCorpGoodsResDto {

    /**
     * 商品主图
     */
    private List<String> goodsImages;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品数量
     */
    private Integer goodsNum;

    /**
     * 获赠时间
     */
    private Date createTime;

    /**
     * 商品来源 商品购买|商品赠品
     */
    private String goodsSource;

}
