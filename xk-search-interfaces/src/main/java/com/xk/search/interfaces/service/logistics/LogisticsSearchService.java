package com.xk.search.interfaces.service.logistics;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.search.interfaces.dto.req.logistics.LogisticsIndexReqDto;
import com.xk.search.interfaces.dto.req.logistics.SearchLogisticsConfirmReqDto;

import reactor.core.publisher.Mono;

/**
 * 物流服务
 */
@HttpExchange("/search/logistics")
public interface LogisticsSearchService extends IApplicationService {

    /**
     * 创建索引
     *
     * @return Mono<Void>
     */
    @PostExchange("/addIndex")
    Mono<Void> addIndex(@RequestBody Mono<LogisticsIndexReqDto> mono);

    /**
     * 更新索引
     *
     * @return Mono<Void>
     */
    @PostExchange("/updateIndex")
    Mono<Void> updateIndex(@RequestBody Mono<LogisticsIndexReqDto> mono);

    /**
     * 删除索引
     *
     * @return Mono<Void>
     */
    @PostExchange("/deleteIndex")
    Mono<Void> deleteIndex(@RequestBody Mono<LogisticsIndexReqDto> mono);

    /**
     * 确认收货
     *
     * @return Mono<Void>
     */
    @PostExchange("/confirm")
    Mono<Void> updateConfirm(@RequestBody Mono<SearchLogisticsConfirmReqDto> mono);
}
