package com.xk.search.interfaces.service.order;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.search.interfaces.dto.req.order.OrderIndexReqDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 订单搜索服务
 */
@HttpExchange("/search/order")
public interface OrderSearchService extends IApplicationService {

    /**
     * 创建索引 物料订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/material/addIndex")
    Mono<Void> addIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /***
     * 更新索引 物料订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/material/updateIndex")
    Mono<Void> updateIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 物料订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/material/deleteIndex")
    Mono<Void> deleteIndexMaterial(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 创建索引 商城商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/mall/addIndex")
    Mono<Void> addIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /***
     * 更新索引 商城商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/mall/updateIndex")
    Mono<Void> updateIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 商城商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/mall/deleteIndex")
    Mono<Void> deleteIndexMall(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 创建索引 商家商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/merchant/addIndex")
    Mono<Void> addIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /***
     * 更新索引 商家商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/merchant/updateIndex")
    Mono<Void> updateIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

    /**
     * 删除索引 商家商品订单
     * 
     * @return Mono<Void>
     */
    @PostExchange("/merchant/deleteIndex")
    Mono<Void> deleteIndexMerchant(@RequestBody Mono<OrderIndexReqDto> indexReqDtoMono);

}
