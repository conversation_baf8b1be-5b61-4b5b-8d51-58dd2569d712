package com.xk.search.interfaces.service.search;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import com.myco.mydata.interfaces.service.IApplicationService;
import com.xk.search.interfaces.dto.req.search.IndexReqDto;

import reactor.core.publisher.Mono;

/**
 * 搜索服务
 */
@HttpExchange("/search")
public interface SearchService extends IApplicationService {

    /**
     * 创建索引
     * 
     * @param indexReqDtoMono indexReqDtoMono
     * @return Mono<Void>
     */
    @PostExchange("/addIndex")
    Mono<Void> addIndex(@RequestBody Mono<IndexReqDto> indexReqDtoMono);

    /***
     * 更新索引
     * 
     * @param indexReqDtoMono indexReqDtoMono
     * @return Mono<Void>
     */
    @PostExchange("/updateIndex")
    Mono<Void> updateIndex(@RequestBody Mono<IndexReqDto> indexReqDtoMono);

    /**
     * 删除索引
     * 
     * @param indexReqDtoMono indexReqDtoMono
     * @return Mono<Void>
     */
    @PostExchange("/deleteIndex")
    Mono<Void> deleteIndex(@RequestBody Mono<IndexReqDto> indexReqDtoMono);

}
