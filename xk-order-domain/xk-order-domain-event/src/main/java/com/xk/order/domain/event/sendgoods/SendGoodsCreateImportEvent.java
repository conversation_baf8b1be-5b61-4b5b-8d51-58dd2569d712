package com.xk.order.domain.event.sendgoods;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.order.AbstractOrderDomainEvent;
import com.xk.order.enums.logistics.LogisticsOrderStatusEnum;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

import java.util.Date;
import java.util.Map;

/**
 * 发货
 */
@Getter
public class SendGoodsCreateImportEvent extends AbstractOrderDomainEvent {

    private final Long logisticsOrderId;

    private final String orderNo;

    private final LogisticsOrderTypeEnum logisticsOrderType;

    private final LogisticsOrderStatusEnum logisticsOrderStatus;

    private final String logisticsNo;

    private final String logisticsCorpName;

    private final Long fileTaskId;

    private final Boolean hasImport;

    private final String addressSite;

    private final String addressDetail;

    private final String goodsName;

    private final String goodsCount;

    private final Long userId;

    private final Date sendGoodsTime;

    private final Date createTime;

    @Builder
    public SendGoodsCreateImportEvent(@NonNull Long identifier, Map<String, Object> context,
                                      Long logisticsOrderId, String orderNo, LogisticsOrderTypeEnum logisticsOrderType,
                                      LogisticsOrderStatusEnum logisticsOrderStatus, String logisticsNo,
                                      String logisticsCorpName, Long fileTaskId, Boolean hasImport, String addressSite,
                                      String addressDetail, String goodsName, String goodsCount, Long userId,
                                      Date sendGoodsTime, Date createTime) {
        super(identifier, context);
        this.logisticsOrderId = logisticsOrderId;
        this.orderNo = orderNo;
        this.logisticsOrderType = logisticsOrderType;
        this.logisticsOrderStatus = logisticsOrderStatus;
        this.logisticsNo = logisticsNo;
        this.logisticsCorpName = logisticsCorpName;
        this.fileTaskId = fileTaskId;
        this.hasImport = hasImport;
        this.addressSite = addressSite;
        this.addressDetail = addressDetail;
        this.goodsName = goodsName;
        this.goodsCount = goodsCount;
        this.userId = userId;
        this.sendGoodsTime = sendGoodsTime;
        this.createTime = createTime;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
