package com.xk.acct.domain.repository.user;


import com.myco.mydata.domain.repository.I2Repository;
import com.xk.acct.domain.model.user.UserRoot;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
public interface UserRootRepository extends I2Repository<UserRoot> {
    /**
     * 修改用户包赔
     *
     * @param userRoot
     * @return
     */
    Mono<Void> updateUserInsurance(UserRoot userRoot);

    /**
     * 添加用户支付账户
     *
     * @param userRoot
     * @return
     */
    Mono<Void> saveUserPayAccount(UserRoot userRoot);

    /**
     * 修改用户支付账户
     *
     * @param userRoot
     * @return
     */
    Mono<Void> updateUserPayAccount(UserRoot userRoot);

    /**
     * 添加用户包赔修改记录
     *
     * @param userRoot
     * @return
     */
    Mono<Void> saveUserInsuranceEditLog(UserRoot userRoot);

    // 添加收藏浏览记录
    Mono<Void> addUserViewsCollectCache(UserRoot userRoot);

    // 删除收藏浏览记录
    Mono<Void> removeUserViewsCollectCache(UserRoot userRoot);

    // 删除首条记录 用于对集合长度有所限制 达到数量限制后 删除首条记录
    Mono<Void> removeUserViewsCollectCacheFirst(UserRoot userRoot);

    // 删除全部收藏浏览记录
    Mono<Void> deletedUserViewCollect(UserRoot userRoot);

    Mono<Void> updateUserSecurity(UserRoot userRoot);

    Mono<Void> saveUserSecurity(UserRoot userRoot);

    // 修改用户配置信息
    Mono<Void> updateUserConfig(UserRoot userRoot);
}
