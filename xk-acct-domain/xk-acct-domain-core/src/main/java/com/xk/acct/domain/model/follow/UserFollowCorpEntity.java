package com.xk.acct.domain.model.follow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.domain.model.common.CreateValObj;
import com.xk.acct.domain.model.common.UpdateValObj;
import com.xk.acct.domain.model.follow.id.UserFollowCorpIdentifier;
import lombok.*;

import java.util.Date;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserFollowCorpEntity implements Entity<UserFollowCorpIdentifier> {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 用户手机
     */
    private String userPhone;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registerTime;

    /**
     * 创建值对象
     */
    private CreateValObj createValObj;

    /**
     * 更新值对象
     */
    private UpdateValObj updateValObj;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer isDelete;

    @Override
    public @NonNull UserFollowCorpIdentifier getIdentifier() {
        return UserFollowCorpIdentifier.builder().userId(userId).corpInfoId(corpInfoId).build();
    }

    @Override
    public Validatable<UserFollowCorpIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
