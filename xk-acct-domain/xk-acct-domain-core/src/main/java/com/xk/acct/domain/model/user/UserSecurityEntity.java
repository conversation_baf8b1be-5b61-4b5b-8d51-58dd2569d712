package com.xk.acct.domain.model.user;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSecurityEntity implements Entity<LongIdentifier> {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录密码
     */
    private String loginPassword;

    /**
     * 加密KEY
     */
    private String loginKey;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}