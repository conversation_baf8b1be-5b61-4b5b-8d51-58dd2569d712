package com.xk.acct.domain.model.count.ordersource;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserSourceCountIdentifier implements Identifier<UserSourceCountIdentifier> {

    /**
     * 日期
     */
    private String day;

    /**
     * 自流量 百度
     */
    private String type;


    @Override
    public @NonNull UserSourceCountIdentifier getIdentifier() {
        return UserSourceCountIdentifier.builder().day(day).type(type).build();
    }
}
