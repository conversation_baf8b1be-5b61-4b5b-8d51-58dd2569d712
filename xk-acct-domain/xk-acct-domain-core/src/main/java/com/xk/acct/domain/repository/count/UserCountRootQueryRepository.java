package com.xk.acct.domain.repository.count;


import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.acct.domain.model.count.entity.UserCountEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;


/**
 * <AUTHOR>
 */
public interface UserCountRootQueryRepository extends IQueryRepository {

    Flux<UserCountEntity> queryDailyUserCount(Map<String, Object> params);

    Mono<UserCountEntity> queryDailyUserCountCache(UserCountEntity userCountEntity);

    Mono<UserCountEntity> queryUserCountDb(UserCountEntity userCountEntity);
}
