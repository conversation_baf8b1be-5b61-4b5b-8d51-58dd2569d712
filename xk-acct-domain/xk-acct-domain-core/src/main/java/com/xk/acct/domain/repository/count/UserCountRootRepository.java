package com.xk.acct.domain.repository.count;


import com.myco.mydata.domain.repository.I2Repository;
import com.xk.acct.domain.model.count.UserCountRoot;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
public interface UserCountRootRepository extends I2Repository<UserCountRoot> {

    Mono<Void> saveUserCount(UserCountRoot userCountRoot);

    Mono<Void> addUserCountCache(UserCountRoot userCountRoot);

    Mono<Void> removeUserCountCache(UserCountRoot userCountRoot);
}
