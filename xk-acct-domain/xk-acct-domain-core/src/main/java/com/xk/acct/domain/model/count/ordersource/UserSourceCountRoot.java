package com.xk.acct.domain.model.count.ordersource;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.Builder;
import lombok.Getter;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Getter
public class UserSourceCountRoot extends DomainRoot<UserSourceCountIdentifier> {

    private final UserSourceCountEntity userSourceCountEntity;


    @Builder
    public UserSourceCountRoot(UserSourceCountIdentifier identifier, UserSourceCountEntity userSourceCountEntity) {
        super(identifier);
        this.userSourceCountEntity = userSourceCountEntity;
    }


    @Override
    public Validatable<UserSourceCountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
