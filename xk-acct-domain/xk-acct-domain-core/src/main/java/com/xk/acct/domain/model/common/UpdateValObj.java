package com.xk.acct.domain.model.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateValObj {

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void setDefaultValue() {
        this.updateTime = new Date();
    }
}
