package com.xk.acct.domain.commons;

import java.util.Random;

public class UserPicRandon {

    private static final String[] imageUrls = {
            "/headPic/%E8%92%99%E7%89%88%202.png",
            "/headPic/%E8%92%99%E7%89%88%203.png",
            "/headPic/%E8%92%99%E7%89%88%204.png",
            "/headPic/%E8%92%99%E7%89%88%205.png",
            "/headPic/%E8%92%99%E7%89%88.png",
            "/headPic/%E8%92%99%E7%89%88%E5%A4%87%E4%BB%BD%202.png",
            "/headPic/%E8%92%99%E7%89%88%E5%A4%87%E4%BB%BD%203.png",
            "/headPic/%E8%92%99%E7%89%88%E5%A4%87%E4%BB%BD%204.png",
            "/headPic/%E8%92%99%E7%89%88%E5%A4%87%E4%BB%BD%205.png",
            "/headPic/%E8%92%99%E7%89%88%E5%A4%87%E4%BB%BD.png"
    };

    public static String getRandomImage() {
        if (imageUrls == null) {
            throw new IllegalArgumentException("图片列表不能为空");
        }
        Random random = new Random();
        int index = random.nextInt(imageUrls.length);
        return imageUrls[index];
    }

}
