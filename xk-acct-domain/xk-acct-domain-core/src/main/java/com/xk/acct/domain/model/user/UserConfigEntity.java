package com.xk.acct.domain.model.user;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 用户配置实体类
 * @date 2025/5/12 14:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserConfigEntity implements Entity<LongIdentifier> {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 配置名称
     */
    private String configKey;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 更新人ID
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;


    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }
}
