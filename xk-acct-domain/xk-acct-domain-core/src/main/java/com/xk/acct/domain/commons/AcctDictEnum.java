package com.xk.acct.domain.commons;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import com.myco.mydata.config.domain.model.cfg.DictObjectEnum;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AcctDictEnum implements DictObjectEnum {

    SYS_MANAGER_PHONE("15623456318"),

    ;

    private static final Map<String, AcctDictEnum> MAP;

    static {
        MAP = Arrays.stream(AcctDictEnum.values())
                .collect(Collectors.toMap(AcctDictEnum::name, enumValue -> enumValue));
    }

    private final String defaultValue;

    public static AcctDictEnum getEnum(String name) {
        return MAP.get(name);
    }

    @Override
    public String getName() {
        return name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return name();
    }
}
