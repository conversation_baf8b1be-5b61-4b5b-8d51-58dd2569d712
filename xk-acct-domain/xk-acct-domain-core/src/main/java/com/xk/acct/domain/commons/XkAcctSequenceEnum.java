package com.xk.acct.domain.commons;

import com.myco.mydata.domain.model.identifier.SequenceIdentifier;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum XkAcctSequenceEnum implements SequenceIdentifier {

    A_USER_DATA("a_user_data", "user_id", "AUserDataMapper"),
    A_LOGIN_LOG("a_login_log", "login_sn", "ALoginLogMapper"),
    A_USER_PAY_ACCOUNT("a_user_pay_account", "user_pay_account_id", "AUserPayAccountMapper"),
    A_USER_INSURANCE_EDIT_LOG("a_user_insurance_edit_log", "log_id", "AUserInsuranceEditLogMapper"),
    ;

    private final String table;
    @Getter
    private final String pk;
    @Getter
    private final String className;

    @Override
    public @NonNull String getName() {
        return this.name();
    }

    @Override
    public @NonNull String getIdentifier() {
        return this.name();
    }

    @Override
    @NonNull
    public String getTable() {
        return this.table;
    }

}
