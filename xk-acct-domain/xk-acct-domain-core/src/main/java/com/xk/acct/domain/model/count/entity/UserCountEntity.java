package com.xk.acct.domain.model.count.entity;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.enums.count.UserCountTypeEnum;
import com.xk.acct.domain.model.count.id.UserCountIdentifier;
import lombok.*;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserCountEntity implements Entity<UserCountIdentifier> {

    /**
     * 1注册 2登录
     */
    private UserCountTypeEnum type;

    /**
     * 日期
     */
    private String day;

    /**
     * 用户id集合
     */
    private List<Long> userIds;

    /**
     * 数量
     */
    private Long num;

    /**
     * 创建时间
     */
    private Date createTime;


    @Override
    public @NonNull UserCountIdentifier getIdentifier() {
        return UserCountIdentifier.builder().type(type.getCode()).day(day).build();
    }

    @Override
    public Validatable<UserCountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
