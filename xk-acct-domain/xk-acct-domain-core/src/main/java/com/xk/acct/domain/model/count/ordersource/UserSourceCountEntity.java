package com.xk.acct.domain.model.count.ordersource;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @CreateTime: 2024-07-22
 * @Description:
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSourceCountEntity implements Entity<UserSourceCountIdentifier> {

    /**
     * 日期
     */
    private String day;

    /**
     * 自流量 百度
     */
    private String type;

    /**
     * 数量
     */
    private Long num;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 下单用户数
     */
    private Long placeOrderUserNum;

    /**
     * 注册用户数
     */
    private Long registerUserNum;


    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    @Override
    public @NonNull UserSourceCountIdentifier getIdentifier() {
        return UserSourceCountIdentifier.builder().day(day).type(type).build();
    }

    @Override
    public Validatable<UserSourceCountIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
