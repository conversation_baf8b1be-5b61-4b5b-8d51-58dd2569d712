package com.xk.acct.domain.model.user;

import java.util.List;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

/**
 * @author: killer
 **/
@Getter
@Setter
public class UserRoot extends DomainRoot<LongIdentifier> {

    /**
     * 用户数据
     */
    private UserDataEntity userData;
    /**
     * 用户地址数据
     */
    private UserAddressEntity userAddressData;
    /**
     * 用户配置信息
     */
    private List<UserConfigEntity> userConfigEntities;
    /**
     * 用户注册信息
     */
    private UserRegisterEntity userRegister;
    /**
     * 用户安全信息
     */
    private UserSecurityEntity userSecurity;
    /**
     * 用户登录日志
     */
    private UserLoginLogEntity userLoginLog;
    /**
     * 用户包赔信息
     */
    private UserInsuranceEntity userInsurance;
    /**
     * 用户支付账号
     */
    private List<UserPayAccountEntity> userPayAccountEntities;
    private List<UserInsuranceEditLogEntity> userInsuranceEditLogEntities;
    private final List<UserCollectViewsEntity> userCollectViewsEntities;

    @Builder
    public UserRoot(@NonNull LongIdentifier identifier, UserDataEntity userData,
            UserAddressEntity userAddressData, UserRegisterEntity userRegister,
            UserSecurityEntity userSecurity, UserLoginLogEntity userLoginLog,
            UserInsuranceEntity userInsurance, List<UserPayAccountEntity> userPayAccountEntities,
            List<UserInsuranceEditLogEntity> userInsuranceEditLogEntities,
            List<UserCollectViewsEntity> userCollectViewsEntities,
            List<UserConfigEntity> userConfigEntities) {
        super(identifier);
        this.userData = userData;
        this.userAddressData = userAddressData;
        this.userRegister = userRegister;
        this.userSecurity = userSecurity;
        this.userLoginLog = userLoginLog;
        this.userInsurance = userInsurance;
        this.userPayAccountEntities = userPayAccountEntities;
        this.userInsuranceEditLogEntities = userInsuranceEditLogEntities;
        this.userCollectViewsEntities = userCollectViewsEntities;
        this.userConfigEntities = userConfigEntities;
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
