package com.xk.acct.domain.support;

import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.exception.wrapper.DomainWrapperThrowable;
import com.xk.acct.domain.commons.XkAcctDomainErrorEnum;

/**
 * 领域异常
 * 
 * @author: killer
 **/
public class XkAcctDomainException extends DomainWrapperThrowable {

    public XkAcctDomainException(XkAcctDomainErrorEnum exceptionIdentifier, Exception throwable) {
        super(exceptionIdentifier, throwable);
    }

    public XkAcctDomainException(XkAcctDomainErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }

    public XkAcctDomainException(SystemErrorEnum exceptionIdentifier) {
        super(exceptionIdentifier);
    }
}
