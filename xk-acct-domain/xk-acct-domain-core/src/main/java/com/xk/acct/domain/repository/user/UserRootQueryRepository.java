package com.xk.acct.domain.repository.user;


import java.util.Date;
import java.util.List;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.acct.domain.model.user.*;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
public interface UserRootQueryRepository extends IQueryRepository {

    /**
     * 根据 identifier 查询 UserDataEntity
     *
     * @param identifier identifier
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataById(LongIdentifier identifier);

    /**
     * 根据 identifier 查询 UserRegisterEntity
     *
     * @param identifier identifier
     * @return Mono<UserRegisterEntity>
     */
    Mono<UserRegisterEntity> getUserRegisterById(LongIdentifier identifier);

    /**
     * 根据 identifier 查询 UserSecurityEntity
     *
     * @param identifier identifier
     * @return Mono<UserSecurityEntity>
     */
    Mono<UserSecurityEntity> getUserSecurityById(LongIdentifier identifier);

    /**
     * 根据 identifier 查询 包赔
     *
     * @param identifier
     * @return
     */
    Mono<UserInsuranceEntity> getUserInsuranceById(LongIdentifier identifier);

    /**
     * 根据 identifier 查询
     *
     * @param identifier identifier
     * @return Mono<DictItemRoot>
     */
    Mono<UserRoot> getRoot(LongIdentifier identifier);

    /**
     * 根据 登录名 查询
     *
     * @param userData userData
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataByLoginName(UserDataEntity userData);

    /**
     * 根据 昵称 查询
     *
     * @param userData userData
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataByNickname(UserDataEntity userData);

    /**
     * 根据 邮箱 查询
     *
     * @param userData userData
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataByEmail(UserDataEntity userData);

    /**
     * 根据mobile查询
     *
     * @param userData userData
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataByMobile(UserDataEntity userData);

    /**
     * 根据用户的id[]查询用户数据.
     *
     * @param ids ids
     * @return Flux<UserDataEntity>
     */
    Flux<UserDataEntity> findUserDataByIds(List<LongIdentifier> ids);

    /**
     * 搜索用户数据
     *
     * @param pagination pagination
     * @return Flux<UserDataEntity>
     */
    Flux<UserDataEntity> searchUserData(Pagination pagination);

    /**
     * 搜索用户收货地址
     *
     * @param pagination pagination
     * @return Flux<UserDataEntity>
     */
    Flux<UserAddressEntity> searchUserAddress(Pagination pagination);

    /**
     * 根据注册时间 渠道查询
     *
     * @param dateStart dateStart
     * @param dateEnd dateEnd
     * @param channelId channelId
     * @return Flux<UserRegisterEntity>
     */
    Flux<UserRegisterEntity> findUserRegisterByChannelDate(Date dateStart, Date dateEnd,
            String channelId);


    /**
     * 根据 身份证号 查询
     *
     * @param userData userData
     * @return Mono<UserDataEntity>
     */
    Mono<UserDataEntity> getUserDataByIdCard(UserDataEntity userData);

    /**
     * 根据用户id查询
     * 
     * @param entity entity
     * @return Flux<UserPayAccountEntity>
     */
    Flux<UserPayAccountEntity> findUserPayAccountByUserId(UserPayAccountEntity entity);

    /**
     * 根据用户id和类型查询账号数量
     * 
     * @param entity entity
     * @return Mono<Integer>
     */
    Mono<Integer> getUserPayAccountByUserIdAndType(UserPayAccountEntity entity);

    /**
     * 根据id查询用户支付账户
     * 
     * @param entity entity
     * @return Mono<UserPayAccountEntity>
     */
    Mono<UserPayAccountEntity> getUserPayAccountById(UserPayAccountEntity entity);

    /**
     * 根据用户id查询用户包赔修改记录
     * 
     * @param entity entity
     * @return Flux<UserInsuranceEditLogEntity>
     */
    Flux<UserInsuranceEditLogEntity> findUserInsuranceEditLogByUserId(
            UserInsuranceEditLogEntity entity);

    /**
     * 根据用户id查询用户包赔修改记录分页列表
     * 
     * @param pager pager
     * @return Flux<UserInsuranceEditLogEntity>
     */
    Flux<UserInsuranceEditLogEntity> findUserInsuranceEditLogPageByUserId(Pagination pager);

    /**
     * 查询用户浏览收藏记录
     * 
     * @param pagination pagination
     * @return Flux<UserCollectViewsEntity>
     */
    Flux<UserCollectViewsEntity> findCollectViewPage(Pagination pagination);

    /**
     * 查询用户浏览收藏记录数量
     * 
     * @param entity entity
     * @return Mono<Long>
     */
    Mono<Long> queryCollectViewCount(UserCollectViewsEntity entity);

    /**
     * 验证用户是否已收藏 或 已浏览
     * 
     * @param entity entity
     * @return Mono<Boolean>
     */
    Mono<Boolean> checkUserViewCollect(UserCollectViewsEntity entity);

    /**
     * 查询用户全部浏览收藏记录
     * 
     * @param entity entity
     * @return Flux<UserCollectViewsEntity>
     */
    Flux<UserCollectViewsEntity> findCollectViewByUserId(UserCollectViewsEntity entity);

    /**
     * 根据用户类型查询用户
     * 
     * @param entity entity
     * @return Flux<UserDataEntity>
     */
    Flux<UserDataEntity> findUserByUserType(UserDataEntity entity);

    /**
     * 根据 identifier 查询 UserLoginLogEntity
     * 
     * @param identifier identifier
     * @return Mono<UserLoginLogEntity>
     */
    Mono<UserLoginLogEntity> getUserLoginLogById(LongIdentifier identifier);

    /**
     * 根据用户ID和默认值 查询默认地址
     */
    Mono<UserAddressEntity> findDefaultAddressByUserId(LongIdentifier longIdentifier);

    /**
     * 根据地址id查询地址
     */
    Mono<UserAddressEntity> findAddressById(LongIdentifier longIdentifier);

    /**
     * 根据用户ID和默认值 查询默认地址
     */
    Flux<UserConfigEntity> findUserConfigByUserId(LongIdentifier longIdentifier);

    /**
     * 根据 identifier 查询 UserRoot
     *
     * @param identifierMono identifierMono
     * @return Mono<UserRoot>
     */
    Mono<UserRoot> getUserObjectById(Mono<LongIdentifier> identifierMono);
}
