package com.xk.acct.domain.service.user.impl;

import static com.xk.acct.enums.user.IdentifyBindStatusEnum.BIND;

import java.util.List;
import java.util.function.Function;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.commons.util.JodaTimeUtil;
import com.myco.mydata.commons.util.MD5Util;
import com.myco.mydata.commons.util.RandomHelper;
import com.myco.mydata.commons.util.StringHelper;
import com.myco.mydata.config.domain.service.cfg.DictObjectDomainService;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.identifier.IdentifierGenerateEnum;
import com.myco.mydata.domain.model.identifier.IdentifierRoot;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.myco.mydata.domain.service.identifier.IdentifierGenerateService;
import com.xk.acct.domain.commons.IDCardValidatorUtil;
import com.xk.acct.domain.commons.RandomName;
import com.xk.acct.domain.commons.XkAcctDomainErrorEnum;
import com.xk.acct.domain.commons.XkAcctSequenceEnum;
import com.xk.acct.domain.model.user.*;
import com.xk.acct.domain.repository.user.UserRootQueryRepository;
import com.xk.acct.domain.repository.user.UserRootRepository;
import com.xk.acct.domain.service.user.UserRootDomainService;
import com.xk.acct.domain.support.XkAcctDomainException;
import com.xk.acct.enums.user.*;
import com.xk.domain.commons.user.UserConfigTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class UserRootDomainServiceImpl implements UserRootDomainService {

    private final UserRootQueryRepository userRootQueryRepository;

    private final UserRootRepository userRootRepository;

    private final IdentifierGenerateService identifierGenerateService;

    private final DictObjectDomainService dictObjectService;

    /**
     * 获取 UserDataEntity
     *
     * @param userRootMono userRootMono
     * @return Mono<UserDataEntity>
     */
    private static Mono<UserDataEntity> initUserDataEntityMono(Mono<UserRoot> userRootMono) {
        // 设置 UserDataEntity 的属性
        return userRootMono.flatMap(userRoot -> {
            Long userId = userRoot.getIdentifier().id();
            return Mono.justOrEmpty(userRoot.getUserData()).flatMap(user -> {
                // user_id设置
                if (user.getUserId() == null) {
                    user.setUserId(userId);
                }
                user.setCreateTime(JodaTimeUtil.getCurrentDateTime());
                // login_name设置
                if (StringHelper.isEmpty(user.getLoginName())) {
                    String loginName =
                            user.getAccountPlatformId() + StringHelper.UNDERLINE + user.getUserId();
                    user.setLoginName(loginName.toLowerCase());
                }
                // 邮箱绑定设置
                if (StringHelper.isNotEmpty(user.getEmail()) && user.getEmailStatus() == null) {
                    user.setEmailStatus(IdentifyBindStatusEnum.UNBIND);
                }
                // 手机绑定设置
                if (StringHelper.isNotEmpty(user.getMobile()) && user.getMobileStatus() == null) {
                    user.setMobileStatus(IdentifyBindStatusEnum.UNBIND);
                }
                // 无昵称的情况下自动生成昵称,生成规则：用户+user_id
                if (user.getNickname() == null
                        || StringHelper.EMPTY.equals(user.getNickname().trim())) {
                    String randomName = RandomName.randomName(true, 4);
                    user.setNickname(randomName + user.getUserId());
                }
                user.setBaseAuthStatus(BaseAuthStatusEnum.DISABLED);
                user.setFaceAuthStatus(FaceAuthStatusEnum.DISABLED);
                return Mono.just(user);
            });
        });
    }

    @Override
    public Mono<UserDataEntity> getByIdentificationNumber(Mono<String> identificationNumberMono) {
        return identificationNumberMono.flatMap(idn -> {
            Mono<UserDataEntity> getMobile =
                    this.userRootQueryRepository.getUserDataByMobile(UserDataEntity.builder()
                            .mobile(idn).mobileStatus(IdentifyBindStatusEnum.BIND).build());
            Mono<UserDataEntity> getLoginName = userRootQueryRepository
                    .getUserDataByLoginName(UserDataEntity.builder().loginName(idn).build());
            Mono<UserDataEntity> getEmail =
                    userRootQueryRepository.getUserDataByEmail(UserDataEntity.builder().email(idn)
                            .emailStatus(IdentifyBindStatusEnum.BIND).build());
            return getMobile.flatMap(Mono::just).switchIfEmpty(getLoginName)
                    .switchIfEmpty(getEmail);
        });
    }

    @Override
    public Mono<Boolean> checkIdentificationNumber(Mono<String> identificationNumberMono) {
        return identificationNumberMono.flatMap(idn -> {
            Mono<Boolean> checkMobile =
                    this.userRootQueryRepository
                            .getUserDataByMobile(UserDataEntity.builder().mobile(idn)
                                    .mobileStatus(IdentifyBindStatusEnum.BIND).build())
                            .hasElement();
            Mono<Boolean> checkLoginName = userRootQueryRepository
                    .getUserDataByLoginName(UserDataEntity.builder().loginName(idn).build())
                    .hasElement();
            Mono<Boolean> checkEmail =
                    userRootQueryRepository.getUserDataByEmail(UserDataEntity.builder().email(idn)
                            .emailStatus(IdentifyBindStatusEnum.BIND).build()).hasElement();
            return checkMobile.flatMap(result -> {
                if (Boolean.TRUE.equals(result)) {
                    return Mono.just(Boolean.FALSE);
                }
                return checkLoginName.flatMap(loginNameResult -> {
                    if (Boolean.TRUE.equals(loginNameResult)) {
                        return Mono.just(Boolean.FALSE);
                    }
                    return checkEmail.flatMap(emailResult -> Mono.just(!emailResult));
                });
            });
        }).switchIfEmpty(Mono.just(Boolean.FALSE));
    }

    @Override
    public Mono<Boolean> checkIdentificationNumberV2(RegisterTypeEnum registerType,
            String identificationNumber) {

        Function<String, Mono<Boolean>> checkMobile = identification -> this.userRootQueryRepository
                .getUserDataByMobile(UserDataEntity.builder().mobile(identification)
                        .mobileStatus(IdentifyBindStatusEnum.BIND).build())
                .hasElement();

        Function<String, Mono<Boolean>> checkEmail = identification -> this.userRootQueryRepository
                .getUserDataByEmail(UserDataEntity.builder().email(identification)
                        .emailStatus(IdentifyBindStatusEnum.BIND).build())
                .hasElement();

        return switch (registerType) {
            case MOBILE -> checkMobile.apply(identificationNumber).map(result -> !result);
            case EMAIL -> checkEmail.apply(identificationNumber).map(emailResult -> !emailResult);
            default -> Mono.just(Boolean.FALSE);
        };
    }

    @Override
    public Mono<Boolean> checkNickname(Mono<String> nicknameMono) {
        return nicknameMono.flatMap(name -> {
            if (name.startsWith("用户_")) {
                return Mono.just(Boolean.FALSE);
            }
            return this.userRootQueryRepository
                    .getUserDataByNickname(UserDataEntity.builder().nickname(name)
                            .mobileStatus(IdentifyBindStatusEnum.BIND).build())
                    .hasElement().map(result -> !result);
        }).switchIfEmpty(Mono.just(Boolean.TRUE));
    }

    @Override
    public Mono<Void> checkUserTypeAndPlatformType(PlatformTypeEnum platformType,
            UserTypeEnum userType) {
        if ((PlatformTypeEnum.PC_BOSS_OMS.equals(platformType)
                && !UserTypeEnum.MANAGER.equals(userType))
                || (PlatformTypeEnum.PC_COMPANY_OMS.equals(platformType)
                        && !UserTypeEnum.MERCHANT_KAS.equals(userType))) {
            return Mono.error(new XkAcctDomainException(
                    XkAcctDomainErrorEnum.ACCOUNT_LOGIN_FIND_USER_FAILURE));
        }
        return Mono.empty();
    }

    @Override
    public Mono<Void> checkPassword(LongIdentifier identifier, String loginPassword) {
        if (StringHelper.isEmpty(loginPassword)) {
            return Mono
                    .error(new XkAcctDomainException(XkAcctDomainErrorEnum.ACCOUNT_PASSWORD_ERROR));
        }
        return userRootQueryRepository.getUserSecurityById(identifier)
                .switchIfEmpty(Mono.error(new XkAcctDomainException(
                        XkAcctDomainErrorEnum.ACCOUNT_NOT_PASSWORD_ERROR)))
                .flatMap(userSecurity -> {
                    String password = MD5Util.EncodePasswordByLoginKey(loginPassword,
                            userSecurity.getLoginKey());
                    if (password == null || !password.equals(userSecurity.getLoginPassword())) {
                        return Mono.error(new XkAcctDomainException(
                                XkAcctDomainErrorEnum.ACCOUNT_PASSWORD_ERROR));
                    }
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Boolean> isValidateAuthCode(Mono<UserLoginLogEntity> userLoginLogEntityMono) {
        return userLoginLogEntityMono.flatMap(userLoginLogEntity -> {
            if (userLoginLogEntity.getLoginSn() == null) {
                return Mono.just(Boolean.TRUE);
            }
            return userRootQueryRepository.getUserLoginLogById(userLoginLogEntity.getIdentifier())
                    .flatMap(lastUserLoginLogEntity -> lastUserLoginLogEntity
                            .compareWithLast(userLoginLogEntityMono));
        });
    }

    /**
     * 校验用户身份证号
     *
     * @param idCardMono idCard
     * @return Mono<Boolean>
     */
    @Override
    public Mono<Boolean> checkIdCard(Mono<String> idCardMono) {
        return idCardMono
                .flatMap(idCard -> this.userRootQueryRepository
                        .getUserDataByIdCard(UserDataEntity.builder().idCard(idCard).build())
                        .hasElement().map(result -> !result))
                .switchIfEmpty(Mono.just(Boolean.TRUE));
    }

    /**
     * 校验用户当前账户类型是否存在
     *
     * @param userPayAccountEntityMono userPayAccountEntityMono
     * @return Mono<Boolean>
     */
    @Override
    public Mono<Boolean> checkUserPayAccountByUserIdAndType(
            Mono<UserPayAccountEntity> userPayAccountEntityMono) {
        return userPayAccountEntityMono.flatMap(entity -> userRootQueryRepository
                .getUserPayAccountByUserIdAndType(entity).map(num -> num <= 0));
    }

    @Override
    public Mono<Boolean> checkMobileBindStatus(Mono<UserDataEntity> entityMono,
            Mono<String> mobileMono) {
        return entityMono.flatMap(entity -> mobileMono.flatMap(mobile -> {
            // 如果未绑定或手机号为空，直接返回true
            if (!BIND.equals(entity.getMobileStatus()) || !StringUtils.hasText(mobile)) {
                return Mono.just(true);
            }

            // 绑定状态下，检查手机号是否一致
            return Mono.just(StringHelper.bothEmptyOrEquals(entity.getMobile(), mobile));
        }));
    }

    @Override
    public Mono<Boolean> checkEmailBindStatus(Mono<UserDataEntity> entityMono,
            Mono<String> emailMono) {
        return entityMono.flatMap(entity -> emailMono.flatMap(email -> {
            // 如果未绑定或邮箱为空，直接返回true
            if (!BIND.equals(entity.getEmailStatus()) || !StringUtils.hasText(email)) {
                return Mono.just(true);
            }

            // 绑定状态下，检查邮箱是否一致
            return Mono.just(StringHelper.bothEmptyOrEquals(entity.getEmail(), email));
        }));
    }

    @Override
    public Mono<Long> generateUserId() {
        return Mono.just(identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(XkAcctSequenceEnum.A_USER_DATA)
                        .type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);
    }

    @Override
    public Mono<Long> generateUserLoginLogId() {
        return Mono.just(identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(XkAcctSequenceEnum.A_LOGIN_LOG)
                        .type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);
    }

    /**
     * 生成用户支付账户ID
     *
     * @return Mono<Long>
     */
    @Override
    public Mono<Long> generateUserPayAccountId() {
        return Mono.just(identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(XkAcctSequenceEnum.A_USER_PAY_ACCOUNT)
                        .type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);

    }

    /**
     * 生成用户包赔修改记录ID
     *
     * @return Mono<Long>
     */
    @Override
    public Mono<Long> generateUserInsuranceEditLogId() {
        return Mono.just(identifierGenerateService.generateIdentifier(
                IdentifierRoot.builder().identifier(XkAcctSequenceEnum.A_USER_INSURANCE_EDIT_LOG)
                        .type(IdentifierGenerateEnum.CACHE).build()))
                .cast(Long.class);
    }

    @Override
    public Mono<Void> saveUserRoot(Mono<UserRoot> userRootMono) {
        // 设置 UserDataEntity 属性
        Mono<UserDataEntity> userDataMono = initUserDataEntityMono(userRootMono);
        // 设置 UserSecurityEntity 属性
        Mono<UserSecurityEntity> securityEntityMono =
                initUserSecurityEntityMono(userRootMono).defaultIfEmpty(new UserSecurityEntity());
        // 设置 UserRegisterEntity
        Mono<UserRegisterEntity> registerEntityMono =
                initUserRegisterEntityMono(userRootMono).defaultIfEmpty(new UserRegisterEntity());
        // 设置 userConfigEntity
        Flux<UserConfigEntity> userConfigEntityFlux =
                initUserConfigEntityFlux(userRootMono).defaultIfEmpty(new UserConfigEntity());
        Mono<List<UserConfigEntity>> fluxAsMono = userConfigEntityFlux.collectList();
        // 保存
        return userRootMono.flatMap(userRoot -> Mono
                .zip(userDataMono, securityEntityMono, registerEntityMono, fluxAsMono)
                .flatMap(tuple -> {
                    UserDataEntity userDataEntity = tuple.getT1();
                    userRoot.setUserData(userDataEntity);

                    UserSecurityEntity userSecurityEntity = tuple.getT2();
                    if (userSecurityEntity.getUserId() != null) {
                        userRoot.setUserSecurity(userSecurityEntity);
                    }
                    UserRegisterEntity registerEntity = tuple.getT3();
                    if (registerEntity.getUserId() != null) {
                        userRoot.setUserRegister(registerEntity);
                    }
                    UserInsuranceEntity entity = new UserInsuranceEntity();
                    entity.setUserId(userDataEntity.getUserId());
                    userRoot.setUserInsurance(entity);
                    // 添加用户配置参数
                    List<UserConfigEntity> userConfigEntities = tuple.getT4();
                    userRoot.setUserConfigEntities(userConfigEntities);
                    return this.userRootRepository.save(userRoot);
                }));
    }

    /**
     * 修改用户数据
     * 
     * @param userRootMono userRootMono
     * @return Mono<Void>
     */
    @Override
    public Mono<Void> updateUserRoot(Mono<UserRoot> userRootMono) {
        // 设置 UserDataEntity 属性
        Mono<UserDataEntity> userDataMono =
                initUserDataEntityMono(userRootMono).defaultIfEmpty(new UserDataEntity());
        // 设置 UserSecurityEntity 属性
        Mono<UserSecurityEntity> securityEntityMono =
                initUserSecurityEntityMono(userRootMono).defaultIfEmpty(new UserSecurityEntity());
        // 设置 UserRegisterEntity
        Mono<UserRegisterEntity> registerEntityMono =
                initUserRegisterEntityMono(userRootMono).defaultIfEmpty(new UserRegisterEntity());
        // 保存
        return userRootMono.flatMap(userRoot -> Mono
                .zip(userDataMono, securityEntityMono, registerEntityMono).flatMap(tuple -> {
                    UserDataEntity userDataEntity = tuple.getT1();
                    if (userDataEntity.getUserId() != null) {
                        userRoot.setUserData(userDataEntity);
                    }

                    UserSecurityEntity userSecurityEntity = tuple.getT2();
                    if (userSecurityEntity.getUserId() != null) {
                        userRoot.setUserSecurity(userSecurityEntity);
                    }
                    UserRegisterEntity registerEntity = tuple.getT3();
                    if (registerEntity.getUserId() != null) {
                        userRoot.setUserRegister(registerEntity);
                    }
                    return this.userRootRepository.update(userRoot);
                }));
    }

    @Override
    public Mono<UserRoot> getUserRootById(Mono<LongIdentifier> userRootIdMono) {
        return userRootIdMono.flatMap(userRootQueryRepository::getRoot);

    }

    @Override
    public Mono<UserRoot> getUserObject(Mono<LongIdentifier> longIdentifierMono) {
        return userRootQueryRepository.getUserObjectById(longIdentifierMono);
    }

    /**
     * 获取 UserRegisterEntity
     *
     * @param userRootMono userRootMono
     * @return Mono<UserRegisterEntity>
     */
    private Mono<UserRegisterEntity> initUserRegisterEntityMono(Mono<UserRoot> userRootMono) {
        return userRootMono.flatMap(userRoot -> {
            Long userId = userRoot.getIdentifier().id();
            return Mono.justOrEmpty(userRoot.getUserRegister())
                    .flatMap(register -> this.userRootQueryRepository
                            .getUserRegisterById(LongIdentifier.builder().id(userId).build())
                            .hasElement().flatMap(exists -> {
                                if (Boolean.FALSE.equals(exists)) {
                                    if (register.getUserId() == null) {
                                        register.setUserId(userId);
                                    }
                                    if (register.getVisitorUserId() == null) {
                                        register.setVisitorUserId(-1L);
                                    }
                                    if (register.getRegisterType() == null) {
                                        register.setRegisterType(RegisterTypeEnum.ACCOUNT);
                                    }
                                    register.setRegisterDate(JodaTimeUtil.getCurrentDateTime());
                                    if (register.getChannelId() == null) {
                                        register.setChannelId(UserRegisterChannelEnum.OFFICIAL);
                                    }
                                    return Mono.just(register);
                                } else {
                                    return Mono.empty();
                                }
                            }));
        });
    }

    /**
     * 获取 UserConfigEntity
     *
     * @param userRootMono userRootMono
     * @return Mono<UserConfigEntity>
     */
    private Flux<UserConfigEntity> initUserConfigEntityFlux(Mono<UserRoot> userRootMono) {
        return userRootMono.flatMapMany(userRoot -> {
            Long userId = userRoot.getIdentifier().id();

            // 获取两个配置值的 Mono
            Mono<String> platformValueMono =
                    dictObjectService.getSystemConfigValue(UserConfigTypeEnum.PLATFORM_NOTIFY);
            Mono<String> activityValueMono =
                    dictObjectService.getSystemConfigValue(UserConfigTypeEnum.ACTIVITY_NOTIFY);
            Mono<String> transactionValueMono =
                    dictObjectService.getSystemConfigValue(UserConfigTypeEnum.TRANSACTION_NOTIFY);
            Mono<String> orderValueMono =
                    dictObjectService.getSystemConfigValue(UserConfigTypeEnum.ORDER_NOTIFY);

            // 使用 zip 合并两并创建 Flux
            return Mono
                    .zip(platformValueMono, activityValueMono, transactionValueMono, orderValueMono)
                    .flatMapMany(tuple -> {
                        UserConfigEntity platformEntity = UserConfigEntity.builder().userId(userId)
                                .configKey(UserConfigTypeEnum.PLATFORM_NOTIFY.getCode())
                                .configValue(tuple.getT1()).updateId(userId).build();

                        UserConfigEntity activityEntity = UserConfigEntity.builder().userId(userId)
                                .configKey(UserConfigTypeEnum.ACTIVITY_NOTIFY.getCode())
                                .configValue(tuple.getT2()).updateId(userId).build();

                        UserConfigEntity transactionEntity =
                                UserConfigEntity.builder().userId(userId)
                                        .configKey(UserConfigTypeEnum.TRANSACTION_NOTIFY.getCode())
                                        .configValue(tuple.getT3()).updateId(userId).build();

                        UserConfigEntity orderEntity = UserConfigEntity.builder().userId(userId)
                                .configKey(UserConfigTypeEnum.ORDER_NOTIFY.getCode())
                                .configValue(tuple.getT4()).updateId(userId).build();
                        return Flux.just(platformEntity, activityEntity, transactionEntity,
                                orderEntity);
                    });
        });
    }


    /**
     * @param userRootMono userRootMono
     * @return Mono<UserSecurityEntity>
     */
    private Mono<UserSecurityEntity> initUserSecurityEntityMono(Mono<UserRoot> userRootMono) {
        return userRootMono.flatMap(userRoot -> {
            Long userId = userRoot.getIdentifier().id();
            return Mono.justOrEmpty(userRoot.getUserSecurity())
                    .flatMap(security -> this.userRootQueryRepository
                            .getUserSecurityById(LongIdentifier.builder().id(userId).build())
                            .hasElement().flatMap(exists -> {
                                if (Boolean.FALSE.equals(exists)) {
                                    // 未随机 MD5 干扰码
                                    if (security.getUserId() == null) {
                                        security.setUserId(userId);
                                    }
                                } else {
                                    // 未随机 MD5 干扰码
                                    security.setUserId(userId);
                                }
                                security.setLoginKey(RandomHelper.genCodeWithCharsAndNumbers());
                                if (StringHelper.isNotEmpty(security.getLoginPassword())) {
                                    security.setLoginPassword(MD5Util.EncodePasswordByLoginKey(
                                            security.getLoginPassword(), security.getLoginKey()));
                                }
                                return Mono.just(security);
                            }));
        });
    }

    /**
     * 根据id集合获取用户数据
     *
     * @param userIdsMono userIdsMono
     * @return Mono<UserRoot>
     */
    @Override
    public Flux<UserDataEntity> getUserByIds(Mono<List<LongIdentifier>> userIdsMono) {
        return userIdsMono.flatMapMany(userIds -> Flux.fromIterable(userIds)
                .flatMap(userRootQueryRepository::getUserDataById));
    }

    /**
     * 根据用户id校验是否实名认证
     * 
     * @param identifierMono identifierMono
     * @return Mono<Boolean>
     */
    @Override
    public Mono<Boolean> checkUserAuthByUserId(Mono<LongIdentifier> identifierMono) {
        return identifierMono
                .flatMap(identifier -> userRootQueryRepository.getUserDataById(identifier)
                        .map(userDataEntity -> BaseAuthStatusEnum.ENABLE
                                .equals(userDataEntity.getBaseAuthStatus())))
                .switchIfEmpty(Mono.just(Boolean.FALSE));
    }

    /**
     * 校验用户是否填写包赔信息
     *
     * @param identifierMono identifierMono
     * @return Mono<Boolean>
     */
    @Override
    public Mono<Boolean> checkUserInsuranceByUserId(Mono<LongIdentifier> identifierMono) {
        return identifierMono
                .flatMap(identifier -> userRootQueryRepository.getUserInsuranceById(identifier)
                        .map(userInsuranceEntity -> StringUtils
                                .hasText(userInsuranceEntity.getAddress())))
                .switchIfEmpty(Mono.just(Boolean.FALSE));
    }

    @Override
    public Mono<Boolean> checkIdCardLegal(Mono<String> idCardMono) {
        return idCardMono.map(IDCardValidatorUtil::isValidIDCard);
    }

    @Override
    public Mono<Long> queryUserViewCollectCount(Mono<UserRoot> userRootMono) {
        return userRootMono.flatMap(userRoot -> userRootQueryRepository
                .queryCollectViewCount(userRoot.getUserCollectViewsEntities().getFirst()));
    }

    @Override
    public Mono<Boolean> checkUserViewCollect(Mono<UserRoot> userRootMono) {
        return userRootMono.flatMap(userRoot -> userRootQueryRepository
                .checkUserViewCollect(userRoot.getUserCollectViewsEntities().getFirst()));
    }


    @Override
    public Mono<Void> updateUserSecurity(Mono<UserRoot> userRootMono) {

        // 保存
        return userRootMono.flatMap(userRoot -> initUserSecurityEntityMono(userRootMono)
                .defaultIfEmpty(new UserSecurityEntity()).flatMap(userSecurityEntity -> {
                    if (userSecurityEntity.getUserId() != null) {
                        userRoot.setUserSecurity(userSecurityEntity);
                    }

                    return userRootQueryRepository
                            .getUserSecurityById(LongIdentifier.builder()
                                    .id(userRoot.getUserSecurity().getUserId()).build())
                            .hasElement().flatMap(hasElement -> {
                                // 修改
                                if (Boolean.TRUE.equals(hasElement)) {
                                    return this.userRootRepository.updateUserSecurity(userRoot);

                                }
                                // 添加
                                return this.userRootRepository.saveUserSecurity(userRoot);
                            });
                }));
    }
}
