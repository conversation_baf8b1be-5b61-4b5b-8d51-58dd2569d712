package com.xk.acct.domain.repository.follow;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.I2Repository;
import com.xk.acct.domain.model.follow.UserFollowCorpEntity;
import com.xk.acct.domain.model.follow.UserFollowCorpRoot;
import com.xk.acct.domain.model.user.UserAddressEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface UserFollowCorpRepository extends I2Repository<UserFollowCorpRoot> {

    Mono<Void> saveCache(UserFollowCorpRoot root);

    Mono<Void> removeCache(UserFollowCorpRoot root);

    Flux<UserFollowCorpEntity> queryCache();

}
