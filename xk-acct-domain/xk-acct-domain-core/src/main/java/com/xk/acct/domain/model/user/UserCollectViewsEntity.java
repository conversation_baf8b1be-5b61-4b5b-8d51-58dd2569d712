package com.xk.acct.domain.model.user;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.enums.user.UserViewCollectBusiTypeEnum;
import com.xk.acct.enums.user.UserViewCollectTypeEnum;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCollectViewsEntity implements Entity<LongIdentifier> {

    private Long userId;

    private UserViewCollectBusiTypeEnum busiType;

    private UserViewCollectTypeEnum type;

    private String busiId;

    private Date createTime;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}