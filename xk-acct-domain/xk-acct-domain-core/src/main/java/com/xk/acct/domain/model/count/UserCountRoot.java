package com.xk.acct.domain.model.count;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.domain.model.count.entity.UserCountEntity;
import lombok.Builder;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@Getter
public class UserCountRoot extends DomainRoot<LongIdentifier> {

    private final UserCountEntity userCountEntity;


    @Builder
    public UserCountRoot(LongIdentifier identifier, UserCountEntity userCountEntity) {
        super(identifier);

        this.userCountEntity = userCountEntity;
    }


    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }

}
