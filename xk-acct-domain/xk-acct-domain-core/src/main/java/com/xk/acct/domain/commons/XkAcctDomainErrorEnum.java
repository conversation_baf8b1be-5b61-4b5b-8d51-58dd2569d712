package com.xk.acct.domain.commons;


import com.myco.mydata.domain.model.exception.DefaultExceptionType;
import com.myco.mydata.domain.model.exception.ExceptionIdentifier;
import com.myco.mydata.domain.model.exception.ExceptionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;

/**
 * domain相关错误码
 * 定义错误码区间 11000-11999
 * @author: killer
 **/
@Getter
@AllArgsConstructor
public enum XkAcctDomainErrorEnum implements ExceptionIdentifier {
    DOMAIN_ERROR(11000, "domain错误"),
    ACCOUNT_LOGIN_FIND_USER_FAILURE(11001, "账户不存在或不可用"),
    ACCOUNT_NOT_PASSWORD_ERROR(11004, "没有设置密码"),
    ACCOUNT_PASSWORD_ERROR(11005, "用户名或密码错误"),
    ACCOUNT_LOGIN_USER_STATUS(11006, "账户状态异常"),



    ;

    private final Integer code;

    private final String desc;


    @Override
    public @NonNull Integer getIdentifier() {
        return code;
    }

    @Override
    public @NonNull String getDefaultMessage() {
        return desc;
    }

    @Override
    public @NonNull ExceptionType getExceptionType() {
        return DefaultExceptionType.COMMONS_ERROR;
    }

    @Override
    public @NonNull String getMessageCode() {
        return String.valueOf(code);
    }
}
