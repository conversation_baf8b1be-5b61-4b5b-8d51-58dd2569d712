package com.xk.acct.domain.model.follow;

import com.myco.mydata.domain.model.DomainRoot;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.domain.model.follow.id.UserFollowCorpIdentifier;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class UserFollowCorpRoot extends DomainRoot<UserFollowCorpIdentifier> {

    private final UserFollowCorpEntity userFollowCorpEntity;

    @Builder
    public UserFollowCorpRoot(UserFollowCorpIdentifier identifier, UserFollowCorpEntity userFollowCorpEntity) {
        super(identifier);
        this.userFollowCorpEntity = userFollowCorpEntity;
    }


    @Override
    public Validatable<UserFollowCorpIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
