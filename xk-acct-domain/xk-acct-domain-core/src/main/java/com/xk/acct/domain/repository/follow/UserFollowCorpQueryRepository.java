package com.xk.acct.domain.repository.follow;

import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.acct.domain.model.follow.UserFollowCorpEntity;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface UserFollowCorpQueryRepository extends IQueryRepository {

    Flux<UserFollowCorpEntity> searchUserFollowCorp(Pagination pagination);

    Mono<Long> queryCorpFollowNumberCache(Long corpInfoId);

    Flux<UserFollowCorpEntity> selectByUserId(Long userId);
}
