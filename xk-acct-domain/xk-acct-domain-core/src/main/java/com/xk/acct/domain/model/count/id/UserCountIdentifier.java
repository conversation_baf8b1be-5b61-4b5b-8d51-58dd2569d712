package com.xk.acct.domain.model.count.id;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;


/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCountIdentifier implements Identifier<UserCountIdentifier> {

    /**
     * 日期
     */
    private String day;

    /**
     * 类型
     */
    private Integer type;


    @Override
    public @NonNull UserCountIdentifier getIdentifier() {
        return UserCountIdentifier.builder().type(type).day(day).build();
    }
}
