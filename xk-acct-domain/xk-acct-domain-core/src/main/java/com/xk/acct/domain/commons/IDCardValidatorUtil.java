package com.xk.acct.domain.commons;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class IDCardValidatorUtil {

    // 加权因子
    private static final int[] WEIGHTS = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

    // 校验码
    private static final char[] CHECK_CODES = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    // 身份证号正则
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("\\d{17}[\\dX]");

    /**
     * 校验大陆身份证号是否合法
     *
     * @param idCard 身份证号
     * @return 如果合法返回true，否则返回false
     */
    public static boolean isValidIDCard(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }

        // 校验格式
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return false;
        }

        // 校验出生日期是否合法
        if (!isValidBirthDate(idCard.substring(6, 14))) {
            return false;
        }

        // 校验校验码
        return isValidCheckCode(idCard);
    }

    /**
     * 校验身份证号中的出生日期是否合法
     *
     * @param birthDateStr 出生日期部分的字符串
     * @return 如果合法返回true，否则返回false
     */
    private static boolean isValidBirthDate(String birthDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        try {
            LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
            LocalDate today = LocalDate.now();
            return !birthDate.isAfter(today);
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 校验身份证号中的校验码是否合法
     *
     * @param idCard 身份证号
     * @return 如果校验码合法返回true，否则返回false
     */
    private static boolean isValidCheckCode(String idCard) {
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += Character.getNumericValue(idCard.charAt(i)) * WEIGHTS[i];
        }
        char expectedCheckCode = CHECK_CODES[sum % 11];
        char actualCheckCode = idCard.charAt(17);
        return actualCheckCode == expectedCheckCode;
    }

}
