package com.xk.acct.domain.model.user;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 用户收货地址实体类
 * @date 2025/4/27 19:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAddressEntity implements Entity<LongIdentifier> {
    /**
     * 用户地址id
     */
    private Long userAddressId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 省市区中文
     */
    private String addressSite;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 是否是默认收货地址（0：不是，1：是）
     */
    private Integer isDefault;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer isDelete;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 注册时间
     */
    private Date createTime;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userAddressId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
