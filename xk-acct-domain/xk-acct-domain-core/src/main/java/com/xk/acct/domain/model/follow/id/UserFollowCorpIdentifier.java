package com.xk.acct.domain.model.follow.id;

import com.myco.mydata.domain.model.Identifier;
import lombok.*;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserFollowCorpIdentifier implements Identifier<UserFollowCorpIdentifier> {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商户ID
     */
    private Long corpInfoId;

    @Override
    public @NonNull UserFollowCorpIdentifier getIdentifier() {
        return UserFollowCorpIdentifier.builder().userId(userId).corpInfoId(corpInfoId).build();
    }
}
