package com.xk.acct.domain.model.user;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.acct.enums.user.RegisterTypeEnum;
import com.xk.acct.enums.user.UserRegisterChannelEnum;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserRegisterEntity implements Entity<LongIdentifier> {
    /**
     * 注册用户ID
     */
    private Long userId;

    /**
     * 游客ID
     */
    private Long visitorUserId;

    /**
     * 注册时间
     */
    private Date registerDate;

    /**
     * 注册方式本站注册、第三方账户登录注册
     */
    private RegisterTypeEnum registerType;

    /**
     * 渠道编号
     */
    private UserRegisterChannelEnum channelId;

    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;

    /**
     * 平台详细版本 操作系统的版本信息
     */
    private String platformInfo;

    /**
     * 终端:pc浏览器版本信息、app手机型号
     */
    private String terminalVersions;

    /**
     * APP版本号
     */
    private String appVersions;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * ip
     */
    private String ip;

    /**
     * mac地址
     */
    private String mac;

    /**
     * gps
     */
    private String gps;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 来源第三方网站跳转过来，APP跳转过来，用户主动打开
     */
    private String comeFrom;

    /**
     * 登录其他信息
     */
    private String loginDetail;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(userId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}