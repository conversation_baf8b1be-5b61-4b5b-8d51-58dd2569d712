package com.xk.acct.domain.model.user;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.domain.model.exception.SystemErrorEnum;
import com.myco.mydata.domain.model.user.AccountStatusEnum;
import com.myco.mydata.domain.model.user.UserDataObjEntity;
import com.myco.mydata.domain.model.user.UserTypeEnum;
import com.xk.acct.domain.commons.XkAcctDomainErrorEnum;
import com.xk.acct.domain.support.XkAcctDomainException;
import com.xk.acct.enums.user.*;
import com.xk.enums.common.CommonStatusEnum;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.*;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDataEntity extends UserDataObjEntity {
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 登录名
     */
    private String loginName;
    /**
     * 注册类型
     */
    private RegisterTypeEnum registerType;
    /**
     * 用户头像
     */
    private String picId;
    /**
     * 注册的平台id，本站注册填HOME
     */
    private String accountPlatformId;
    /**
     * 来源渠道
     */
    private UserRegisterChannelEnum channelId;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date birthday;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * mobile_status
     */
    private IdentifyBindStatusEnum mobileStatus;
    /**
     * email
     */
    private String email;
    /**
     * email_status
     */
    private IdentifyBindStatusEnum emailStatus;
    /**
     * 是否允许申请商户
     */
    private CommonStatusEnum corpApplyStatus;
    /**
     * 国家
     */
    private String country;
    /**
     * 国家id
     */
    private Integer countryId;
    /**
     * 省的中文名称(app端直接使用中文)
     */
    private String province;
    /**
     * 省id
     */
    private Integer provinceId;
    /**
     * 县市中文名称(app端直接使用中文)
     */
    private String city;
    /**
     * 县市id
     */
    private Integer cityId;
    /**
     * 个人主页封面照
     */
    private String cover;
    /**
     * 星座id
     */
    private Integer constellationId;
    /**
     * Twitter
     */
    private String twitter;
    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 注册时记录的设备串号
     */
    private String deviceNumber;
    /**
     * 最后一次登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastLoginTime;
    /**
     * 最后更新资料时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateTime;
    /**
     * 最后充值时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastRechargeTime;
    /**
     * 最后登录平台
     */
    private PlatformTypeEnum lastPlatformType;
    /**
     * 大小写不敏感的登陆名（搜索使用）
     */
    private String loginNameLike;
    /**
     * 是否更新
     */
    private Integer isUpdate;
    /**
     * 是否完整
     */
    private Integer isFull;
    /**
     * facebook 个人主页
     */
    private String facebook;
    /**
     * 手机区位码
     */
    private String mobileCode;

    /**
     * 0：未更新，1：已更新 密码是否更新
     */
    private UserDataPwdUpdateStatusEnum isPwdUpdate;

    /**
     * 个人主页
     */
    private String web;

    /**
     * 最后登录城市
     */
    private String lastLoginCity;
    /**
     * 最后登录国家
     */
    private String lastLoginCountry;

    /**
     * 最后登录设备
     */
    private String lastLoginTerminal;

    /**
     * 分享id
     */
    private Long shareId;
    /**
     * 支付ID u_consume_pay ->done_code
     */
    private Long lastPayId;

    /**
     * 二要素认证状态
     */
    private BaseAuthStatusEnum baseAuthStatus;

    /**
     * 实人认证状态
     */
    private FaceAuthStatusEnum faceAuthStatus;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 最后一次登录ip
     */
    private String lastLoginIp;
    /**
     * 最后一次登录日志id
     */
    private Long lastLoginLogId;
    /**
     * 最后一次管理登录日志id
     */
    private Long lastMrgLoginLogId;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 用户组名称
     */
    private String groupName;

    /**
     * 是否删除（0：不删除，1：已删除）
     */
    private Integer isDelete;

    /**
     * 是否冻结
     */
    private String isFreeze;

    /**
     * 是否禁止购物
     */
    private String isForbidDeal;

    /**
     * 是否禁止编辑
     */
    private String isForbidEdit;

    /**
     * 积分
     */
    private Integer point;

    /**
     * 消费总金额
     */
    private Long totalConsumeAmount;

    /**
     * 消费总次数
     */
    private Integer totalConsumeNum;

    public UserAuthStatusEnum getUserAuthStatus() {
        boolean isAuth =
                BaseAuthStatusEnum.ENABLE.equals(this.baseAuthStatus) && FaceAuthStatusEnum.ENABLE
                        .equals(this.faceAuthStatus);
        return isAuth ? UserAuthStatusEnum.ENABLE : UserAuthStatusEnum.DISABLED;
    }

    /**
     * 校验用户状态
     *
     * @return Mono<Void>
     */
    public Mono<Void> checkUserStatus() {
        if (AccountStatusEnum.DISABLED.equals(this.getStatus())) {
            return Mono.error(
                    new XkAcctDomainException(XkAcctDomainErrorEnum.ACCOUNT_LOGIN_USER_STATUS));
        } else {
            return Mono.empty();
        }
    }

    /**
     * 校验用户类型和登录平台类型
     *
     * @param platformType platformType
     * @return Mono<Void>
     */
    public Mono<Void> checkUserTypeAndPlatformType(PlatformTypeEnum platformType) {
        UserTypeEnum userType = this.getUserType();
        if ((PlatformTypeEnum.PC_BOSS_OMS.equals(platformType) && !UserTypeEnum.MANAGER
                .equals(userType)) || (PlatformTypeEnum.PC_COMPANY_OMS
                .equals(platformType) && UserTypeEnum.USER.equals(userType))) {
            return Mono.error(new XkAcctDomainException(SystemErrorEnum.UNSUPPORTED_OPERATION));
        }
        return Mono.empty();
    }

    public Mono<Boolean> checkIfManager() {
        return Mono.just(UserTypeEnum.MANAGER.equals(this.getUserType()));
    }
}
