package com.xk.acct.domain.model.user;

import static com.myco.mydata.commons.constant.SettingsConstant.SESSION_VALIDATE_TIMEOUT;

import java.util.Date;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.myco.mydata.commons.support.SystemParamTableHolder;
import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.model.user.LoginTypeEnum;

import lombok.*;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLoginLogEntity implements Entity<LongIdentifier> {

    /**
     * 登录sn取seq:登录
     */
    private Long loginSn;

    /**
     * 登录时间
     */
    private Date createTime;

    /**
     * 游客登陆、本站登陆、第三方登陆
     */
    private LoginTypeEnum loginType;

    /**
     * 游客ID
     */
    private Long visitorUserId;

    /**
     * 注册用户ID
     */
    private Long userId;

    /**
     * 令牌ID
     */
    private String tokenId;

    /**
     * 第三方账户绑定SN
     */
    private Long accountSn;

    /**
     * sessionID
     */
    private String sessionId;

    /**
     * 是否是机器人
     */
    private String isRobot;

    /**
     * 渠道编号
     */
    private String channelId;

    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;

    /**
     * 平台详细版本 操作系统的版本信息
     */
    private String platformInfo;

    /**
     * 终端:pc浏览器版本信息、app手机型号
     */
    private String terminalVersions;

    /**
     * APP版本号
     */
    private String appVersions;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * ip
     */
    private String ip;

    /**
     * mac地址
     */
    private String mac;

    /**
     * gps
     */
    private String gps;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 来源第三方网站跳转过来，APP跳转过来，用户主动打开
     */
    private String comeFrom;

    /**
     * 登录其他信息
     */
    private String loginDetail;

    /**
     * 国家
     */
    private String country;
    /**
     * 城市
     */
    private String city;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(loginSn).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return null;
    }


    /**
     * 与上一次登录日志比较
     * 
     * @param lastUserLoginLogEntityMono lastUserLoginLogEntityMono
     * @return Mono<Boolean>
     */
    public Mono<Boolean> compareWithLast(Mono<UserLoginLogEntity> lastUserLoginLogEntityMono) {
        return lastUserLoginLogEntityMono.map(lastUserLoginLogEntity -> {
            if (lastUserLoginLogEntity == null) {
                return false;
            }
            // 比较当前创建时间和最后登录创建时间是否超过7天
            if (this.createTime.getTime() - lastUserLoginLogEntity.getCreateTime()
                    .getTime() > SystemParamTableHolder.getLongValue(SESSION_VALIDATE_TIMEOUT)) {
                return true;
            }
            // 比较ip地址是否一致
            if (this.ip == null || !this.ip.equals(lastUserLoginLogEntity.getIp())) {
                return true;
            }
            // 比较设备号是否一致
            if (this.deviceNumber == null
                    || !this.deviceNumber.equals(lastUserLoginLogEntity.getDeviceNumber())) {
                return true;
            }
            // 比较平台类型是否一致
            if (this.platformType == null
                    || !this.platformType.equals(lastUserLoginLogEntity.getPlatformType())) {
                return true;
            }
            // 比较平台信息是否一致
            if (this.platformInfo == null
                    || !this.platformInfo.equals(lastUserLoginLogEntity.getPlatformInfo())) {
                return true;
            }
            // 比较手机
            if (this.mobile == null || !this.mobile.equals(lastUserLoginLogEntity.getMobile())) {
                return true;
            }
            // mac地址
            if (this.mac == null || !this.mac.equals(lastUserLoginLogEntity.getMac())) {
                return true;
            }
            // 验证地cookies中存储的用户ID
            if (this.userId == null || !this.userId.equals(lastUserLoginLogEntity.getUserId())) {
                return true;
            }
            // 比较终端设备信息是否一致
            return this.terminalVersions == null
                    || !this.terminalVersions.equals(lastUserLoginLogEntity.getTerminalVersions());
        });
    }
}
