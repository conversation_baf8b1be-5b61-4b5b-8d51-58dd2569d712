package com.xk.acct.domain.repository.count;


import com.myco.mydata.domain.repository.IQueryRepository;
import com.xk.acct.domain.model.count.ordersource.UserSourceCountEntity;
import com.xk.acct.domain.model.count.ordersource.UserSourceCountIdentifier;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
public interface UserSourceCountRootQueryRepository extends IQueryRepository {


    Mono<UserSourceCountEntity> findById(UserSourceCountIdentifier userSourceCountIdentifier);

    Flux<UserSourceCountEntity> findList(UserSourceCountEntity entity);
}
