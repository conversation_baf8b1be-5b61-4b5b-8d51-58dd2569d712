package com.xk.acct.domain.model.user;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.LongIdentifier;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInsuranceEditLogEntity implements Entity<LongIdentifier> {

    /**
     * 用户修改包赔记录id
     */
    private Long logId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * qq
     */
    private String qq;

    /**
     * 微信
     */
    private String wechat;

    /**
     * 省的中文名称(app端直接使用中文)
     */
    private String province;

    /**
     * 县市中文名称(app端直接使用中文)
     */
    private String city;

    /**
     * 区
     */
    private String county;

    /**
     * 父亲姓名
     */
    private String fatherName;

    /**
     * 母亲姓名
     */
    private String motherName;

    /**
     * 父亲手机号
     */
    private String fatherMobile;

    /**
     * 母亲手机号
     */
    private String motherMobile;

    /**
     * 网购截图
     */
    private String screenshot;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Override
    public @NonNull LongIdentifier getIdentifier() {
        return LongIdentifier.builder().id(logId).build();
    }

    @Override
    public Validatable<LongIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}