package com.xk.cms.gateway.config;

import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * @author: killer
 **/
public class XkCmsServiceConfig {
    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

}
