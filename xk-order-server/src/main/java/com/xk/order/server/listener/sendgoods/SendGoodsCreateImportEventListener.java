package com.xk.order.server.listener.sendgoods;

import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.service.event.EventRootService;
import com.myco.mydata.infrastructure.jms.adapter.rocketmq.AbstractDispatchMessageListener;
import com.myco.mydata.infrastructure.jms.annotation.ConsumerListener;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateEvent;
import com.xk.order.domain.event.sendgoods.SendGoodsCreateImportEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;

@ConsumerListener
@RequiredArgsConstructor
public class SendGoodsCreateImportEventListener
        extends AbstractDispatchMessageListener<SendGoodsCreateImportEvent>
        implements MessageListenerConcurrently {

    private final EventRootService eventRootService;

    @Override
    public void doProcessMessage(SendGoodsCreateImportEvent event) throws Throwable {
        EventRoot eventRoot = EventRoot.builder().domainEvent(event).isQueue(true).build();
        eventRootService.handler(eventRoot);
    }
}
