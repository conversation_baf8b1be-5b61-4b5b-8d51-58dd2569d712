package com.xk.message.interfaces.dto.req.template;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLiveMessageTemplateReqDto extends RequireSessionDto {

    /**
     * 模板id
     */
    @Min(value = 1, message = "直播间模板id错误")
    @NotNull(message = "直播间模板id不能为空")
    private Long templateId;

    /**
     * 模板内容
     */
    @Size(min = 10, max = 100, message = "模板内容长度最少输入10个字，最多可输入100字")
    @NotBlank(message = "模板内容不能为空")
    private String templateContent;

    /**
     * 模板id
     */
    @Min(value = 1, message = "模板id错误")
    @NotNull(message = "模板id不能为空")
    private Long businessMessageTemplateId;

}