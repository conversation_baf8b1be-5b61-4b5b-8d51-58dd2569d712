package com.xk.message.interfaces.dto.req.template;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePushMessageTemplateReqDto extends RequireSessionDto {

    /**
     * 模板id
     */
    @Min(value = 1, message = "推送模板id错误")
    @NotNull(message = "推送模板id不能为空")
    private Long templateId;

    /**
     * 模板内容
     */
    @Size(min = 10, max = 200, message = "模板内容长度最少输入10个字，最多可输入200字")
    @NotBlank(message = "模板内容不能为空")
    private String templateContent;

    /**
     * 模板id
     */
    @Min(value = 1, message = "模板id错误")
    @NotNull(message = "模板id不能为空")
    private Long businessMessageTemplateId;

    /**
     * 标题
     */
    @Size(max = 100, message = "标题长度不能超过100个字符")
    @NotBlank(message = "标题不能为空")
    private String title;

}