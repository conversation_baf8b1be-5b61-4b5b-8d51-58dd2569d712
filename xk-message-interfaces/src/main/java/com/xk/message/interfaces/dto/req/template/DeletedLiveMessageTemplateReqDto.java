package com.xk.message.interfaces.dto.req.template;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeletedLiveMessageTemplateReqDto extends RequireSessionDto {

    /**
     * 模板id
     */
    @Min(value = 1, message = "直播间模板id错误")
    @NotNull(message = "直播间模板id不能为空")
    private Long templateId;

}