package com.xk.tp.infrastructure.convertor.access;


import com.xk.tp.domain.model.access.ids.AccessAccountIdentifier;

public class AccessAccountIdentifierConvertor {

    private AccessAccountIdentifierConvertor() {}

    public static AccessAccountIdentifier map(Long value) {
        if (value == null)
            return null;
        return AccessAccountIdentifier.builder().accessAccountId(value).build();
    }

    public static Long map(AccessAccountIdentifier value) {
        if (value == null)
            return null;
        return value.getAccessAccountId();
    }
}
