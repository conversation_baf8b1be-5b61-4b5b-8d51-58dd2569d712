package com.xk.tp.infrastructure.service.reconciled;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayDataDataserviceBillDownloadurlQueryModel;
import com.alipay.api.request.AlipayDataDataserviceBillDownloadurlQueryRequest;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.xk.tp.enums.reconciled.AliBillTypeEnum;
import org.springframework.stereotype.Service;

import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.xk.tp.domain.model.reconciled.ReconciledPayPlatformTypeEntity;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;
import com.xk.tp.domain.service.reconciled.ReconciledService;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.infrastructure.commons.entity.ApiConfig;
import com.xk.tp.infrastructure.commons.util.BeanUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> date 2024/07/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AliReconciledServiceImpl implements ReconciledService {

    public static final String URL = "https://openapi.alipay.com/gateway.do";

    @Override
    public Integer getPayPlatformType() {
        return PayPlatformTypeEnum.ALI.getValue();
    }

    @Override
    public Mono<Void> selectReconciled(ReconciledRoot root) {
        try {
            ReconciledPayPlatformTypeEntity platformTypeEntity = root.getReconciledPayPlatformTypeEntity();
            ApiConfig apiConfig = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);

            // 初始化SDK
            AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig(apiConfig));

            // 构造请求参数以调用接口
            AlipayDataDataserviceBillDownloadurlQueryRequest request = new AlipayDataDataserviceBillDownloadurlQueryRequest();
            AlipayDataDataserviceBillDownloadurlQueryModel model = new AlipayDataDataserviceBillDownloadurlQueryModel();

            // 设置账单类型
            model.setBillType(AliBillTypeEnum.TRADE_BILL.getValue());

            // 设置账单时间
            model.setBillDate(root.getReconciledEntity().getTradeDate().toString());

            request.setBizModel(model);
            // 第三方代调用模式下请设置app_auth_token
            // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

            AlipayDataDataserviceBillDownloadurlQueryResponse response = alipayClient.execute(request);
            System.out.println(response.getBody());

            if (response.isSuccess()) {
                System.out.println("调用成功");
            } else {
                System.out.println("调用失败");
                // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
                // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                // System.out.println(diagnosisUrl);
            }
        } catch (Exception e){
            log.error("", e);
            throw new RuntimeException("Failed to execute ali reconciled operation", e);
        }
        return Mono.empty();
    }

    @Override
    public Mono<Void> huiFuMerchantConfig(ReconciledRoot root) {
        return Mono.empty();
    }

    private AlipayConfig getAlipayConfig(ApiConfig apiConfig) throws Exception {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(URL);
        alipayConfig.setAppId(apiConfig.getAppId());
        alipayConfig.setPrivateKey(apiConfig.getAppKey());
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(apiConfig.getPublicKey());
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }
}
