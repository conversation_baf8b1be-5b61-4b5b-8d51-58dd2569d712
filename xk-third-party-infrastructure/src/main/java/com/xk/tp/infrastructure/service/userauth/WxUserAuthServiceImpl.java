package com.xk.tp.infrastructure.service.userauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xk.tp.domain.model.userauth.UserAuthAccessTokenEntity;
import com.xk.tp.domain.model.userauth.UserAuthConfigValObj;
import com.xk.tp.domain.model.userauth.UserAuthInfoEntity;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.service.userauth.UserAuthService;
import com.xk.tp.enums.access.UserAuthChannelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

@Slf4j
@Service(WxUserAuthServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class WxUserAuthServiceImpl implements UserAuthService {

    public static final String BEAN_NAME = "wxUserAuthServiceImpl";

    private final RestClient restClient;

    @Override
    public Integer getUserAuthType() {
        return UserAuthChannelTypeEnum.weixin.getValue();
    }

    @Override
    public UserAuthRoot getUserInfo(UserAuthRoot userAuthRoot) {
        try {
            log.info("WxUserAuthServiceImpl 开始微信获取accessToken：{}", JSON.toJSONString(userAuthRoot));
            String atUrl = getAccessTokenUrl(userAuthRoot);
            log.info("WxUserAuthServiceImpl 微信获取accessToken url：{}", atUrl);
            ResponseEntity<String> accessTokenRes = restClient.get().uri(atUrl)
                    .accept(MediaType.APPLICATION_JSON_UTF8).retrieve().toEntity(String.class);
            log.info("WxUserAuthServiceImpl 微信获取accessToken resp：{}", accessTokenRes.getBody());
            JSONObject accessTokenJson = JSON.parseObject(accessTokenRes.getBody());

            String url = getUserInfoUrl(userAuthRoot, accessTokenJson);
            log.info("WxUserAuthServiceImpl 微信获取用户信息 url:{}", url);
            ResponseEntity<String> entity = restClient.get().uri(url)
                    .accept(MediaType.APPLICATION_JSON_UTF8).retrieve().toEntity(String.class);
            log.info("WxUserAuthServiceImpl 微信获取用户信息 resp:{}", entity.getBody());
            JSONObject jsonObject = JSON.parseObject(entity.getBody());
            UserAuthInfoEntity userAuthInfoEntity = userAuthRoot.getUserAuthInfoEntity();
            userAuthInfoEntity.setOpenid(jsonObject.getString("openid"));
            userAuthInfoEntity.setNickname(jsonObject.getString("nickname"));
            userAuthInfoEntity.setSex(jsonObject.getString("sex"));
            userAuthInfoEntity.setUnionid(jsonObject.getString("unionid"));
            return userAuthRoot;
        } catch (Exception e) {
            log.error("WxUserAuthServiceImpl 微信获取用户信息 异常：{}", e.getMessage());
        }
        return null;
    }

    private String getUserInfoUrl(UserAuthRoot userAuthRoot, JSONObject accessTokenJson) {
        String accessToken = accessTokenJson.getString("access_token");
        String openid = accessTokenJson.getString("openid");
        return String.format("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s",
                accessToken, openid);
    }

    private String getAccessTokenUrl(UserAuthRoot userAuthRoot) {
        UserAuthConfigValObj configValObj = userAuthRoot.getUserAuthConfigValObj();
        UserAuthAccessTokenEntity userAuthAccessTokenEntity =
                userAuthRoot.getUserAuthAccessTokenEntity();
        return String.format(
                "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=%s",
                configValObj.getAppid(), configValObj.getSecret(),
                userAuthAccessTokenEntity.getCode(), "authorization_code");
    }
}
