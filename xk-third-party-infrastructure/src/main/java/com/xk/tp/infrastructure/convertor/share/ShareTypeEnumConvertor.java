package com.xk.tp.infrastructure.convertor.share;

import com.xk.tp.enums.share.ShareTypeEnum;

/**
 * 分享类型枚举转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 15:39
 */
public class ShareTypeEnumConvertor {

    private ShareTypeEnumConvertor(){}

    public static ShareTypeEnum map(Integer code) {
        if(code == null) {
            return null;
        }
        return ShareTypeEnum.getEnum(code);
    }

    public static Integer map(ShareTypeEnum shareTypeEnum) {
        if(shareTypeEnum == null) {
            return null;
        }
        return shareTypeEnum.getCode();
    }
}
