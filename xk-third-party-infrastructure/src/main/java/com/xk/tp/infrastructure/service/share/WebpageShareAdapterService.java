package com.xk.tp.infrastructure.service.share;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.entity.ShareBusinessEntity;
import com.xk.tp.domain.model.share.obj.WebpageValObj;
import com.xk.tp.domain.service.share.ShareTypeAdapterService;
import com.xk.tp.enums.share.ShareTypeEnum;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

/**
 * 页面类型分享适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:52
 */
@Service(WebpageShareAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class WebpageShareAdapterService implements ShareTypeAdapterService<WebpageValObj> {

    @Override
    public ShareTypeEnum getShareType() {
        return ShareTypeEnum.WEBPAGE;
    }

    @Override
    public Mono<WebpageValObj> getShareVal(ShareRoot root) {
        ShareBusinessEntity shareBusinessEntity = root.getShareBusinessEntity();

        //获取分享落地页地址
        String shareH5Url = root.getShareEntity().getShareTypeExtValObj().getShareH5Url();
        //参数拼接
        Long shareBusinessId = shareBusinessEntity.getShareBusinessId();
        String businessExtField = shareBusinessEntity.getBusinessExtField();
        StringBuilder url = new StringBuilder().append("?").append("shareBusinessId").append("=").append(shareBusinessId);
        if (StringUtils.isNoneBlank(businessExtField)) {
            url.append("&").append("businessExtField").append("=").append(businessExtField);
        }
        shareH5Url = URLEncoder.encode(shareH5Url + url, StandardCharsets.UTF_8);
        return Mono.just(WebpageValObj.builder().webpageUrl(shareH5Url).build());
    }

    public static final String BEAN_NAME = "webpageShareAdapterService";
}
