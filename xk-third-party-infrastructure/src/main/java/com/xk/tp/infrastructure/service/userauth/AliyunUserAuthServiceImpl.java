package com.xk.tp.infrastructure.service.userauth;

import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.service.userauth.UserAuthService;
import com.xk.tp.enums.access.UserAuthChannelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(AliyunUserAuthServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class AliyunUserAuthServiceImpl implements UserAuthService {

    public static final String BEAN_NAME = "aliyunUserAuthServiceImpl";

    @Override
    public Integer getUserAuthType() {
        return UserAuthChannelTypeEnum.ALI_CLOUD.getValue();
    }

    @Override
    public UserAuthRoot getUserInfo(UserAuthRoot userAuthRoot) {
        return null;
    }
}
