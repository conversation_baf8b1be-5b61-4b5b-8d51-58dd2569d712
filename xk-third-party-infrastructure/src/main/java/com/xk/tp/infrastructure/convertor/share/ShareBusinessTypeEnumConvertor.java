package com.xk.tp.infrastructure.convertor.share;

import com.xk.tp.enums.share.ShareBusinessTypeEnum;

/**
 * 分享业务类型枚举转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 15:39
 */
public class ShareBusinessTypeEnumConvertor {

    private ShareBusinessTypeEnumConvertor(){}

    public static ShareBusinessTypeEnum map(Integer code) {
        if(code == null) {
            return null;
        }
        return ShareBusinessTypeEnum.getEnum(code);
    }

    public static Integer map(ShareBusinessTypeEnum shareBusinessTypeEnum) {
        if(shareBusinessTypeEnum == null) {
            return null;
        }
        return shareBusinessTypeEnum.getCode();
    }
}
