package com.xk.tp.infrastructure.service.reconciled;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.sign.JsonUtils;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.RsaUtils;
import com.myco.mydata.domain.model.event.EventRoot;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.myco.mydata.domain.service.event.EventRootService;
import com.xk.infrastructure.util.ExcelUtil;
import com.xk.tp.domain.commons.MoneyHelper;
import com.xk.tp.domain.event.reconciled.CreateFinancialTransactionEvent;
import com.xk.tp.domain.event.sms.SendSmsEvent;
import com.xk.tp.domain.model.reconciled.ReconciledPayPlatformTypeEntity;
import com.xk.tp.domain.model.reconciled.ReconciledRoot;
import com.xk.tp.domain.service.reconciled.ReconciledService;
import com.xk.tp.enums.pay.PayPlatformTypeEnum;
import com.xk.tp.enums.reconciled.BillTypeEnum;
import com.xk.tp.infrastructure.commons.entity.ApiConfig;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.infrastructure.dto.rsp.HuiFuReconciledFileDetailsRsp;
import com.xk.tp.infrastructure.dto.rsp.HuiFuReconciledFileExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> date 2024/07/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HuiFuReconciledServiceImpl implements ReconciledService {

    public static final String CONFIG_URI = "/v2/merchant/busi/bill/config";
    public static final String QUERY_URI = "v2/trade/check/filequery";

    private EventRootService eventRootService;

    @Autowired
    @Lazy
    public void setEventRootService(EventRootService eventRootService) {
        this.eventRootService = eventRootService;
    }

    @Override
    public Integer getPayPlatformType() {
        return PayPlatformTypeEnum.HUIFU.getValue();
    }

    @Override
    public Mono<Void> selectReconciled(ReconciledRoot root) {
        try {
            // 1. 数据初始化，填入对应的商户配置
            ReconciledPayPlatformTypeEntity platformTypeEntity = root.getReconciledPayPlatformTypeEntity();
            ApiConfig hfPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);
            Map<String,Object> requestBody = getSelectReconciledReqBody(root, hfPay);

            // 3. 发起API调用
            Map<String, Object> response = BasePayRequest.requestBasePay(QUERY_URI, requestBody, null, false);
            Object fileDetails = response.get("file_details");
            if (fileDetails == null) {
                return Mono.empty();
            }
            List<HuiFuReconciledFileDetailsRsp> huiFuReconciledFileDetailsRspList = JSONArray.parseArray(fileDetails.toString(), HuiFuReconciledFileDetailsRsp.class);
            return Flux.fromIterable(huiFuReconciledFileDetailsRspList).flatMap(huiFuReconciledFileDetailsRsp -> {
                try {
                    processLargeZipFromUrl(huiFuReconciledFileDetailsRsp.getDownloadUrl());
                } catch (IOException e) {
                    log.error("", e);
                }
                return Mono.empty();
            }).then();
        } catch (Exception e) {
            log.error("", e);
            throw new RuntimeException("Failed to execute huifu reconciled operation", e);
        }
    }

    public void processLargeZipFromUrl(String fileUrl) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("excelZip");

        try {
            // 从URL获取输入流
            URL url = new URL(fileUrl);
            try (InputStream inputStream = url.openStream();
                 ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {

                ZipEntry entry;
                // 遍历ZIP文件中的每个条目
                while ((entry = zipInputStream.getNextEntry()) != null) {
                    // 只处理Excel文件
                    if (entry.getName().toLowerCase().endsWith(".xls") ||
                            entry.getName().toLowerCase().endsWith(".xlsx")) {

                        // 创建临时文件
                        Path tempFile = tempDir.resolve(Paths.get(entry.getName()).getFileName().toString());
                        System.out.println("解压文件到: " + tempFile);

                        // 将文件写入临时目录
                        try (OutputStream outputStream = Files.newOutputStream(tempFile)) {
                            byte[] buffer = new byte[1024];
                            int len;
                            while ((len = zipInputStream.read(buffer)) > -1) {
                                outputStream.write(buffer, 0, len);
                            }
                        }

                        // 处理Excel文件
                        try (InputStream excelInputStream = Files.newInputStream(tempFile)) {
                            readExcelFile(excelInputStream);
                        } catch (ExceptionWrapperThrowable e) {
                            throw new RuntimeException(e);
                        }

                        // 删除临时文件
                        Files.deleteIfExists(tempFile);
                    }
                    zipInputStream.closeEntry();
                }
            }
        } finally {
            // 删除临时目录
            Files.walk(tempDir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            Files.deleteIfExists(path);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    });
        }
    }

    private void readExcelFile(InputStream inputStream) throws ExceptionWrapperThrowable {
        List<HuiFuReconciledFileExcel> huiFuReconciledFileExcels = ExcelUtil.readExcel(inputStream, HuiFuReconciledFileExcel.class);
        for (HuiFuReconciledFileExcel huiFuReconciledFileExcel : huiFuReconciledFileExcels) {
            EventRoot event = EventRoot.builder()
                    .domainEvent(CreateFinancialTransactionEvent.builder()
                            .financialTransactionId(huiFuReconciledFileExcel.getFinancialTransactionId())
                            .payPlatformTypeEnum(PayPlatformTypeEnum.HUIFU)
                            .financialDate(huiFuReconciledFileExcel.getFinancialDate())
                            .payNo(huiFuReconciledFileExcel.getPayNo())
                            .payAmount(MoneyHelper.multiply(Long.parseLong(huiFuReconciledFileExcel.getPayAmount())))
                            .build()).build();
                eventRootService.publish(event);
        }
    }


    @Override
    public Mono<Void> huiFuMerchantConfig(ReconciledRoot root) {
        try {
            // 1. 数据初始化，填入对应的商户配置
            ReconciledPayPlatformTypeEntity platformTypeEntity = root.getReconciledPayPlatformTypeEntity();
            ApiConfig hfPay = BeanUtil.mapTo(platformTypeEntity.getConfig(), ApiConfig.class);
            Map<String,Object> requestBody = getConfigReqBody(root, hfPay);

            // 3. 发起API调用
            Map<String, Object> response = BasePayRequest.requestBasePay(CONFIG_URI, requestBody, null, false);
            log.info(JSONObject.toJSONString(response));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Mono.empty();
    }

    private Map<String,Object> getSelectReconciledReqBody(ReconciledRoot root, ApiConfig hfPay) throws Exception {
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(hfPay.getProductId());
        merConfig.setSysId(hfPay.getSysId());
        merConfig.setRsaPrivateKey(hfPay.getAppKey());
        merConfig.setRsaPublicKey(hfPay.getPublicKey());

        BasePay.initWithMerConfig(merConfig);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        // 请求日期
        paramsInfo.put("req_date", LocalDate.now().format(formatter));
        // 请求流水号
        paramsInfo.put("req_seq_id", "dayReconciled" + DateTools.getCurrentDateTimeYYYYMMDDHHMMSSSSS());
        // 汇付客户Id
        paramsInfo.put("huifu_id", hfPay.getCusId());
        // 文件生成日期
        paramsInfo.put("file_date", root.getReconciledEntity().getTradeDate().format(formatter));
        // 对账单类型
        paramsInfo.put("bill_type", BillTypeEnum.TRADE_BILL.getValue());

        String reqData = JSONObject.toJSONString(paramsInfo);
        String sortedData = JsonUtils.sort4JsonString(reqData, 0);
        String requestSign = RsaUtils.sign(sortedData, hfPay.getAppKey());
        // 签名
        paramsInfo.put("sign", requestSign);

        return paramsInfo;
    }

    private Map<String,Object> getConfigReqBody(ReconciledRoot root, ApiConfig hfPay) throws Exception {
        MerConfig merConfig = new MerConfig();
        merConfig.setProcutId(hfPay.getProductId());
        merConfig.setSysId(hfPay.getSysId());
        merConfig.setRsaPrivateKey(hfPay.getAppKey());
        merConfig.setRsaPublicKey(hfPay.getPublicKey());

        BasePay.initWithMerConfig(merConfig);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 2.组装请求参数
        Map<String, Object> paramsInfo = new HashMap<>();
        // 请求日期
        paramsInfo.put("req_date", LocalDate.now().format(formatter));
        // 请求流水号
        paramsInfo.put("req_seq_id", "dayReconciled" + DateTools.getCurrentDateTimeYYYYMMDDHHMMSSSSS());
        // 汇付客户Id
        paramsInfo.put("huifu_id", hfPay.getCusId());
        // 文件生成日期
        paramsInfo.put("file_date", root.getReconciledEntity().getTradeDate().format(formatter));
        // 对账单类型
        paramsInfo.put("file_type", "1");

        paramsInfo.put("recon_send_flag", "Y");

        String reqData = JSONObject.toJSONString(paramsInfo);
        String sortedData = JsonUtils.sort4JsonString(reqData, 0);
        String requestSign = RsaUtils.sign(sortedData, hfPay.getAppKey());
        // 签名
        paramsInfo.put("sign", requestSign);

        return paramsInfo;
    }
}
