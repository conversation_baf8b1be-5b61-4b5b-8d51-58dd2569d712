package com.xk.tp.infrastructure.service.userauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.xk.tp.domain.model.userauth.UserAuthConfigValObj;
import com.xk.tp.domain.model.userauth.UserAuthInfoEntity;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.service.userauth.UserAuthService;
import com.xk.tp.enums.access.UserAuthChannelTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.RSAPublicKeySpec;

@Slf4j
@Service(AppleUserAuthServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class AppleUserAuthServiceImpl implements UserAuthService {

    public static final String BEAN_NAME = "appleUserAuthServiceImpl";

    private final RestClient restClient;

    @Override
    public Integer getUserAuthType() {
        return UserAuthChannelTypeEnum.APPLE.getValue();
    }

    @Override
    public UserAuthRoot getUserInfo(UserAuthRoot userAuthRoot) {
        try {
            log.info("AppleUserAuthServiceImpl apple授权参数 {}", JSON.toJSONString(userAuthRoot));
            UserAuthInfoEntity userAuthInfoEntity = userAuthRoot.getUserAuthInfoEntity();
            UserAuthConfigValObj configValObj = userAuthRoot.getUserAuthConfigValObj();
            DecodedJWT decodedJWT = JWT.decode(userAuthInfoEntity.getCode());
            Claim kid = decodedJWT.getHeaderClaim("kid");
            // 获取公钥
            RSAPublicKey publicKey = getPublicKey(kid.asString());
            //验签
            DecodedJWT jwt = JWT.require(Algorithm.RSA256(publicKey, null))
                    .withIssuer("https://appleid.apple.com")
                    .withAudience(configValObj.getClientId())
                    .build()
                    .verify(userAuthInfoEntity.getCode());
            userAuthInfoEntity.setUnionid(jwt.getSubject());
        } catch (Exception e) {
            log.error("AppleUserAuthServiceImpl apple授权异常：{}", e.getMessage());
        }
        return null;
    }

    @SneakyThrows
    private RSAPublicKey getPublicKey(String kid) {
        ResponseEntity<String> entity = restClient.get().uri("https://appleid.apple.com/auth/keys")
                .accept(MediaType.APPLICATION_JSON).retrieve().toEntity(String.class);
        JSONObject jsonObject = JSON.parseObject(entity.getBody());
        JSONArray keys = jsonObject.getJSONArray("keys");
        for (Object key : keys) {
            JSONObject jo = JSON.parseObject(key.toString());
            if (jo.getString("kid").equals(kid)) {
                BigInteger modulus = new BigInteger(1, Base64.decodeBase64(jo.getString("n")));
                BigInteger publicExponent =
                        new BigInteger(1, Base64.decodeBase64(jo.getString("e")));
                RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
                KeyFactory kf = KeyFactory.getInstance("RSA");
                return (RSAPublicKey) kf.generatePublic(spec);
            }
        }
        return null;
    }
}
