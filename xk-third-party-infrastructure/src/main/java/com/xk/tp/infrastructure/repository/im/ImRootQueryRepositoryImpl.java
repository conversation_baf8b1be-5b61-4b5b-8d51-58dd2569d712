package com.xk.tp.infrastructure.repository.im;

import com.xk.tp.domain.repository.im.ImRootQueryRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> date 2024/07/22
 */
@Repository
@RequiredArgsConstructor
public class ImRootQueryRepositoryImpl implements ImRootQueryRepository {

    private final Converter converter;


}
