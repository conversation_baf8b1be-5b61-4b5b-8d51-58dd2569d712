package com.xk.tp.infrastructure.repository.reconciled;

import com.xk.tp.domain.repository.reconciled.ReconciledRootQueryRepository;
import org.springframework.stereotype.Repository;

import com.xk.tp.domain.repository.live.LiveRootQueryRepository;

import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> date 2024/07/22
 */
@Repository
@RequiredArgsConstructor
public class ReconciledRootQueryRepositoryImpl implements ReconciledRootQueryRepository {

    private final Converter converter;


}
