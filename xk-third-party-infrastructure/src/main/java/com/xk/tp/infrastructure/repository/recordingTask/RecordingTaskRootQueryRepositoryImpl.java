package com.xk.tp.infrastructure.repository.recordingTask;

import com.myco.mydata.domain.model.StringIdentifier;
import com.xk.tp.domain.model.pay.PayRecordEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskEntity;
import com.xk.tp.domain.repository.recordingTask.RecordingTaskRootQueryRepository;
import com.xk.tp.infrastructure.data.persistence.recordingTask.TpRecordingTaskMapper;
import com.xk.tp.infrastructure.data.po.pay.TpPayRecord;
import com.xk.tp.infrastructure.data.po.recordingTask.TpRecordingTask;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * date 2024/07/22
 */
@Repository
@RequiredArgsConstructor
public class RecordingTaskRootQueryRepositoryImpl implements RecordingTaskRootQueryRepository {

    private final Converter converter;

    private final TpRecordingTaskMapper tpRecordingTaskMapper;

    @Override
    public Flux<RecordingTaskEntity> selectById(StringIdentifier identifier) {
        return this.find(
                () -> tpRecordingTaskMapper.selectByLiveId(TpRecordingTask.builder().liveId(identifier.id()).build()),
                RecordingTaskEntity.class, converter::convert);
    }
}
