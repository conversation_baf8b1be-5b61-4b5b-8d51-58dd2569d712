package com.xk.tp.infrastructure.service.share;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.MusicVideoValObj;
import com.xk.tp.domain.service.share.ShareTypeAdapterService;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 音乐视频类型分享适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:54
 */
@Service(MusicVideoShareAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class MusicVideoShareAdapterService implements ShareTypeAdapterService<MusicVideoValObj> {

    @Override
    public ShareTypeEnum getShareType() {
        return ShareTypeEnum.MUSIC_VIDEO;
    }

    @Override
    public Mono<MusicVideoValObj> getShareVal(ShareRoot root) {
        return Mono.empty();
    }

    public static final String BEAN_NAME = "musicVideoShareAdapterService";
}
