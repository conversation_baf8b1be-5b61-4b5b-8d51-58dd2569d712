package com.xk.tp.infrastructure.service.push;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.tp.domain.model.push.PushMessageRoot;
import com.xk.tp.domain.model.push.entity.MessageContextEntity;
import com.xk.tp.domain.model.push.entity.PushMessageEntity;
import com.xk.tp.domain.model.push.obj.ApiPushObj;
import com.xk.tp.domain.model.push.obj.PushChannelExtObj;
import com.xk.tp.domain.service.push.ApiPushAdapterService;
import com.xk.tp.enums.access.AccessChannelTypeEnum;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


@Slf4j
@Service(GeTuiPushServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class GeTuiPushServiceImpl implements ApiPushAdapterService {

    public static final String BEAN_NAME = "geTuiPushService";

    private static final String PUSH_SINGLE_CID_URL = "push/single/cid";

    private static Map<String, PushApi> pushApiMap = new HashMap<>();

    @Override
    public AccessChannelTypeEnum getPushChannelType() {
        return AccessChannelTypeEnum.GE_TUI;
    }


    @Override
    public Mono<ApiPushObj> pushAppMessage(PushMessageRoot root) {
        //获取推送api
        PushMessageEntity pushMessageEntity = root.getPushMessageEntity();
        MessageContextEntity messageContextEntity = root.getMessageContextEntity();
        PushChannelExtObj pushChannelExtObj = pushMessageEntity.getPushChannelExtObj();
        PushApi pushApi;
        String domain = pushChannelExtObj.getDomain();
        pushApi = pushApiMap.get(pushChannelExtObj.getAppId());
        if (pushApi == null) {
            synchronized (GeTuiPushServiceImpl.class) {
                //创建api
                GtApiConfiguration apiConfiguration = new GtApiConfiguration();
                //填写应用配置
                apiConfiguration.setAppId(pushChannelExtObj.getAppId());
                apiConfiguration.setAppKey(pushChannelExtObj.getAppKey());
                apiConfiguration.setMasterSecret(pushChannelExtObj.getMasterSecret());
                // 接口调用前缀，请查看文档: 接口调用规范 -> 接口前缀
                apiConfiguration.setDomain(domain);
                // 实例化ApiHelper对象，用于创建接口对象
                ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
                // 创建对象，建议复用。目前有PushApi、StatisticApi、UserApi
                pushApi = apiHelper.creatApi(PushApi.class);
                pushApiMap.put(pushChannelExtObj.getAppId(), pushApi);

            }
        }

        //根据cid进行单推
        PushDTO<Audience> pushRequest = new PushDTO<>();
        // 设置推送参数
        String requestId = UUID.randomUUID().toString();
        pushRequest.setRequestId(requestId);
        //设置个推通道参数
        PushMessage pushMessage = new PushMessage();
        PushChannel pushChannel = new PushChannel();

        if (pushMessageEntity.getPlatformType().equals(PlatformTypeEnum.IOS_USER)) {
            //苹果不支持个推推送信息，使用透传消息（在线）和厂商通道（离线）
            //厂商通道
            pushMessage.setTransmission(messageContextEntity.getContent());
            //使用厂商通道
            IosDTO iosDTO = new IosDTO();
            iosDTO.setApnsCollapseId(UUID.randomUUID().toString());
            Aps aps = new Aps();
            iosDTO.setAps(aps);
            Alert alert = new Alert();
            alert.setTitle(messageContextEntity.getTitle());
            alert.setBody(messageContextEntity.getContent());
            aps.setAlert(alert);
            pushChannel.setIos(iosDTO);
        } else {
            //安卓鸿蒙使用个推推送信息
            GTNotification notification = new GTNotification();
            pushMessage.setNotification(notification);
            notification.setTitle(messageContextEntity.getTitle());
            notification.setBody(messageContextEntity.getContent());
            notification.setClickType(messageContextEntity.getClickType());
            notification.setUrl(messageContextEntity.getUrl());
            //notification.setLogo(pushChannelExtObj.getLogo());
            notification.setLogoUrl(pushChannelExtObj.getLogoUrl());
            pushMessage.setNotification(notification);
        }

        pushRequest.setPushMessage(pushMessage);
        pushRequest.setPushChannel(pushChannel);

        //设置接收人信息
        Audience audience = new Audience();
        audience.addCid(pushMessageEntity.getDeviceId());
        pushRequest.setAudience(audience);
        ApiResult<Map<String, Map<String, String>>> apiResult = pushApi.pushToSingleByCid(pushRequest);
        ApiPushObj apiPushObj = ApiPushObj.builder().apiUrl(domain + PUSH_SINGLE_CID_URL)
                .params(JSON.toJSONString(pushRequest)).success(apiResult.isSuccess()).build();
        if (apiResult.isSuccess()) {
            apiPushObj.setResponse(JSON.toJSONString(apiResult.getData()));
            // success
        } else {
            // failed
            apiPushObj.setResponse(JSON.toJSONString(apiResult.getMsg()));
        }

        return Mono.justOrEmpty(apiPushObj);
    }
}
