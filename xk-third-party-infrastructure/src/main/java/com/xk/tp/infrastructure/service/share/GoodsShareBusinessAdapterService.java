package com.xk.tp.infrastructure.service.share;

import com.myco.mydata.domain.model.session.SessionRoot;
import com.xk.goods.interfaces.dto.req.goods.GoodsIdSearchReqDto;
import com.xk.goods.interfaces.dto.res.business.BusinessResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchCollectibleResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchMallResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchMaterialResDto;
import com.xk.goods.interfaces.dto.res.goods.GoodsSearchMerchantResDto;
import com.xk.goods.interfaces.query.goods.GoodsSearchQueryService;
import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.entity.ShareBusinessEntity;
import com.xk.tp.domain.model.share.obj.ShareBusinessValObj;
import com.xk.tp.domain.service.share.ShareBusinessAdapterService;
import com.xk.tp.enums.share.ShareBusinessTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 商品分享数据适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 13:43
 */
@Service(GoodsShareBusinessAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class GoodsShareBusinessAdapterService implements ShareBusinessAdapterService {


    @Override
    public ShareBusinessTypeEnum getShareBusinessType() {
        return ShareBusinessTypeEnum.GOODS;
    }

    @Override
    public Mono<ShareBusinessValObj> getShareBusinessVal(ShareRoot root) {
        ShareBusinessEntity shareBusinessEntity = root.getShareBusinessEntity();
        String businessExtField = shareBusinessEntity.getBusinessExtField();
        if (StringUtils.isBlank(businessExtField) || !NumberUtils.isCreatable(businessExtField)) {
            businessExtField = "1";
        }
        GoodsIdSearchReqDto goodsIdSearchReqDto = GoodsIdSearchReqDto.builder()
                .goodsId(shareBusinessEntity.getShareBusinessId()).build();
        goodsIdSearchReqDto.setSessionId(SessionRoot.getInternalDefaultSessionId());
        int goodsType = Integer.parseInt(businessExtField);
        //查询业务数据
        Mono<GoodsIdSearchReqDto> dtoMono = Mono.just(goodsIdSearchReqDto);
        return switch (goodsType) {
            //商城商品
            case 1 -> {
                Mono<GoodsSearchMallResDto> mailMono = goodsSearchQueryService.getGoodsMallById(dtoMono);
                yield mailMono
                        .filter(Objects::nonNull)  // 过滤掉null的GoodsSearchMallResDto
                        .flatMap(mail -> {
                            String thumbUrl = Optional.ofNullable(mail.getProductPicList())
                                    .filter(list -> !list.isEmpty())
                                    .map(List::getFirst)
                                    .map(BusinessResDto::getAddr)
                                    .orElse(null);

                            // 构建返回对象
                            return Mono.just(ShareBusinessValObj.builder()
                                    .title(mail.getGoodsName())
                                    .description(mail.getGoodsDescribe())
                                    .thumbUrl(thumbUrl)
                                    .build());
                        })
                        .switchIfEmpty(Mono.empty());
            }
            //物料商品
            case 2 -> {
                Mono<GoodsSearchMaterialResDto> materMono = goodsSearchQueryService.getGoodsMaterialById(dtoMono);
                yield materMono
                        .filter(Objects::nonNull)  // 过滤掉null的GoodsSearchMallResDto
                        .flatMap(mail -> {
                            String thumbUrl = Optional.ofNullable(mail.getProductPicList())
                                    .filter(list -> !list.isEmpty())
                                    .map(List::getFirst)
                                    .map(BusinessResDto::getAddr)
                                    .orElse(null);

                            // 构建返回对象
                            return Mono.just(ShareBusinessValObj.builder()
                                    .title(mail.getGoodsName())
                                    .description(mail.getGoodsDescribe())
                                    .thumbUrl(thumbUrl)
                                    .build());
                        })
                        .switchIfEmpty(Mono.empty());
            }
            //收藏卡
            case 3 -> {
                Mono<GoodsSearchCollectibleResDto> collectibleMono = goodsSearchQueryService.getGoodsCollectibleById(dtoMono);
                yield collectibleMono
                        .filter(Objects::nonNull)  // 过滤掉null的GoodsSearchMallResDto
                        .flatMap(mail -> {
                            String thumbUrl = Optional.ofNullable(mail.getProductPicList())
                                    .filter(list -> !list.isEmpty())
                                    .map(List::getFirst)
                                    .map(BusinessResDto::getAddr)
                                    .orElse(null);

                            // 构建返回对象
                            return Mono.just(ShareBusinessValObj.builder()
                                    .title(mail.getGoodsName())
                                    .description(mail.getGoodsDescribe())
                                    .thumbUrl(thumbUrl)
                                    .build());
                        })
                        .switchIfEmpty(Mono.empty());
            }
            //商家商品
            case 4 -> {
                Mono<GoodsSearchMerchantResDto> merchantMono = goodsSearchQueryService.getGoodsMerchantById(dtoMono);
                yield merchantMono
                        .filter(Objects::nonNull)  // 过滤掉null的GoodsSearchMallResDto
                        .flatMap(mail -> {
                            String thumbUrl = Optional.ofNullable(mail.getProductPicList())
                                    .filter(list -> !list.isEmpty())
                                    .map(List::getFirst)
                                    .map(BusinessResDto::getAddr)
                                    .orElse(null);

                            // 构建返回对象
                            return Mono.just(ShareBusinessValObj.builder()
                                    .title(mail.getGoodsName())
                                    .description(mail.getGoodsDescribe())
                                    .thumbUrl(thumbUrl)
                                    .build());
                        })
                        .switchIfEmpty(Mono.empty());
            }
            default -> Mono.empty();
        };
    }

    private final GoodsSearchQueryService goodsSearchQueryService;

    public static final String BEAN_NAME = "goodsShareAdapterService";
}
