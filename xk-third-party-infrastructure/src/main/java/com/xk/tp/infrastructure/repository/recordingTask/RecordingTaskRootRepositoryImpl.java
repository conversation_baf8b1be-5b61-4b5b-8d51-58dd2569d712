package com.xk.tp.infrastructure.repository.recordingTask;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.myco.mydata.commons.constant.SettingsConstant;
import com.myco.mydata.commons.support.SystemParamTableHolder;
import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.domain.commons.response.ApiResult;
import com.xk.tp.domain.model.recordingTask.RecordingTaskEntity;
import com.xk.tp.domain.model.recordingTask.RecordingTaskRoot;
import com.xk.tp.domain.model.sms.SmsEntity;
import com.xk.tp.domain.model.sms.SmsRoot;
import com.xk.tp.domain.repository.recordingTask.RecordingTaskRootRepository;
import com.xk.tp.domain.repository.sms.SmsRootRepository;
import com.xk.tp.domain.service.sms.ApiSmsAdapterService;
import com.xk.tp.infrastructure.data.persistence.recordingTask.TpRecordingTaskMapper;
import com.xk.tp.infrastructure.data.po.pay.TpPayRecord;
import com.xk.tp.infrastructure.data.po.recordingTask.TpRecordingTask;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/8/10 14:24
 */
@Repository
@RequiredArgsConstructor
public class RecordingTaskRootRepositoryImpl implements RecordingTaskRootRepository {

    private final Converter converter;

    private final TpRecordingTaskMapper tpRecordingTaskMapper;

    @Override
    public Mono<Void> save(RecordingTaskRoot root) {
        return this.save(root.getRecordingTaskEntity(), TpRecordingTask.class, converter::convert,
                tpRecordingTaskMapper::insertSelective);
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(RecordingTaskRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(RecordingTaskRoot root) {
        return this.update(root.getRecordingTaskEntity(), TpRecordingTask.class, converter::convert,
                tpRecordingTaskMapper::updateByPrimaryKeySelective);
    }

    @Override
    public Mono<Void> remove(RecordingTaskRoot root) {
        return null;
    }
}
