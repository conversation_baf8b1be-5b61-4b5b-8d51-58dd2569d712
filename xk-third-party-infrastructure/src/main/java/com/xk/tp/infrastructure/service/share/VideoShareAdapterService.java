package com.xk.tp.infrastructure.service.share;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.VideoValObj;
import com.xk.tp.domain.service.share.ShareTypeAdapterService;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 视频类型分享适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:54
 */
@Service(VideoShareAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class VideoShareAdapterService implements ShareTypeAdapterService<VideoValObj> {

    @Override
    public ShareTypeEnum getShareType() {
        return ShareTypeEnum.VIDEO;
    }

    @Override
    public Mono<VideoValObj> getShareVal(ShareRoot root) {
        return Mono.empty();
    }

    public static final String BEAN_NAME = "videoShareAdapterService";
}
