package com.xk.tp.infrastructure.service.im;

import com.alibaba.fastjson2.JSONObject;
import com.xk.tp.domain.model.im.ImPlatformTypeEntity;
import com.xk.tp.domain.model.im.ImRoot;
import com.xk.tp.domain.model.live.LivePlatformTypeEntity;
import com.xk.tp.domain.service.im.ImService;
import com.xk.tp.enums.im.ActionStatusEnum;
import com.xk.tp.enums.im.ImMsgTypeEnum;
import com.xk.tp.enums.im.ImPlatformTypeEnum;
import com.xk.tp.enums.im.ImTypeEnum;
import com.xk.tp.infrastructure.commons.XkThirdPartyInfrastructureErrorEnum;
import com.xk.tp.infrastructure.commons.entity.ApiConfig;
import com.xk.tp.infrastructure.commons.security.api.TLSSigAPIv2;
import com.xk.tp.infrastructure.commons.util.BeanUtil;
import com.xk.tp.infrastructure.dto.req.*;
import com.xk.tp.infrastructure.dto.rsp.*;
import com.xk.tp.infrastructure.support.XkThirdPartyInfrastructureException;
import com.xk.tp.infrastructure.utils.HttpClientPoolUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR> date 2024/07/16
 */
@Slf4j
@Service(TencentImServiceImpl.BEAN_NAME)
@RequiredArgsConstructor
public class TencentImServiceImpl implements ImService {

    public static final String BEAN_NAME = "tencentImServiceImpl";

//    private final String sdkSecretKey = "d672cb84e5e8b9cb3acbb2584f710a4da5383bcbdf486ae9bef2ceccf496ce10";
//    private final Long sdkAppId = 1600101406L;
//    private final String sdkAppIdStr = "1600101406";
//    private final Long expire = 60 * 60 * 8L;
//    private final String identifier = "administrator";
    private final String baseUrl = "https://console.tim.qq.com";
    private final String createGroupUrl = "/v4/group_open_http_svc/create_group";
    private final String deleteGroupUrl = "/v4/group_open_http_svc/destroy_group";
    private final String selectOnlineNumber = "/v4/group_open_http_svc/get_online_member_num";
    private final String sendGroupMsg = "/v4/group_open_http_svc/send_group_msg";
    private final String sendGroupSystemMsg = "/v4/group_open_http_svc/send_group_system_notification";
    private final String sendGroupMsgAll = "/v4/group_open_http_svc/send_broadcast_msg";
    private final String contenttype = "json";

    @Override
    public Integer getImPlatformType() {
        return ImPlatformTypeEnum.TENCENT.getValue();
    }

    @Override
    public Mono<Void> createGroup(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImCreateGroupReq imCreateGroupReq = ImCreateGroupReq.builder()
                .groupId(imRoot.getImLiveEntity().getImId())
                .type(ImTypeEnum.AV_CHAT_ROOM.getValue())
                .name(imRoot.getImLiveEntity().getName())
                .ownerAccount(imRoot.getImLiveEntity().getOwnerAccount())
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

//        reqHashMap.put("sdkappid", sdkAppIdStr);
//        reqHashMap.put("identifier", identifier);
//        reqHashMap.put("usersig", new TLSSigAPIv2(sdkAppId, sdkSecretKey)
//                .genUserSig(identifier, expire));

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, createGroupUrl, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(imCreateGroupReq), null);
        ImCreateGroupRsp imCreateGroupRsp = JSONObject.parseObject(rsp, ImCreateGroupRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imCreateGroupRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.empty();
    }

    @Override
    public Mono<Void> deleteGroup(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImDeleteGroupReq imDeleteGroupReq = ImDeleteGroupReq.builder()
                .groupId(imRoot.getImLiveEntity().getImId())
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, deleteGroupUrl, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(imDeleteGroupReq), null);
        ImDeleteGroupRsp imDeleteGroupRsp = JSONObject.parseObject(rsp, ImDeleteGroupRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imDeleteGroupRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.empty();
    }

    @Override
    public Mono<Long> selectOnlineNumber(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImSelectOnlineNumberReq imSelectOnlineNumberReq = ImSelectOnlineNumberReq.builder()
                .groupId(imRoot.getImLiveEntity().getImId())
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, selectOnlineNumber, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(imSelectOnlineNumberReq), null);
        ImSelectOnlineNumberRsp imSelectOnlineNumberRsp = JSONObject.parseObject(rsp, ImSelectOnlineNumberRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imSelectOnlineNumberRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.justOrEmpty(imSelectOnlineNumberRsp.getOnlineMemberNum());
    }

    @Override
    public Mono<Long> sendGroupMsgAll(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImSendGroupMsgAllReq imSendGroupMsgAllReq = ImSendGroupMsgAllReq.builder()
                .fromAccount(imRoot.getImLiveEntity().getFromAccount())
                .random(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL)
                .msgBody(List.of(ImSendGroupMsgAllReq.MsgBody.builder()
                                .msgType(ImMsgTypeEnum.TIM_TEXT_ELEM.getValue())
                                .msgContent(ImSendGroupMsgAllReq.MsgContent.builder()
                                        .text(imRoot.getImLiveEntity().getText())
                                        .build())
                        .build()))
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, sendGroupMsgAll, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(imSendGroupMsgAllReq), null);
        ImSendGroupMsgAllRsp imSendGroupMsgAllRsp = JSONObject.parseObject(rsp, ImSendGroupMsgAllRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imSendGroupMsgAllRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.justOrEmpty(imSendGroupMsgAllRsp.getMsgSeq());
    }

    @Override
    public Mono<Long> sendGroupMsg(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImSendGroupMsgReq req = ImSendGroupMsgReq.builder()
                .fromAccount(imRoot.getImLiveEntity().getFromAccount())
                .random(String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL))
                .groupId(imRoot.getImLiveEntity().getImId())
                .msgBody(List.of(ImSendGroupMsgReq.MsgBody.builder()
                        .msgType(ImMsgTypeEnum.TIM_TEXT_ELEM.getValue())
                        .msgContent(ImSendGroupMsgReq.MsgContent.builder()
                                .text(imRoot.getImLiveEntity().getText())
                                .build())
                        .build()))
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, sendGroupMsg, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(req), null);
        ImSendGroupMsgRsp imSendGroupMsgRsp = JSONObject.parseObject(rsp, ImSendGroupMsgRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imSendGroupMsgRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.justOrEmpty(imSendGroupMsgRsp.getMsgSeq());
    }

    @Override
    public Mono<Void> sendGroupSystemMsg(@NotNull ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

        ImSendGroupSystemMsgReq req = ImSendGroupSystemMsgReq.builder()
                .groupId(imRoot.getImLiveEntity().getImId())
                .content(imRoot.getImLiveEntity().getText())
                .build();

        HashMap<String, String> reqHashMap = new HashMap<>();

        reqHashMap.put("sdkappid", apiConfig.getAppId());
        reqHashMap.put("identifier", apiConfig.getIdentifier());
        reqHashMap.put("usersig", new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(apiConfig.getIdentifier(), apiConfig.getExpireTime()));
        reqHashMap.put("random", String.valueOf(ThreadLocalRandom.current().nextLong() & 0xFFFFFFFFL));
        reqHashMap.put("contenttype", contenttype);

        String rsp = HttpClientPoolUtils.doPostJson(String.format("%s%s%s", baseUrl, sendGroupSystemMsg, mapToUrlParams(reqHashMap)), JSONObject.toJSONString(req), null);
        ImSendGroupMsgRsp imSendGroupMsgRsp = JSONObject.parseObject(rsp, ImSendGroupMsgRsp.class);

        if (ActionStatusEnum.FAIL.getValue().equals(imSendGroupMsgRsp.getActionStatus())){
            return Mono.error(new XkThirdPartyInfrastructureException(
                    XkThirdPartyInfrastructureErrorEnum.INFRASTRUCTURE_ERROR));
        }

        return Mono.empty();
    }

    @Override
    public Mono<String> sign(ImRoot imRoot) {
        ImPlatformTypeEntity imPlatformTypeEntity = imRoot.getImPlatformTypeEntity();
        ApiConfig apiConfig = BeanUtil.mapTo(imPlatformTypeEntity.getConfig(), ApiConfig.class);

//        return Mono.justOrEmpty(new TLSSigAPIv2(sdkAppId, sdkSecretKey)
//                .genUserSig(imRoot.getImLiveEntity().getUserId(), expire));

        return Mono.justOrEmpty(new TLSSigAPIv2(Long.parseLong(apiConfig.getAppId()), apiConfig.getSdkSecretKey())
                .genUserSig(imRoot.getImLiveEntity().getUserId(), apiConfig.getExpireTime()));
    }

    public static String mapToUrlParams(Map<String, String> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.newInstance();
        params.forEach(builder::queryParam); // 自动处理编码
        return builder.build().encode().toUriString();
    }
}
