package com.xk.tp.infrastructure.repository.im;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.domain.model.im.ImRoot;
import com.xk.tp.domain.repository.im.ImRootRepository;
import com.xk.tp.domain.service.im.ImService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/30 11:38
 */
@Repository
@RequiredArgsConstructor
public class ImRootRepositoryImpl implements ImRootRepository {

    @BeansOfTypeToMap(value = ImService.class, methodName = "getImPlatformType")
    private Map<Integer, ImService> imServiceMap;

    @Override
    public Mono<Void> createGroup(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).createGroup(imRoot);
    }

    @Override
    public Mono<Void> deleteGroup(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).deleteGroup(imRoot);
    }

    @Override
    public Mono<Long> selectOnlineNumber(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).selectOnlineNumber(imRoot);
    }

    @Override
    public Mono<Long> sendGroupMsgAll(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).sendGroupMsgAll(imRoot);
    }

    @Override
    public Mono<Long> sendGroupMsg(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).sendGroupMsg(imRoot);
    }

    @Override
    public Mono<Void> sendGroupSystemMsg(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).sendGroupSystemMsg(imRoot);
    }

    @Override
    public Mono<String> sign(ImRoot imRoot) {
        return imServiceMap.get(imRoot.getImLiveEntity().getImPlatformTypeEnum().getValue()).sign(imRoot);
    }

    @Override
    public Mono<Void> save(ImRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(ImRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(ImRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(ImRoot root) {
        return null;
    }
}
