package com.xk.tp.infrastructure.repository.userauth;

import com.myco.mydata.commons.annotation.BeansOfTypeToMap;
import com.myco.mydata.domain.model.Identifier;
import com.xk.tp.domain.model.userauth.UserAuthPlatformTypeEntity;
import com.xk.tp.domain.model.userauth.UserAuthRoot;
import com.xk.tp.domain.repository.userauth.UserAuthRootRepository;
import com.xk.tp.domain.service.pay.ApiPayService;
import com.xk.tp.domain.service.userauth.UserAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Map;

@Repository
@RequiredArgsConstructor
public class UserAuthRootRepositoryImpl implements UserAuthRootRepository {

    @BeansOfTypeToMap(value = UserAuthService.class, methodName = "getUserAuthType")
    private Map<Integer, UserAuthService> userAuthServiceMap;

    @Override
    public Mono<Void> save(UserAuthRoot root) {
        return null;
    }

    @Override
    public Mono<? extends Identifier<?>> saveRt(UserAuthRoot root) {
        return null;
    }

    @Override
    public Mono<Void> update(UserAuthRoot root) {
        return null;
    }

    @Override
    public Mono<Void> remove(UserAuthRoot root) {
        return null;
    }

    @Override
    public Mono<UserAuthRoot> getUserInfo(UserAuthRoot userAuthRoot) {
        return Mono.fromCallable(()->{
            UserAuthPlatformTypeEntity platformTypeEntity = userAuthRoot.getUserAuthPlatformTypeEntity();
            UserAuthService userAuthService = userAuthServiceMap.get(platformTypeEntity.getChannelType().getValue());
            return userAuthService.getUserInfo(userAuthRoot);
        });
    }
}
