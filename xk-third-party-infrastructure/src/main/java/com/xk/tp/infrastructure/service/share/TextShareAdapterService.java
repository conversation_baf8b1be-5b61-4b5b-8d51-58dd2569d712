package com.xk.tp.infrastructure.service.share;

import com.xk.tp.domain.model.share.ShareRoot;
import com.xk.tp.domain.model.share.obj.TextValObj;
import com.xk.tp.domain.service.share.ShareTypeAdapterService;
import com.xk.tp.enums.share.ShareTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 文本类型分享适配器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8 14:54
 */
@Service(TextShareAdapterService.BEAN_NAME)
@RequiredArgsConstructor
public class TextShareAdapterService implements ShareTypeAdapterService<TextValObj> {

    @Override
    public ShareTypeEnum getShareType() {
        return ShareTypeEnum.TEST;
    }

    @Override
    public Mono<TextValObj> getShareVal(ShareRoot root) {
        return Mono.empty();
    }

    public static final String BEAN_NAME = "textShareAdapterService";
}
