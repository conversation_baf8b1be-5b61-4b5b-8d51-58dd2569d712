package com.xk.order.interfaces.dto.req.order;

import java.util.List;

import com.myco.mydata.interfaces.dto.commons.session.RequireSessionDto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantFortuneUnlockReq extends RequireSessionDto {

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空")
    private Long goodsId;

    /**
     * 福盒规格id
     */
    @NotEmpty(message = "福盒规格id不能为空")
    private List<Long> specificationIdList;
}
