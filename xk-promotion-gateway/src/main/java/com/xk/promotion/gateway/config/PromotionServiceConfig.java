package com.xk.promotion.gateway.config;

import com.xk.interfaces.query.object.GoodsObjectQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import com.xk.acct.interfaces.query.UserQueryService;
import com.xk.goods.interfaces.service.stock.StockService;
import com.xk.interfaces.query.object.CorpObjectQueryService;
import com.xk.interfaces.query.object.UserObjectQueryService;

/**
 * @author: killer
 **/
public class PromotionServiceConfig {

    @Bean
    public UserObjectQueryService userObjectQueryService(
            HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserObjectQueryService.class);
    }

    @Bean
    public CorpObjectQueryService corpObjectQueryService(
            HttpServiceProxyFactory xkCorpHttpServiceProxyFactory) {
        return xkCorpHttpServiceProxyFactory.createClient(CorpObjectQueryService.class);
    }

    @Bean
    public GoodsObjectQueryService  goodsObjectQueryService(HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory){
        return xkGoodsHttpServiceProxyFactory.createClient(GoodsObjectQueryService.class);
    }

    @Bean
    public StockService stockService(HttpServiceProxyFactory xkGoodsHttpServiceProxyFactory) {
        return xkGoodsHttpServiceProxyFactory.createClient(StockService.class);
    }

    @Bean
    public UserQueryService userQueryService(HttpServiceProxyFactory xkAcctHttpServiceProxyFactory) {
        return xkAcctHttpServiceProxyFactory.createClient(UserQueryService.class);
    }
}
