package com.xk.search.domain.event.retry;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@EventDefinition(
        appName = AppNameEnum.YD_SEARCH,
        domainName = DomainNameEnum.SEARCH
)
@Getter
public class RetryDeleteJobEvent extends AbstractCommonsDomainEvent implements Serializable {


    @Builder
    public RetryDeleteJobEvent(@NonNull Long identifier, Map<String, Object> context) {
        super(identifier, context);
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
