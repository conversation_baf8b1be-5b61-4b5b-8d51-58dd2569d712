package com.xk.search.domain.event.logistics;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.order.enums.logistics.LogisticsOrderTypeEnum;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_SEARCH, domainName = DomainNameEnum.SEARCH)
public class LogisticsOrderExpiredDeleteJobEvent extends AbstractCommonsDomainEvent
        implements Serializable {

    private final String indexName;

    private final SearchIndexTypeEnum searchIndexTypeEnum;

    private final SearchBizTypeEnum searchBizTypeEnum;

    private final LogisticsOrderTypeEnum logisticsOrderTypeEnum;

    @Builder
    public LogisticsOrderExpiredDeleteJobEvent(@NonNull Long identifier,
            Map<String, Object> context, String indexName, SearchIndexTypeEnum searchIndexTypeEnum,
            SearchBizTypeEnum searchBizTypeEnum, LogisticsOrderTypeEnum logisticsOrderTypeEnum) {
        super(identifier, context);
        this.indexName = indexName;
        this.searchIndexTypeEnum = searchIndexTypeEnum;
        this.searchBizTypeEnum = searchBizTypeEnum;
        this.logisticsOrderTypeEnum = logisticsOrderTypeEnum;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
