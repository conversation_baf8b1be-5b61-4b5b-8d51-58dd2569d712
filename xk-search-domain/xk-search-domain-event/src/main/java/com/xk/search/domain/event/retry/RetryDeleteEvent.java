package com.xk.search.domain.event.retry;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;

import com.xk.search.enums.retry.RetryBizTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@EventDefinition(
        appName = AppNameEnum.YD_SEARCH,
        domainName = DomainNameEnum.SEARCH
)
@Getter
public class RetryDeleteEvent extends AbstractCommonsDomainEvent implements Serializable {

    private Integer retryCount;
    private Integer channelType;
    private Long id;
    private RetryBizTypeEnum bizType;

    @Builder
    public RetryDeleteEvent(@NonNull Long identifier, Map<String, Object> context, Long id, Integer retryCount, Integer channelType, RetryBizTypeEnum bizType) {
        super(identifier, context);
        this.id = id;
        this.bizType = bizType;
        this.retryCount = retryCount;
        this.channelType = channelType;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
