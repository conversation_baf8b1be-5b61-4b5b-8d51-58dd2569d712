package com.xk.search.domain.event.order;

import java.io.Serializable;
import java.util.Map;

import com.myco.mydata.event.annotation.EventDefinition;
import com.myco.mydata.event.definition.AbstractCommonsDomainEvent;
import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.meta.AppNameEnum;
import com.myco.mydata.event.meta.DomainNameEnum;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.search.enums.search.SearchBizTypeEnum;
import com.xk.search.enums.search.SearchIndexTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@EventDefinition(appName = AppNameEnum.YD_SEARCH, domainName = DomainNameEnum.SEARCH)
public class OrderExpiredDeleteJobEvent extends AbstractCommonsDomainEvent implements Serializable {

    private final String indexName;

    private final SearchIndexTypeEnum searchIndexTypeEnum;

    private final SearchBizTypeEnum searchBizTypeEnum;

    private final Integer productType;

    private final Integer orderStatus;

    @Builder
    public OrderExpiredDeleteJobEvent(@NonNull Long identifier, Map<String, Object> context,
            String indexName, SearchIndexTypeEnum searchIndexTypeEnum,
            SearchBizTypeEnum searchBizTypeEnum, Integer productType, Integer orderStatus) {
        super(identifier, context);
        this.indexName = indexName;
        this.searchIndexTypeEnum = searchIndexTypeEnum;
        this.searchBizTypeEnum = searchBizTypeEnum;
        this.productType = productType;
        this.orderStatus = orderStatus;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
