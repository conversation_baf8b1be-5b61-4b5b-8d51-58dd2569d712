package com.xk.goods.infrastructure.data.persistence.serial;

import java.util.List;

import com.myco.framework.cache.annotations.GroupStrategy;
import com.myco.framework.sharding.annotation.Table;
import com.myco.framework.support.mybatis.Pagination;
import com.myco.mydata.infrastructure.data.annotation.Repository;
import com.xk.goods.domain.model.serial.entity.SerialOriginalItemEntity;
import com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem;

/**
 * <AUTHOR>
 * @description 针对表【g_serial_original_item(原始卡密组条目表)】的数据库操作Mapper
 * @createDate 2025-05-16 15:23:55
 * @Entity com.xk.goods.infrastructure.data.po.serial.GSerialOriginalItem
 */
@Repository
@Table("g_serial_original_item")
public interface GSerialOriginalItemMapper {

    int deleteByPrimaryKey(GSerialOriginalItem record);

    int insert(GSerialOriginalItem record);

    int insertSelective(GSerialOriginalItem record);

    GSerialOriginalItem selectByPrimaryKey(GSerialOriginalItem record);

    int updateByPrimaryKeySelective(GSerialOriginalItem record);

    int updateByPrimaryKey(GSerialOriginalItem record);

    List<GSerialOriginalItem> searchByKeywords(Pagination pagination);

    // @GroupStrategy("serialGroupId")
    List<GSerialOriginalItem> selectBySerialGroupId(GSerialOriginalItem record);

    int deleteBySerialGroupId(Long serialGroupId);

    int countOriginalItem(Long serialGroupId);

    int countDistinctSerialGroupTeam(Long serialGroupId);

    List<SerialOriginalItemEntity> searchOriginalByTeamMemberId(GSerialOriginalItem record);
}
