package com.xk.goods.enums.goods;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum LiveStatusEnum {
    WAIT_LIVE(10, "待直播"),
    LIVE(30, "直播中"),
    LIVE_SUCCESS(20, "直播完成"),
    ;
    private static final Map<Integer, LiveStatusEnum> MAP;

    static {
        MAP = Arrays.stream(LiveStatusEnum.values())
                .collect(Collectors.toMap(LiveStatusEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static LiveStatusEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
