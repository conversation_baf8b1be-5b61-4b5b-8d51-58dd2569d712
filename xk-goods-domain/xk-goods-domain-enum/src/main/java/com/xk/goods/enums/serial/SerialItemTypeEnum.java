package com.xk.goods.enums.serial;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@RequiredArgsConstructor
public enum SerialItemTypeEnum {
    ORIGINAL(1, "原盒卡密条目"),
    SPECIAL(2, "自定义卡密条目"),
    TEAM(3, "卡密队伍"),
    ;
    private static final Map<Integer, SerialItemTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SerialItemTypeEnum.values())
                .collect(Collectors.toMap(SerialItemTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static SerialItemTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
