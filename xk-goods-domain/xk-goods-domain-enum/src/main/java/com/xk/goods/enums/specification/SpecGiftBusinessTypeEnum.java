package com.xk.goods.enums.specification;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SpecGiftBusinessTypeEnum {

    FORTUNE_BOX(1,"福盒"),
    PICK(2, "选队"),
    NONE_PICK(3, "非选队"),
    ;

    private static final Map<Integer, SpecGiftBusinessTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SpecGiftBusinessTypeEnum.values())
                .collect(Collectors.toMap(SpecGiftBusinessTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static SpecGiftBusinessTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }

}
