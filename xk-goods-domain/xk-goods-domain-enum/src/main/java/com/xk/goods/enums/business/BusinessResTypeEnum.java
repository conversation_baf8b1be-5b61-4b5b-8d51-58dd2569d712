package com.xk.goods.enums.business;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BusinessResTypeEnum {

    MEMBER_PICTURE(1, "球员图片"),
    MEMBER_AVATAR(2, "球员头像"),
    SERIES_PICTURE(3, "选队图片"),
    PRODUCT_PICTURE(4, "商品图片"),
    PRODUCT_DETAIL(5,"商品详情"),
    PRODUCT_INTRODUCTION(6, "商品介绍"),
    PRODUCT_SOURCE(7,"商品来源"),
    PRODUCT_VIDEO(8,"商品视频"),
    GIFT_PICTURE(9,"赠品图片"),
    SERIAL_PICTURE(10,"卡密图片"),
    GIFT_REPORT_PICTURE(11,"赠品报告"),
    SERIAL_ITEM_COLOR(12,"拆卡卡背"),
    SERIES_GIFT_PICTURE(13, "赠品图片"),
    SERIAL_ITEM_BACKGROUND(14,"拆卡背景"),
    ;

    private static final Map<Integer, BusinessResTypeEnum> MAP;

    static {
        MAP = Arrays.stream(BusinessResTypeEnum.values())
                .collect(Collectors.toMap(BusinessResTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String msg;

    public static BusinessResTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
