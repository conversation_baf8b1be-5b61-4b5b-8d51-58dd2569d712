package com.xk.goods.domain.model.serial.entity;

import java.util.Date;

import com.myco.mydata.domain.model.Entity;
import com.myco.mydata.domain.model.Validatable;
import com.myco.mydata.domain.model.exception.wrapper.ExceptionWrapperThrowable;
import com.xk.goods.domain.model.serialitem.id.SerialItemIdentifier;

import lombok.*;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SerialTeamItemEntity implements Entity<SerialItemIdentifier> {
    /**
     * 卡密条目id
     */
    private Long serialItemId;

    /**
     * 卡密组id
     */
    private Long serialGroupId;

    /**
     * 球队名称
     */
    private String teamName;

    /**
     * 球队类型
     */
    private Integer teamType;

    /**
     * 特效为：TEAM
     */
    private String color;

    /**
     * 球员id
     */
    private Long teamMemberId;

    /**
     * 球员图片
     */
    private String memberPicAddr;

    /**
     * 球员头像
     */
    private String memberAvatarAddr;

    /**
     * 创建时间
     */
    private Date createTime;

    @Override
    public @NonNull SerialItemIdentifier getIdentifier() {
        return SerialItemIdentifier.builder().serialItemId(serialItemId).build();
    }

    @Override
    public Validatable<SerialItemIdentifier> validate() throws ExceptionWrapperThrowable {
        return this;
    }
}
