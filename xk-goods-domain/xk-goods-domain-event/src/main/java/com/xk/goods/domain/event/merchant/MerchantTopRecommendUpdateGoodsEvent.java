package com.xk.goods.domain.event.merchant;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class MerchantTopRecommendUpdateGoodsEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;

    private final Integer blockType;

    private final Integer topRecommendScore;

    @Builder
    protected MerchantTopRecommendUpdateGoodsEvent(@NonNull Long identifier,
            Map<String, Object> context, Long goodsId, Integer blockType, Integer hasTopRecommend,
            Integer topRecommendScore) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.blockType = blockType;
        this.topRecommendScore = topRecommendScore;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
