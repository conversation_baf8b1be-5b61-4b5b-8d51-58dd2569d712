package com.xk.goods.domain.event.merchant;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class MerchantProductReportEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;
    private final List<Long> serialItemIdList;

    @Builder
    public MerchantProductReportEvent(@NonNull Long identifier, Map<String, Object> context,
            Long goodsId, List<Long> serialItemIdList) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.serialItemIdList = serialItemIdList;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
