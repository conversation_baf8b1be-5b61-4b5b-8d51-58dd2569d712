package com.xk.goods.domain.event.merchant;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class MerchantProductDownEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;
    private final Integer retryCount;

    @Builder
    public MerchantProductDownEvent(@NonNull Long identifier, Map<String, Object> context,
            Long goodsId, Integer retryCount) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.retryCount = retryCount;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).goodsId(this.goodsId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
