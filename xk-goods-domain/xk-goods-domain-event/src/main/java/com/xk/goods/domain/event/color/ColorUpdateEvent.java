
package com.xk.goods.domain.event.color;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.domain.event.business.BaseBusinessRes;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class ColorUpdateEvent extends AbstractGoodsDomainEvent implements Serializable {

    private final Long serialItemColorId;
    private final List<BaseBusinessRes> baseBusinessResList;

    @Builder
    public ColorUpdateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long serialItemColorId, List<BaseBusinessRes> baseBusinessResList) {
        super(identifier, context);
        this.serialItemColorId = serialItemColorId;
        this.baseBusinessResList = baseBusinessResList;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
