package com.xk.goods.domain.event.team;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.domain.event.business.BaseBusinessRes;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;

@Getter
@ToString
public class CreateTeamMemberEvent extends AbstractGoodsDomainEvent {

    /**
     * 队员图片资源映射
     */
    private final List<BaseBusinessRes> baseBusinessResList;

    @Builder
    public CreateTeamMemberEvent(@NonNull Long identifier, Map<String, Object> context,
            List<BaseBusinessRes> baseBusinessResList) {
        super(identifier, context);
        this.baseBusinessResList = baseBusinessResList;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).baseBusinessResList(baseBusinessResList).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
