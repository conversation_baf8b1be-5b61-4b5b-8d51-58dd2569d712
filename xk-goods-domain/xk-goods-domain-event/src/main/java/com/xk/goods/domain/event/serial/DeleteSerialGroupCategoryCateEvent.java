package com.xk.goods.domain.event.serial;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import lombok.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public class DeleteSerialGroupCategoryCateEvent extends AbstractGoodsDomainEvent {
    private final Long parentId;

    private final String categoryName;

    private final Integer sort;

    private final Integer status;

    private final Long serialGroupCategoryId;

    private final String groupBusinessType;

    private final Long groupId;

    @Builder
    public DeleteSerialGroupCategoryCateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long parentId, Long serialGroupCategoryId, String categoryName, Integer sort, Integer status,
            String groupBusinessType, Long groupId) {
        super(identifier, context);
        this.parentId = parentId;
        this.serialGroupCategoryId = serialGroupCategoryId;
        this.categoryName = categoryName;
        this.sort = sort;
        this.status = status;
        this.groupBusinessType = groupBusinessType;
        this.groupId = groupId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
