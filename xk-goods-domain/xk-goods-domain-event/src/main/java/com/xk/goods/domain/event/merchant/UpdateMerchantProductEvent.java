package com.xk.goods.domain.event.merchant;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class UpdateMerchantProductEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;

    @Builder
    public UpdateMerchantProductEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId) {
        super(identifier, context);
        this.goodsId = goodsId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
