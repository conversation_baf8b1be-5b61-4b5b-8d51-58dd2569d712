package com.xk.goods.domain.event.score;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.enums.score.ScoreRuleTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class QueryScoreEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;
    private final ProductTypeEnum productType;
    private final ScoreRuleTypeEnum scoreRuleType;
    private final Long ruleVersion;

    @Builder
    public QueryScoreEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId,
                           ProductTypeEnum productType, ScoreRuleTypeEnum scoreRuleType,
                           Long ruleVersion) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.productType = productType;
        this.scoreRuleType = scoreRuleType;
        this.ruleVersion = ruleVersion;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
