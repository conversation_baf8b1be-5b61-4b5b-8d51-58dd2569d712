package com.xk.goods.domain.event.goods;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class DeleteGoodsCateEvent extends AbstractGoodsDomainEvent {

    private final Long goodsCategoryId;

    private final String groupBusinessType;

    private final Long groupId;

    @Builder
    public DeleteGoodsCateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long goodsCategoryId, String groupBusinessType, Long groupId) {
        super(identifier, context);
        this.goodsCategoryId = goodsCategoryId;
        this.groupBusinessType = groupBusinessType;
        this.groupId = groupId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).goodsCategoryId(this.goodsCategoryId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
