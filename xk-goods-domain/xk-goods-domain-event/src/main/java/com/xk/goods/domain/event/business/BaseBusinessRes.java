package com.xk.goods.domain.event.business;

import java.io.Serializable;

import com.xk.goods.enums.business.BusinessGroupTypeEnum;
import com.xk.goods.enums.business.BusinessResTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseBusinessRes implements Serializable {

    /**
     * 队员图片资源映射
     */
    private BusinessResTypeEnum businessResType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 资源id
     */
    private Integer resId;

    /**
     * 业务分组类型
     */
    private BusinessGroupTypeEnum businessGroupType;

}
