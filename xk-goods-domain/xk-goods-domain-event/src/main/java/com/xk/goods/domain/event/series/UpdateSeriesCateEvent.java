package com.xk.goods.domain.event.series;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.domain.event.business.BaseBusinessRes;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class UpdateSeriesCateEvent extends AbstractGoodsDomainEvent {

    private final String categoryName;

    private final Integer sort;

    private final Integer status;

    private final Long seriesCategoryId;

    private final String groupBusinessType;

    private final Long groupId;

    private final List<BaseBusinessRes> baseBusinessResList;

    @Builder
    public UpdateSeriesCateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long seriesCategoryId, String categoryName, Integer sort, Integer status,
            String groupBusinessType, Long groupId, List<BaseBusinessRes> baseBusinessResList) {
        super(identifier, context);
        this.categoryName = categoryName;
        this.sort = sort;
        this.status = status;
        this.groupBusinessType = groupBusinessType;
        this.seriesCategoryId = seriesCategoryId;
        this.groupId = groupId;
        this.baseBusinessResList = baseBusinessResList;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).seriesCategoryId(this.seriesCategoryId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
