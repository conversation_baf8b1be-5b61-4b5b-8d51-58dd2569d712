package com.xk.goods.domain.event.score;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.enums.merchant.ProductTypeEnum;
import com.xk.goods.enums.score.ScoreRuleTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class UpdateScoreRuleEvent extends AbstractGoodsDomainEvent {

    private final ScoreRuleTypeEnum scoreRuleType;
    private final Long ruleVersion;

    @Builder
    public UpdateScoreRuleEvent(@NonNull Long identifier, Map<String, Object> context,
            ScoreRuleTypeEnum scoreRuleType, Long ruleVersion) {
        super(identifier, context);
        this.scoreRuleType = scoreRuleType;
        this.ruleVersion = ruleVersion;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

    public ProductTypeEnum getProductType() {
        return switch (scoreRuleType) {
            case FORTUNE_BOX -> ProductTypeEnum.FORTUNE_BOX;
            case EDGE_BOX -> ProductTypeEnum.EDGE_BOX;
            case RUBBED_CARD_PACK -> ProductTypeEnum.RUBBED_CARD_PACK;
            case ORIGINAL_BOX -> ProductTypeEnum.ORIGINAL_BOX;
            default -> null;
        };
    }
}
