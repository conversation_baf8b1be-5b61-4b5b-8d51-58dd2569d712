package com.xk.goods.domain.event.merchant;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class MerchantProductDownJobEvent extends AbstractGoodsDomainEvent {

    private final String timeFormat;

    @Builder
    public MerchantProductDownJobEvent(@NonNull Long identifier, Map<String, Object> context,
            String timeFormat) {
        super(identifier, context);
        this.timeFormat = timeFormat;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).timeFormat(this.timeFormat).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }

}
