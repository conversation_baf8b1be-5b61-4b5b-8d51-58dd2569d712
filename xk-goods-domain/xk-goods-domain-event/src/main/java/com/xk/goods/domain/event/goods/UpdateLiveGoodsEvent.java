package com.xk.goods.domain.event.goods;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.enums.goods.LiveStatusEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class UpdateLiveGoodsEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;
    private final LiveStatusEnum liveStatusEnum;

    @Builder
    public UpdateLiveGoodsEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId,
            LiveStatusEnum liveStatusEnum) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.liveStatusEnum = liveStatusEnum;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
