package com.xk.goods.domain.event.team;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;

@Getter
@ToString
public class DeleteTeamMemberEvent extends AbstractGoodsDomainEvent {

    /**
     * 业务id
     */
    private final Long teamMemberId;

    @Builder
    public DeleteTeamMemberEvent(@NonNull Long identifier, Map<String, Object> context,
            Long teamMemberId) {
        super(identifier, context);
        this.teamMemberId = teamMemberId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
