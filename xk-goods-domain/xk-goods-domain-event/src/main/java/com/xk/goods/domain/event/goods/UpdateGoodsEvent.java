package com.xk.goods.domain.event.goods;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.domain.event.business.BaseBusinessRes;
import com.xk.goods.enums.goods.GoodsTypeEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class UpdateGoodsEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;
    private final GoodsTypeEnum goodsTypeEnum;
    private final List<BaseBusinessRes> baseBusinessResList;

    @Builder
    public UpdateGoodsEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId,
                            GoodsTypeEnum goodsTypeEnum,
                            List<BaseBusinessRes> baseBusinessResList) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.goodsTypeEnum = goodsTypeEnum;
        this.baseBusinessResList = baseBusinessResList;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
