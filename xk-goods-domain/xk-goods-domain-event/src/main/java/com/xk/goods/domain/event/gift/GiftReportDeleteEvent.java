package com.xk.goods.domain.event.gift;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class GiftReportDeleteEvent extends AbstractGoodsDomainEvent {

    private final Long giftReportId;
    private final Long goodsId;
    private final Long serialItemId;

    @Builder
    public GiftReportDeleteEvent(@NonNull Long identifier, Map<String, Object> context,
            Long giftReportId, Long goodsId, Long serialItemId) {
        super(identifier, context);
        this.giftReportId = giftReportId;
        this.goodsId = goodsId;
        this.serialItemId = serialItemId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
