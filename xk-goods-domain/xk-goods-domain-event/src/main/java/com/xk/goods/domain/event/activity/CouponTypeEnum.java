package com.xk.goods.domain.event.activity;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum CouponTypeEnum {
    NO_THRESHOLD(1, "无门槛优惠券"),
    THRESHOLD(2, "有门槛优惠券"),
    ;
    private static final Map<Integer, CouponTypeEnum> MAP;

    static {
        MAP = Arrays.stream(CouponTypeEnum.values())
                .collect(Collectors.toMap(CouponTypeEnum::getCode, enumValue -> enumValue));
    }

    private final Integer code;
    private final String desc;

    public static CouponTypeEnum getByCode(Integer code) {
        return MAP.get(code);
    }
}
