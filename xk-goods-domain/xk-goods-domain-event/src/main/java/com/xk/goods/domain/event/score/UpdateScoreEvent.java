package com.xk.goods.domain.event.score;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class UpdateScoreEvent extends AbstractGoodsDomainEvent {

    private final Long goodsId;

    private final Long score;

    @Builder
    protected UpdateScoreEvent(@NonNull Long identifier, Map<String, Object> context, Long goodsId,
            Long score) {
        super(identifier, context);
        this.goodsId = goodsId;
        this.score = score;
    }


    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
