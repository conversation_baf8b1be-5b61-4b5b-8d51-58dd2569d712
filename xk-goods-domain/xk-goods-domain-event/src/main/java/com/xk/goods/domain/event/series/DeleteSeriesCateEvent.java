package com.xk.goods.domain.event.series;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class DeleteSeriesCateEvent extends AbstractGoodsDomainEvent {

    private final Long seriesCategoryId;

    private final String groupBusinessType;

    private final Long groupId;

    @Builder
    public DeleteSeriesCateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long seriesCategoryId, String groupBusinessType, Long groupId) {
        super(identifier, context);
        this.seriesCategoryId = seriesCategoryId;
        this.groupBusinessType = groupBusinessType;
        this.groupId = groupId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).seriesCategoryId(this.seriesCategoryId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
