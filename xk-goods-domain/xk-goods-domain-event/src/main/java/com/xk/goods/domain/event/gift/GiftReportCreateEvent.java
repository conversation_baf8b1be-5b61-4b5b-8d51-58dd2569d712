package com.xk.goods.domain.event.gift;

import java.util.List;
import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;
import com.xk.goods.domain.event.business.BaseBusinessRes;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
public class GiftReportCreateEvent extends AbstractGoodsDomainEvent {

    private final Long giftReportId;
    private final Long goodsId;
    private final List<BaseBusinessRes> baseBusinessResList;

    @Builder
    public GiftReportCreateEvent(@NonNull Long identifier, Map<String, Object> context,
            Long giftReportId, Long goodsId, List<BaseBusinessRes> baseBusinessResList) {
        super(identifier, context);
        this.giftReportId = giftReportId;
        this.goodsId = goodsId;
        this.baseBusinessResList = baseBusinessResList;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return this;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
