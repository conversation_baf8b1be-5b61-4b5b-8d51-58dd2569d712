package com.xk.goods.domain.event.goods;

import java.util.Map;

import com.myco.mydata.event.definition.CommonsDomainEvent;
import com.myco.mydata.event.support.EventValidateException;
import com.xk.domain.event.base.AbstractGoodsDomainEvent;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
@Getter
public class CancelGoodsCollectEvent extends AbstractGoodsDomainEvent {

    private final String goodsId;


    @Builder
    public CancelGoodsCollectEvent(@NonNull Long identifier, Map<String, Object> context,
            String goodsId) {
        super(identifier, context);
        this.goodsId = goodsId;
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> toImmutable() {
        return builder().context(this.getContext()).goodsId(this.goodsId).build();
    }

    @Override
    public @NonNull CommonsDomainEvent<Long> validate() throws EventValidateException {
        return this;
    }
}
