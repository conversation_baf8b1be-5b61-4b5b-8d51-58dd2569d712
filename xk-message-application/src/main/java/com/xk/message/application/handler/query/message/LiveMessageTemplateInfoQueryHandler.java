package com.xk.message.application.handler.query.message;


import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.LiveMessageTemplateInfoQuery;
import com.xk.message.application.dto.message.LiveMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.LiveMessageTemplateRspDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class LiveMessageTemplateInfoQueryHandler
        implements IActionQueryHandler<LiveMessageTemplateInfoQuery, LiveMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<LiveMessageTemplateRspDto> execute(Mono<LiveMessageTemplateInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getTemplateId()).build()
                , messageTemplateRootQueryRepository::findLiveMessageTemplateById, LiveMessageTemplateRspDto.class,
                (data, clazz) -> {
                    LiveMessageTemplateAppDto appDto = this.converter.convert(data,
                            LiveMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
