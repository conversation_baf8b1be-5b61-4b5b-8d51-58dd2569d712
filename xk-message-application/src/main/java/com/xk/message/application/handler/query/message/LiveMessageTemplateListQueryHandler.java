package com.xk.message.application.handler.query.message;


import com.myco.mydata.application.handler.query.many.IActionQueryManyHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.LiveMessageTemplateByBusinessTemplateIdQuery;
import com.xk.message.application.dto.message.LiveMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.LiveMessageTemplateRspDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class LiveMessageTemplateListQueryHandler implements
        IActionQueryManyHandler<LiveMessageTemplateByBusinessTemplateIdQuery, LiveMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Flux<LiveMessageTemplateRspDto> execute(Mono<LiveMessageTemplateByBusinessTemplateIdQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getBusinessMessageTemplateId()).build()
                , messageTemplateRootQueryRepository::searchLiveMessageTemplateByRootId,
                LiveMessageTemplateRspDto.class, (data, clazz) -> {
                    LiveMessageTemplateAppDto appDto = this.converter.convert(data,
                            LiveMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
