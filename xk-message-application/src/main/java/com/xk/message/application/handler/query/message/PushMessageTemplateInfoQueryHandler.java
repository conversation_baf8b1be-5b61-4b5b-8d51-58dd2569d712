package com.xk.message.application.handler.query.message;


import com.myco.mydata.application.handler.query.IActionQueryHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.query.template.PushMessageTemplateInfoQuery;
import com.xk.message.application.dto.message.PushMessageTemplateAppDto;
import com.xk.message.domain.repository.template.MessageTemplateRootQueryRepository;
import com.xk.message.interfaces.dto.rsp.template.PushMessageTemplateRspDto;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class PushMessageTemplateInfoQueryHandler
        implements IActionQueryHandler<PushMessageTemplateInfoQuery, PushMessageTemplateRspDto> {

    private final MessageTemplateRootQueryRepository messageTemplateRootQueryRepository;
    private final Converter converter;

    @Override
    public Mono<PushMessageTemplateRspDto> execute(Mono<PushMessageTemplateInfoQuery> query) {
        return execute(query, LongIdentifier.class,
                (data, clazz) -> LongIdentifier.builder().id(data.getTemplateId()).build()
                , messageTemplateRootQueryRepository::findPushMessageTemplateById, PushMessageTemplateRspDto.class,
                (data, clazz) -> {
                    PushMessageTemplateAppDto appDto = this.converter.convert(data,
                            PushMessageTemplateAppDto.class);
                    return this.converter.convert(appDto, clazz);
                });
    }
}
