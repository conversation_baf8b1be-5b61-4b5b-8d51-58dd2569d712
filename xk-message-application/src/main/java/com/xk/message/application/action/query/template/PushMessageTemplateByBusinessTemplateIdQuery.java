package com.xk.message.application.action.query.template;

import com.myco.mydata.application.handler.query.many.IActionQueryMany;
import com.xk.message.interfaces.dto.req.template.BusinessMessageTemplateIdentifierReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@AutoMappers({
        @AutoMapper(target = BusinessMessageTemplateIdentifierReqDto.class)
})
public class PushMessageTemplateByBusinessTemplateIdQuery implements IActionQueryMany {


    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

}
