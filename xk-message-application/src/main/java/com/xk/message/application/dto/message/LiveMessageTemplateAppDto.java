package com.xk.message.application.dto.message;

import com.myco.mydata.commons.constant.PlatformTypeEnum;
import com.xk.message.domain.model.template.entity.LiveMessageTemplateEntity;
import com.xk.message.enums.message.BusinessTypeEnum;
import com.xk.message.interfaces.dto.rsp.template.LiveMessageTemplateRspDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AutoMappers({
        @AutoMapper(target = LiveMessageTemplateEntity.class, uses = {BusinessTypeEnum.class, PlatformTypeEnum.class}),
        @AutoMapper(target = LiveMessageTemplateRspDto.class)
})
public class LiveMessageTemplateAppDto {
    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务类型
     */
    @ReverseAutoMapping(targetClass = LiveMessageTemplateEntity.class,
            expression = "java(source.getBusinessType() == null ? null : source.getBusinessType().getValue())"
    )
    private Integer businessType;

    /**
     * 平台类型
     */
    @ReverseAutoMapping(targetClass = LiveMessageTemplateEntity.class,
            expression = "java(source.getPlatformType() == null ? null : source.getPlatformType().getValue())"
    )
    private Integer platformType;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

}