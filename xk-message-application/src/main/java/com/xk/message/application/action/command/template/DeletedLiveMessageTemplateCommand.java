package com.xk.message.application.action.command.template;

import com.myco.mydata.application.handler.command.AbstractActionCommand;
import com.xk.message.domain.model.template.entity.LiveMessageTemplateEntity;
import com.xk.message.interfaces.dto.req.template.DeletedLiveMessageTemplateReqDto;
import com.xk.message.interfaces.dto.req.template.DeletedShortMessageTemplateReqDto;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.*;

/**
 * @author: killer
 **/
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMappers({
        @AutoMapper(target = DeletedLiveMessageTemplateReqDto.class),
        @AutoMapper(target = LiveMessageTemplateEntity.class)
})
public class DeletedLiveMessageTemplateCommand extends AbstractActionCommand {

    /**
     * 模板id
     */
    private Long templateId;


    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

}
