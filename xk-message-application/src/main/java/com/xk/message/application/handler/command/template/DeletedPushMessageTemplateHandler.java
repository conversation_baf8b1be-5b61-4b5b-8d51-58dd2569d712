package com.xk.message.application.handler.command.template;

import com.myco.mydata.application.handler.command.IActionCommandHandler;
import com.myco.mydata.domain.model.LongIdentifier;
import com.xk.message.application.action.command.template.DeletedPushMessageTemplateCommand;
import com.xk.message.application.action.command.template.DeletedStationMessageTemplateCommand;
import com.xk.message.domain.model.template.MessageTemplateRoot;
import com.xk.message.domain.model.template.entity.PushMessageTemplateEntity;
import com.xk.message.domain.model.template.entity.StationMessageTemplateEntity;
import com.xk.message.domain.repository.template.MessageTemplateRootRepository;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: killer
 **/
@Component
@RequiredArgsConstructor
public class DeletedPushMessageTemplateHandler
        implements IActionCommandHandler<DeletedPushMessageTemplateCommand, Void> {

    private final MessageTemplateRootRepository messageTemplateRootRepository;

    private final Converter converter;

    @Override
    public Mono<Void> execute(Mono<DeletedPushMessageTemplateCommand> commandMono) {
        return execute(commandMono, PushMessageTemplateEntity.class, converter::convert,
                entity -> MessageTemplateRoot.builder()
                        .identifier(LongIdentifier.builder().id(-1L).build())
                        .pushMessageTemplateEntityList(List.of(entity))
                        .build(), messageTemplateRootRepository::deletedPushMessageTemplate);
    }
}
