package com.xk.message.application.action.query.template;

import com.myco.mydata.application.handler.query.IActionQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PushMessageTemplateInfoQuery implements IActionQuery {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板id
     */
    private Long businessMessageTemplateId;

}
